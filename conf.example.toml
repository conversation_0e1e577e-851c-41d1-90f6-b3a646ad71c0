[System]
RunMode = "develop" # System run mode, develop|test|product
LogOut = ["stdout", "app.log"] # Log output destination, stdout|filename(a.log,b.log and so on)\
BatchSize = 100

[Redis]
Host = "127.0.0.1:6379"
Password = ""
Database = 6
TaskQueue = "ScanTask"
TaskQueueClass = "ScanDetection" # It has some meaning as the topic.

[MySQL]
Host = "127.0.0.1:3306"
Username = "root"
Password = "root"
Database = "task.flow.engine.of.assetsync"
MaxIdleConnections = 200
MaxOpenConnections = 100
MaxConnectionLifeTime = "10s"
LogLevel = 2 # GORM log level, 1: silent, 2:error, 3:warn, 4:info
DriverName = "mysql"
AutoMigrate = false

[Elastic]
Host = "http://127.0.0.1:9200"
AutoMigrate = false