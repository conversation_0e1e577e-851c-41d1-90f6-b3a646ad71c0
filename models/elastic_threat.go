package models

import (
	"strconv"
	"sync"
	"time"

	expressionc "git.gobies.org/foeye/foeye-engine-syncasset/module/expression"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/timesun"

	expression "git.gobies.org/foeye-dependencies/expression"
	"git.gobies.org/foeye-dependencies/httpclient"
	"git.gobies.org/foeye-dependencies/logger"
)

// ScanEngineOfGoscanner vulnerability engine of definition.
const (
	ScanEngineOfGoscanner = 4
)

const (
	HostnameTypeOfDomain = iota
	HostnameTypeOfIP
)

// ElasticSearch index of state related constants' definition.
const (
	ThreatStateOfPending       = iota + 1 // 未处理
	ThreatStateOffline                    // 离线
	ThreatStateOfIgnored                  // 已忽略
	ThreatStateOfFixed                    // 已修复
	ThreatStateOfReported                 // 已提交
	ThreatStateOfSystemIgnored            // 系统忽略
	ThreatStateOfSystemFixed              // 系统判断修复
)

var threats sync.RWMutex

//func NewDocumentId(hostinfo, filename string) (string, error) {
//	if hostinfo == "" || filename == "" {
//		return "", fmt.Errorf("hostinfo or file can't empty")
//	}
//	return secret.EncodeMD5(fmt.Sprintf("%s_%s", hostinfo, filename)), nil
//}

// ThreatDocument elastic threat document structure.
type ThreatDocument struct {
	Hosts             interface{}         `json:"hosts"`
	IP                string              `json:"ip"`
	Port              int                 `json:"port"`
	Name              string              `json:"name"`
	CommonTitle       string              `json:"common_title"`
	Mac               string              `json:"mac"`
	NetBios           interface{}         `json:"net_bios"`
	AddWay            string              `json:"add_way"`
	BusinessApp       interface{}         `json:"business_app"`
	Province          interface{}         `json:"province"`
	City              interface{}         `json:"city"`
	Company           interface{}         `json:"company"`
	Username          interface{}         `json:"username"`
	BelongUserID      int                 `json:"belong_user_id"`
	ManagerMobile     interface{}         `json:"manager_mobile"`
	ManagerEmail      interface{}         `json:"manager_email"`
	ComputerRoom      interface{}         `json:"computer_room"`
	Descriptions      interface{}         `json:"descriptions"`
	Country           interface{}         `json:"country"`
	Operator          interface{}         `json:"operator"`
	OperatingCompany  interface{}         `json:"operating_company"`
	RuleTags          []string            `json:"rule_tags"`
	CatTags           []string            `json:"cat_tags"`
	CompanyTags       []string            `json:"company_tags"`
	TaskIds           []int               `json:"task_ids"`
	State             int                 `json:"state"` // 漏洞状态().
	Level             int                 `json:"level"`
	Hostinfo          string              `json:"hostinfo"`
	Vulfile           string              `json:"vulfile"`
	URL               string              `json:"vulURL"`
	ObjType           int                 `json:"obj_type"` // IP: 1 DOMAIN: 0
	Object            string              `json:"object"`
	IntranetIP        int                 `json:"intranet_ip"`
	Addition          interface{}         `json:"addition"`
	MergeMd5          string              `json:"merge_md5"`
	ScanEngine        int                 `json:"scan_engine"`
	PortList          []AssetPortListItem `json:"port_list"`
	RuleInfos         []AssetRuleInfoItem `json:"rule_infos"`
	CommonDescription string              `json:"common_description"`
	CommonImpact      string              `json:"common_impact"`
	Recommandation    string              `json:"recommandation"`
	CustomFields      interface{}         `json:"custom_fields"`
	Gid               interface{}         `json:"gid"`
	Uploaded          int                 `json:"uploaded"`
	NoticeTime        interface{}         `json:"notice_time"`
	CveID             interface{}         `json:"cveId"`
	VulType           string              `json:"vulType"`
	HasExp            int                 `json:"has_exp"`
	IsIpv6            bool                `json:"is_ipv6"`
	LastResponse      string              `json:"last_response"`
	HasResponse       int                 `json:"has_response"`
	Createtime        string              `json:"createtime"`
	LastCheckTime     string              `json:"lastchecktime"`
	LastUpdateTime    string              `json:"lastupdatetime"`
}

// SetTaskId set task id.
func (doc *ThreatDocument) SetTaskId(taskId int) {
	threats.Lock()
	defer threats.Unlock()

	if doc.TaskIds == nil {
		doc.TaskIds = []int{0, taskId}
	} else {
		var exists bool
		for _, id := range doc.TaskIds {
			if taskId == id {
				exists = true
			}
		}
		if !exists {
			doc.TaskIds = append(doc.TaskIds, taskId)
		}
	}
}

// NewThreatDocument initialize elasticsearch threat document instance.
func NewThreatDocument(asset *AssetDocument, poc *Poc, scanned *Scanned) (*ThreatDocument, error) {
	fixedURL, err := httpclient.FixedURL(scanned.HostInfo)
	if err != nil {
		return nil, err
	}

	port := 0
	if fixedURL.Port() != "" {
		i, err := strconv.Atoi(fixedURL.Port())
		if err != nil {
			logger.Infow("create threat document data to failed", "err", err)
		}
		port = i
	}

	return &ThreatDocument{
		Hosts:             nil,
		IP:                asset.IP,
		Port:              port,
		Name:              poc.Name,
		CommonTitle:       scanned.Name,
		Mac:               asset.Mac,
		AddWay:            asset.AddWay,
		BusinessApp:       asset.BusinessApp,
		Province:          asset.Province,
		City:              asset.City,
		Company:           asset.Company,
		Username:          asset.Username,
		BelongUserID:      asset.BelongUserID,
		ManagerMobile:     asset.ManagerMobile,
		ManagerEmail:      asset.ManagerEmail,
		ComputerRoom:      asset.ComputerRoom,
		RuleTags:          asset.RuleTags,
		CatTags:           asset.CatTags,
		CompanyTags:       asset.CompanyTags,
		State:             ThreatStateOfPending,
		Vulfile:           scanned.FileName,
		Hostinfo:          scanned.HostInfo,
		URL:               vulURL(scanned),
		ObjType:           hostname(fixedURL.Hostname()),
		Object:            fixedURL.Hostname(),
		IntranetIP:        internal(fixedURL.Hostname()),
		ScanEngine:        ScanEngineOfGoscanner,
		PortList:          asset.PortList,
		RuleInfos:         asset.RuleInfos,
		CommonDescription: poc.Description,
		CommonImpact:      poc.Impact,
		Recommandation:    poc.Recommandation,
		CustomFields:      asset.CustomFields,
		NoticeTime:        nil,
		CveID:             poc.CveID,
		VulType:           poc.VulType,
		HasExp:            hasExp(poc),
		IsIpv6:            asset.IsIpv6,
		LastResponse:      lastResponse(scanned),
		HasResponse:       hasResponse(scanned),
		Createtime:        now(),
		LastCheckTime:     now(),
		LastUpdateTime:    now(),
	}, nil
}

// NewThreatDocumentWithSearchHitSource initialize elasticsearch threat document instance with byte slice.
//func NewThreatDocumentWithSearchHitSource(source *json.RawMessage) (*ThreatDocument, error) {
//	data, err := source.MarshalJSON()
//	if err != nil {
//		return nil, err
//	}
//
//	var r *ThreatDocument
//	if err := json.Unmarshal(data, &r); err != nil {
//		return nil, err
//	}
//
//	return r, nil
//}

func internal(ip string) int {
	if expressionc.IsInternalIP(ip) {
		return 1
	}
	return 0
}

func hostname(hostname string) int {
	if expression.IsIPv4(hostname) {
		return HostnameTypeOfIP
	}
	return HostnameTypeOfDomain
}

func hasExp(poc *Poc) int {
	if poc.HasExp {
		return 1
	}
	return 0
}

func vulURL(scanned *Scanned) string {
	if scanned.VulURL != nil {
		return scanned.VulURL.(string)
	}
	return ""
}

func lastResponse(scanner *Scanned) string {
	if scanner.LastResponse != nil {
		return scanner.LastResponse.(string)
	}
	return ""
}

func hasResponse(scanner *Scanned) int {
	response := lastResponse(scanner)
	if response != "" {
		return 1
	}
	return 0
}

func now() string {
	return time.Now().Format(timesun.TimeFormatOfChina24.String())
}
