package models

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/jsonfixed"
)

func TestElasticThreatSuite(t *testing.T) {
	suite.Run(t, &ElasticThreatSuite{})
}

type ElasticThreatSuite struct {
	suite.Suite
	err             error
	baseDir         string
	testTaskDataDir string
	asset           *AssetDocument
	scanned         *Scanned
	poc             *Poc
}

func (suite *ElasticThreatSuite) BeforeTest(suiteName, testName string) {
	suite.baseDir = "./../"
	suite.testTaskDataDir, suite.err = fsfire.GetFilePathWithFileSystemPath(
		suite.baseDir,
		fsfire.WithSpecificFileSystemPath("tmp/test/data/task"),
	)
	assert.NoError(suite.T(), suite.err)

	// Assign asset document data.
	suite.asset = suite.AssetDocument()
	suite.scanned = suite.ScannedResultHasThreat()
	suite.poc = suite.Poc()
}

func (suite *ElasticThreatSuite) AssetDocument() *AssetDocument {
	data, err := ioutil.ReadFile(filepath.Join(suite.testTaskDataDir, "create/elastic/doc/asset.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	var r *elastic.SearchHit
	err = json.Unmarshal(data, &r)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), r)

	data, err = r.Source.MarshalJSON()
	assert.NoError(suite.T(), err)

	var asset *AssetDocument
	err = json.Unmarshal(data, &asset)
	assert.NoError(suite.T(), err)

	return asset
}

func (suite *ElasticThreatSuite) Poc() *Poc {
	data, err := ioutil.ReadFile(filepath.Join(suite.testTaskDataDir, "create/elastic/doc/poc.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	time := jsonfixed.NewDestinationOfTime(jsonfixed.TimeFormatOfChina24, jsonfixed.TimeFormatOfRFC3339)
	template := jsonfixed.Template{
		"created_at":      time,
		"updated_at":      time,
		"begin_scan_time": time,
		"disclosure_date": jsonfixed.NewDestinationOfTime(jsonfixed.TimeFormatOfChinaDate, jsonfixed.TimeFormatOfRFC3339),
		"has_exp":         jsonfixed.NewDestinationOfBool(0), // Original should, can be specified original data type.
	}
	data, err = jsonfixed.Convert(data, template)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	var poc *Poc
	err = json.Unmarshal(data, &poc)
	assert.NoError(suite.T(), err)

	return poc
}

func (suite *ElasticThreatSuite) ScannedResultHasThreat() *Scanned {
	data, err := ioutil.ReadFile(filepath.Join(suite.testTaskDataDir, "create/elastic/doc/scanned.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	var scanned *Scanned
	err = json.Unmarshal(data, &scanned)
	assert.NoError(suite.T(), err)

	return scanned
}

func (suite *ElasticThreatSuite) Test_Threat_NewThreatDocument() {
	document, err := NewThreatDocument(suite.asset, suite.poc, suite.scanned)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), document)

	//mosso.DebugShowContentWithJSON(document)
}

func (suite *ElasticThreatSuite) Test_Threat_SetTaskId() {
	document, err := NewThreatDocument(suite.asset, suite.poc, suite.scanned)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), document)
	document.SetTaskId(1)
}

//func TestNewDocumentId(t *testing.T) {
//	scanner := &Scanned{FileName: "CoreOS_etcd_api_disclosure.json", HostInfo: "http://**********:2379"}
//	actual, err := NewDocumentId(scanner.HostInfo, scanner.FileName)
//
//	assert.NoError(t, err)
//	assert.Equal(t, "57de78fa1ccfd37198d1f5296f82f51d", actual)
//}
