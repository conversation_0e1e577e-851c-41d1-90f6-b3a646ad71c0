package models

import (
	"git.gobies.org/foeye-dependencies/interside"
)

const ElasticAssetAddWayOfFlow = "flow"

type MigrateToAsset struct {
	Updates []*AssetDocument
	Inserts []*AssetDocument
}

func (migrate *MigrateToAsset) AppendUpdates(assets ...*AssetDocument) *MigrateToAsset {
	if migrate.Updates == nil {
		migrate.Updates = make([]*AssetDocument, 0)
	}

	migrate.Updates = append(migrate.Updates, assets...)

	return migrate
}

func (migrate *MigrateToAsset) AppendInserts(assets ...*AssetDocument) *MigrateToAsset {
	if migrate.Inserts == nil {
		migrate.Inserts = make([]*AssetDocument, 0)
	}

	migrate.Inserts = append(migrate.Inserts, assets...)

	return migrate
}

type AssetPortListItem struct {
	Protocol     string      `json:"protocol"`
	Port         int         `json:"port"`
	Banner       string      `json:"banner"`
	Certs        interface{} `json:"certs"`
	IsHoneypot   interface{} `json:"is_honeypot"`
	HoneypotName string      `json:"honeypot_name"`
	Charset      string      `json:"charset"`
	<PERSON><PERSON><PERSON><PERSON>      bool        `json:"is_fraud"`
	FraudName    string      `json:"fraud_name"`
}

type AssetTitleListItem struct {
	Port  int    `json:"port"`
	Host  string `json:"host"`
	Title string `json:"title"`
}

type AssetRuleInfoItem struct {
	BelongLevel  int         `json:"belong_level"`
	RuleID       int         `json:"rule_id"`
	SecondCatTag string      `json:"second_cat_tag"`
	SoftHardCode int         `json:"soft_hard_code"`
	FirstCatTag  string      `json:"first_cat_tag"`
	LevelCode    int         `json:"level_code"`
	Company      string      `json:"company"`
	IsXc         interface{} `json:"is_xc"`
	Ports        []int       `json:"ports"`
	Title        string      `json:"title"`
}

type AssetFirstTagItem struct {
	HardwareNum int    `json:"hardware_num"`
	FirstCatTag string `json:"first_cat_tag"`
	Num         int    `json:"num"`
	SoftwareNum int    `json:"software_num"`
}

type AssetSecondTagItem struct {
	HardwareNum  int    `json:"hardware_num"`
	SecondCatTag string `json:"second_cat_tag"`
	Num          int    `json:"num"`
	SoftwareNum  int    `json:"software_num"`
}

type AssetDocument struct {
	TaskId         interface{}          `json:"task_id,omitempty"`
	Createtime     interface{}          `json:"createtime,omitempty"`
	LastUpdateTime string               `json:"lastupdatetime"`
	PortSize       int                  `json:"port_size"`
	PortList       []AssetPortListItem  `json:"port_list"`
	IP             string               `json:"ip"`
	IPBNet         string               `json:"ip_b_net"`
	IPCNet         string               `json:"ip_c_net"`
	IsIpv6         bool                 `json:"is_ipv6"`
	State          int                  `json:"state"`
	Ports          []string             `json:"ports"`
	Protocols      []string             `json:"protocols"`
	TitleList      []AssetTitleListItem `json:"title_list"`
	AddWay         string               `json:"add_way"`
	LastCheckTime  string               `json:"lastchecktime"`
	Name           string               `json:"name"`
	Host           interface{}          `json:"host"`
	AssetLevel     interface{}          `json:"asset_level"`
	BusinessApp    interface{}          `json:"business_app"`
	GroupName      interface{}          `json:"group_name"`
	ComputerRoom   interface{}          `json:"computer_room"`
	Country        interface{}          `json:"country"`
	Province       interface{}          `json:"province"`
	City           interface{}          `json:"city"`
	Company        interface{}          `json:"company"`
	BelongUserID   int                  `json:"belong_user_id"`
	Hosts          interface{}          `json:"hosts"`
	CustomFields   interface{}          `json:"custom_fields"`
	CustomNames    interface{}          `json:"custom_names"`
	Username       interface{}          `json:"username"`
	ManagerEmail   interface{}          `json:"manager_email"`
	ManagerMobile  interface{}          `json:"manager_mobile"`
	CatTags        []string             `json:"cat_tags"`
	RuleTags       []string             `json:"rule_tags"`
	RuleInfos      []AssetRuleInfoItem  `json:"rule_infos"`
	CompanyTags    []string             `json:"company_tags"`
	SoftwareNum    int                  `json:"software_num"`
	FirstTagNum    []AssetFirstTagItem  `json:"first_tag_num"`
	SecondTagNum   []AssetSecondTagItem `json:"second_tag_num"`
	Mac            string               `json:"mac"`
	IsHoneypot     bool                 `json:"is_honeypot"`
	IsFraud        bool                 `json:"is_fraud"`
	IsXc           interface{}          `json:"is_xc"`
	Ipv6Raw        string               `json:"ipv6_raw"`
}

type Resolution struct {
	Rtt  int64  `json:"rtt"`
	IP   string `json:"ip"`
	Type string `json:"type"`
	Ttl  uint32 `json:"ttl"`
}

type DomainDocument struct {
	AssetLevel     interface{}         `json:"asset_level"`
	BusinessApp    interface{}         `json:"business_app"`
	ComputerRoom   interface{}         `json:"computer_room"`
	Country        interface{}         `json:"country"`
	Province       interface{}         `json:"province"`
	City           interface{}         `json:"city"`
	Company        interface{}         `json:"company"`
	CustomFields   interface{}         `json:"custom_fields"`
	CustomNames    interface{}         `json:"custom_names"`
	Username       interface{}         `json:"username"`
	ManagerEmail   interface{}         `json:"manager_email"`
	ManagerMobile  interface{}         `json:"manager_mobile"`
	CatTags        []string            `json:"cat_tags"`
	RuleTags       []string            `json:"rule_tags"`
	RuleInfos      []AssetRuleInfoItem `json:"rule_infos"`
	CompanyTags    []string            `json:"company_tags"`
	Resolution     []Resolution        `json:"resolution"`
	Domain         string              `json:"domain"`
	Ip             string              `json:"ip"`
	TaskId         int                 `json:"task_id"`
	LastCheckTime  string              `json:"lastchecktime"`
	Createtime     interface{}         `json:"createtime,omitempty"`
	AddWay         string              `json:"add_way"`
	LastUpdateTime string              `json:"lastupdatetime"`
}

type AssetDocuments []*AssetDocument

type DomainDocuments []*DomainDocument

type HostIpDocument struct {
	Ip    string   `json:"ip"`
	Hosts []string `json:"hosts"`
}

func (docs AssetDocuments) Length() int {
	return len(docs)
}

func (docs AssetDocuments) IPs() []string {
	var ips = make([]string, 0, docs.Length())
	if docs != nil {
		for _, doc := range docs {
			ips = append(ips, doc.IP)
		}
	}

	if len(ips) == 0 {
		return nil
	}

	return ips
}

func (docs AssetDocuments) IPsForContainer() interside.Container {
	var ips = interside.NewContainer()

	if docs != nil {
		for _, doc := range docs {
			ips.Append(doc.IP)
		}
	}

	if ips.Length() == 0 {
		return nil
	}

	return ips
}
