package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfTag = "tags"
	TableDescOfTag = "标签表"
)

// Tag Tag module for database table.
type Tag struct {
	ID            uint       `json:"id" gorm:"type:int(11) unsigned auto_increment;primaryKey;not null;comment:记录ID"`
	Name          string     `json:"name" gorm:"size:100;null;comment:标签或标签分组名称"`
	Tag           string     `json:"tag,omitempty" gorm:"-"` // 兼容DCC IP段上传问题.
	TagType       string     `json:"tag_type" gorm:"size:100;null;comment:标签类型"`
	Ancestry      uint       `json:"ancestry" gorm:"null;comment:父级ID;index"`
	Position      uint       `json:"position" gorm:"null;comment:位置"`
	Mode          uint8      `json:"mode" gorm:"null;comment:添加模式(0:系统预置,1:自定义添加)"`
	ChildrenCount uint       `json:"children_count" gorm:"null;comment:子元素总个数"`
	Realname      string     `json:"realname" gorm:"size:100;null;comment:真实名称(英文不处理,中文处理为拼音)"`
	Email         string     `json:"email" gorm:"size:100;null;comment:邮箱"`
	Phone         string     `json:"phone" gorm:"size:100;null;comment:手机号码"`
	CreatedAt     *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt     *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
	DeletedAt     *time.Time `json:"deleted_at" gorm:"type:timestamp null comment '删除时间';"`
}



// TableName specifies the name of the table.
func (Tag) TableName() string {
	return TableNameOfTag
}