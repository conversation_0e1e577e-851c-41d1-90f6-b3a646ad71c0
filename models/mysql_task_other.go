package models

import "encoding/json"

//
type OtherConfigRaw string

func (raw OtherConfigRaw) ToStruct() (*TaskOtherConfig, error) {
	var r *TaskOtherConfig
	if err := json.Unmarshal([]byte(raw), &r); err != nil {
		return nil, err
	}
	return r, nil
}

// TaskOtherConfig task other param configure.
type TaskOtherConfig struct {
	IsDeepScan           bool `json:"is_deep_scan"`           // 资产精准识别
	IsPingRecognition    bool `json:"is_ping_recognition"`    // Ping识别资产
	IsTreckRecognition   bool `json:"is_treck_recognition"`   // Treck协议栈指纹检测
	IsCycleProtocol      bool `json:"is_cycle_protocol"`      // 全选协议识别
	IsDeepRecognition    bool `json:"is_deep_recognition"`    // 深度识别操作系统及设备
	IsCrackDns           bool `json:"is_crack_dns"`           // 域名解析(不传微内核，但需要透传)
	IsVersionRecognition bool `json:"is_version_recognition"` // 版本号识别(不传微内核，但需要透传)
}
