package models

import (
	"encoding/json"
	"strconv"
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
)

// Table-related constant definitions.
const (
	TableNameOfTask = "tasks"
	TableDescOfTask = "任务表"
)

type TaskCategory string

func (t TaskCategory) IsFlow() bool {
	if t == TaskCategoryOfFlow {
		return true
	}

	return false
}

func (t TaskCategory) IsTask() bool {
	if t == TaskCategoryOfTask {
		return true
	}

	return false
}

const (
	TaskCategoryOfTask TaskCategory = "task"
	TaskCategoryOfFlow TaskCategory = "flow"
)

// TaskIPType task ip type related constant definition.
const (
	TaskIPTypeOfIPV4 = iota + 1
	TaskIPTypeOfIPV6
	TaskIPTypeOfIPV4AndIPV6
)

// PocScanType poc scan type related constant definition.
const (
	PocScanTypeOfNormal  = "normal"   // 普通poc
	PocScanTypeOfWeekPwd = "week_pwd" // 弱口令poc
	PocScanTypeOfAll     = "all"      // 全部poc
	PocScanTypeOfSpecial = "special"  // 指定poc
)

const (
	IPRangeTypeOfAll          = "all"
	IPRangeTypeOfUserInput    = "user_input"
	IPRangeTypeOfIPRanges     = "ip_ranges"
	IPRangeTypeOfUploadFile   = "upload_file"
	IPRangeTypeOfCompany      = "company"       // 管理单元
	IPRangeTypeOfBusinessApp  = "business_app"  // 业务系统
	IPRangeTypeOfUsername     = "username"      // 负责人
	IPRangeTypeOfComputerRoom = "computer_room" // 机房信息
	IPRangeTypeOfDCC          = "dcc"
	IPRangeTypeOfDCCAll       = "dcc_all"
)

const (
	TaskTypeOfAsset = iota + 1
	TaskTypeOfVulnerability
	TaskTypeOfAssetAndVulnerability
)

const (
	DefaultTaskRepeatTimes            = 1
	DefaultTaskProtocolUpdateCycle    = 0
	DefaultTaskUnknownProtocolStorage = true
	DefaultDeepGetMAC                 = true
)

type TaskState int

func (state TaskState) Int() int {
	return int(state)
}

func (state TaskState) IsPausedState() bool {
	states := []TaskState{
		TaskStateOfManualPausing,
		TaskStateOfAutoPausing,
	}

	var paused bool
	for _, item := range states {
		if state == item {
			paused = true
		}
	}

	return paused
}

const (
	TaskTopOfTure  = true
	TaskTopOfFalse = false
)

const (
	TaskStateOfWaitingExecute TaskState = iota // Task waiting execute.
	TaskStateOfExecuting                       // Task executing.
	TaskStateOfManualPausing                   // Task manual pausing.
	TaskStateOfFinished                        // Task finished.
	TaskStateOfAutoPausing                     // Task auto pausing.
)

// Task model structure.
type Task struct {
	Id                 int               `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID"` // 记录ID.
	Title              string            `json:"title" gorm:"column:title;type:varchar(200)" `                                     // 标题名称
	Progress           float32           `json:"progress" gorm:"column:progress;type:float" `                                      // 进度
	TaskType           int               `json:"task_type" gorm:"column:task_type;type:int(11);default:1" `                        // 任务类型.
	UserID             int               `json:"user_id" gorm:"index:index_tasks_on_user_id;column:user_id;type:int(11)" `         // 用户ID.
	RealBeginTime      *time.Time        `json:"real_begin_time" gorm:"type:timestamp null comment 'real_begin_time';"`            // 开始时间.
	RealEndTime        *time.Time        `json:"real_end_time" gorm:"type:timestamp null comment 'real_end_time';"`                // 结束时间.
	UseSeconds         string            `json:"use_seconds" gorm:"column:use_seconds;type:varchar(255)" `                         // 已经使用秒数.
	State              TaskState         `json:"state" gorm:"column:state;type:int(11)" `                                          // 状态.
	RunType            string            `json:"run_type" gorm:"column:run_type;type:varchar(50);default:right_now" `              // 运行类型.
	RepeatType         string            `json:"repeat_type" gorm:"column:repeat_type;type:varchar(30)" `                          // 重复类型.
	Num                string            `json:"num" gorm:"column:num;type:varchar(10)" `                                          // 记录数量.
	BeginTime          string            `json:"begin_time" gorm:"column:begin_time;type:varchar(255)" `                           // 开始时间.
	AssetNum           int               `json:"asset_num" gorm:"column:asset_num;type:int(11)" `                                  // 资产数量.
	ThreatNum          int               `json:"threat_num" gorm:"column:threat_num;type:int(11)" `                                // 威胁数量.
	RuleNum            int               `json:"rule_num" gorm:"column:rule_num;type:int(11)" `                                    // 规则数量.
	ThreatRepairNum    int               `json:"threat_repair_num" gorm:"column:threat_repair_num;type:int(11)" `                  // 已修复威胁数量.
	IPRangeType        string            `json:"ip_range_type" gorm:"column:ip_range_type;type:varchar(255)" `                     // IP段类型.
	Bandwidth          int               `json:"bandwidth" gorm:"column:bandwidth;type:int(11)" `                                  // 带宽.
	ScanPortTemplateID int               `json:"scan_port_template_id" gorm:"column:scan_port_template_id;type:int(11)" `          // 扫描端口模板ID.
	DeepScan           string            `json:"deep_scan" gorm:"column:deep_scan;type:varchar(30)" `                              // 深度扫描
	PocScanType        string            `json:"poc_scan_type" gorm:"column:poc_scan_type;type:varchar(50)" `                      // POC扫描类型.
	Position           int               `json:"position" gorm:"index:index_tasks_on_position;column:position;type:int(11)" `      // 排序位置.
	CreatedAt          *time.Time        `json:"created_at" gorm:"column:created_at;type:datetime;not null" `                      // 创建时间.
	UpdatedAt          *time.Time        `json:"updated_at" gorm:"column:updated_at;type:datetime;not null" `                      // 更新时间.
	ScanStep           ScanStep          `json:"scan_step" gorm:"column:scan_step;type:varchar(50)" `                              // 扫描步骤.
	Ready              bool              `json:"ready" gorm:"column:ready;type:tinyint(1);default:0" `                             // 是否准备.
	Category           string            `json:"category" gorm:"column:category;type:varchar(50);default:task" `                   // 类别.
	TimerTaskSwitch    bool              `json:"timer_task_switch" gorm:"column:timer_task_switch;type:tinyint(1);default:1"`      // 记录ID.
	Top                bool              `json:"top" gorm:"column:top;type:tinyint(1);default:0" `                                 // 置顶.
	IPCount            int               `json:"ip_count" gorm:"column:ip_count;type:int(11)"`                                     // ID总数.
	Concurrency        int               `json:"concurrency" gorm:"type:int(11);default:0;comment:识别并发数"`                          // 识别并发
	OtherConfig        OtherConfigRaw    `json:"other_cfgs" gorm:"column:other_cfgs;type:varchar(2000)" `                          // 其他配置
	StartedAt          *time.Time        `json:"started_at" gorm:"type:timestamp null comment 'started_at';"`                      // 开始时间.
	DccTaskID          int               `json:"dcc_task_id" gorm:"column:dcc_task_id;type:int(11)" `                              // DCC任务ID.
	ScanType           exchange.ScanType `json:"scan_type" gorm:"column:scan_type;type:varchar(45);default:quick" `                // 扫描类型.
	IPType             exchange.IPType   `json:"ip_type" gorm:"column:ip_type;type:int(11);default:1" `                            // IP类型: 1.IPv4 2.IPv6
	AssetType          int               `json:"asset_type" gorm:"column:asset_type;type:int(11)" `                                // 资产类型，0：已知资产 1：推荐资产
}

// TableName Override table name.
func (m *Task) TableName() string {
	return TableNameOfTask
}

func (m *Task) IsVulnerability() bool {
	if m.TaskType == TaskTypeOfVulnerability {
		return true
	}

	return false
}

func (m *Task) IsVulnerabilityMultiple() bool {
	if m.TaskType == TaskTypeOfVulnerability ||
		m.TaskType == TaskTypeOfAssetAndVulnerability {
		return true
	}

	return false
}

func (m *Task) UseSecondsInteger() int {
	i, err := strconv.Atoi(m.UseSeconds)
	if err != nil {
		return 0
	}
	return i
}

func (m *Task) String() string {
	marshal, _ := json.Marshal(&m)
	return string(marshal)
}
