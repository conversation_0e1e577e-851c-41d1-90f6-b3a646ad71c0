package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfBlackList = "blacklists"
	TableDescOfBlackList = "禁扫IP表"
)

// Blacklist database table.
type Blacklist struct {
	Id        uint       `json:"id" gorm:"type:int(11) unsigned auto_increment;primaryKey;not null;comment:记录ID"`
	IP        string     `json:"ip" gorm:"size:120;null;comment:IP地址;"`
	Name      string     `json:"name" gorm:"size:100;null;comment:名称;"`
	CreatedAt *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
}

// TableName Override table name.
func (Blacklist) TableName() string {
	return TableNameOfBlackList
}
