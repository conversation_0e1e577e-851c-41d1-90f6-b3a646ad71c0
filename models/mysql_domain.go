package models

import (
	"time"
)

// Table-related constant definitions.
const (
	TableName              = "domains"
	TableNameOfDescription = "域名表"
)

type Domains struct {
	Id        uint      `gorm:"type:int(11);unsigned; auto_increment;primaryKey;not null;comment:记录ID"`
	Name      string    `gorm:"type:varchar(500);default:null;comment:域名名称;"`
	CreatedAt time.Time `gorm:"type:datetime;not null;comment:创建时间;"`
	UpdatedAt time.Time `gorm:"type:datetime;not null;comment:修改时间;"`
}

// TableName 表名称
func (d *Domains) TableName() string {
	return TableName
}

// DomainBase Model
//
// 数据模型 DomainBase 包含IP管理范围数据基本数据信息。
//
// swagger:model DomainBase
type DomainBase struct {
	// 域名名称.
	//
	// required: true
	// read only: false
	// example: 22
	Name string `json:"name"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 33
	ID uint `json:"id"`
}
