package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestTaskSuite(t *testing.T) {
	suite.Run(t, &TaskSuite{})
}

type TaskSuite struct {
	suite.Suite
	state TaskState
	task  *Task
}

func (suite *TaskSuite) BeforeTest(suiteName, testName string) {
	suite.state = TaskStateOfWaitingExecute
	suite.task = &Task{
		UseSeconds: "1m",
	}
}

func (suite *TaskSuite) Test_IsPausedState_False() {
	assert.Equal(suite.T(), false, suite.state.IsPausedState())
}

func (suite *TaskSuite) Test_IsPausedState_True_AutoPausing() {
	suite.state = TaskStateOfAutoPausing
	assert.Equal(suite.T(), true, suite.state.IsPausedState())
}

func (suite *TaskSuite) Test_IsPausedState_True_TaskStateOfManualPausing() {
	suite.state = TaskStateOfManualPausing
	assert.Equal(suite.T(), true, suite.state.IsPausedState())
}

func (suite *TaskSuite) Test_IsPausedState_False_TaskStateOfFinished() {
	suite.state = TaskStateOfFinished
	assert.Equal(suite.T(), false, suite.state.IsPausedState())
}

func (suite *TaskSuite) Test_UseSecondsInteger() {
	actual := suite.task.UseSecondsInteger()
	assert.Equal(suite.T(), actual, 0)

	suite.task.UseSeconds = "102"
	actual = suite.task.UseSecondsInteger()
	assert.Equal(suite.T(), actual, 102)
}

func (suite *TaskSuite) Test_IsVulnerability() {
	actual := suite.task.IsVulnerability()
	assert.False(suite.T(), actual)
}

func (suite *TaskSuite) Test_IsVulnerabilityMultiple() {
	actual := suite.task.IsVulnerabilityMultiple()
	assert.False(suite.T(), actual)
}

func (suite *TaskSuite) Test_String() {
	actual := suite.task.String()
	assert.NotEmpty(suite.T(), actual)
}
