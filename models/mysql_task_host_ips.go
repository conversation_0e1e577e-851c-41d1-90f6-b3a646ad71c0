package models

import "time"

const (
	TableNameOfHostUrls = "host_urls"
	TableDescOfHostUrls = "主机对应Urls链接表"
)

// HostUrls model structure.
type HostUrls struct {
	ID        int        `json:"id" gorm:"column:id;type:int(11) unsigned auto_increment;primary_key;not null"`
	Host      string     `json:"host" gorm:"column:host;type:varchar(255);default null;"`
	Urls      string     `json:"urls" gorm:"column:urls;type:text;"`
	CreatedAt *time.Time `json:"created_at" gorm:"column:created_at;type:datetime;not null"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"column:updated_at;type:datetime;not null"`
}

// TableName Override table name.
func (m *HostUrls) TableName() string {
	return TableNameOfHostUrls
}
