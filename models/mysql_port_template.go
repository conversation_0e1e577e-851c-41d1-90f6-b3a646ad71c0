package models

import (
	"time"
)

// Table-related constant definitions.
const (
	TableNameOfPortTemplate = "ports_templates"
	TableDescOfPortTemplate = "端口及扫描端口模板(端口分组)关联表"
)

// PortTemplate ports_templates table all fields.
type PortTemplate struct {
	ID                 int        `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID"`
	ScanPortID         int        `json:"scan_port_id" gorm:"type:int(11);index;uniqueIndex:scan_port_id_and_scan_port_template_id;comment:"`
	ScanPortTemplateID int        `json:"scan_port_template_id" gorm:"type:int(11);index;uniqueIndex:scan_port_id_and_scan_port_template_id;comment:"`
	CreatedAt          *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt          *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
}

// TableName specifies the name of the table.
func (PortTemplate) TableName() string {
	return TableNameOfPortTemplate
}
