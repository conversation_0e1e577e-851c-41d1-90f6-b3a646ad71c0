package models

// Table-related constant definitions.
const (
	TableNameOfProtocolScanPort = "protocols_scan_ports"
	TableDescOfProtocolScanPort = "端口和协议关联表"
)

// ProtocolScanPort protocol and scan port reference database table.
type ProtocolScanPort struct {
	Id              int    `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID;"`
	ScanPortId      int    `json:"scan_port_id" gorm:"type:int(11) unsigned;null;index;comment:扫描端口表记录ID;"`
	ProtocolId      int    `json:"protocol_id" gorm:"type:int(11) unsigned;null;index;comment:协议表记录ID;"`
	UniProtocolId   int    `json:"uni_protocol_id" gorm:"type:int(11) unsigned;null;index;comment:协议表记录ID;"`
	UniProtocolType string `json:"uni_protocol_type" gorm:"type:varchar(255);null;comment:协议类型;"`
}

// TableName Override table name.
func (ProtocolScanPort) TableName() string {
	return TableNameOfProtocolScanPort
}
