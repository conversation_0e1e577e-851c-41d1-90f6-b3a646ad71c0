package models

import "time"

// Database table related constants' definition.
const (
	TableNameOfPoc = "pocs"
	TableDescOfPoc = "poc表"
)

// ThreatLevel related constants' definition.
const (
	ThreatLevelOfLow = iota
	ThreatLevelOfModerate
	ThreatLevelOfHigh
	ThreatLevelOfVeryHigh
)

// Poc model structure.
type Poc struct {
	ID               uint       `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID"`
	Name             string     `json:"name" gorm:"type:varchar(1000);comment:poc名称;"`
	Description      string     `json:"description" gorm:"type:text;comment:描述;"`
	Filename         string     `json:"filename" gorm:"type:varchar(255);default:null;comment:文件名;unique_index"`
	Author           string     `json:"author" gorm:"type:varchar(255);comment:'作者';"`
	Product          string     `json:"product" gorm:"type:varchar(255);comment:对应产品;"`
	Homepage         string     `json:"homepage" gorm:"type:varchar(255);comment:产品主页;"`
	References       string     `json:"references" gorm:"column:references;type:text;comment:引用地址;" `
	Fofaquery        string     `json:"fofaquery" gorm:"column:fofaquery;type:text;comment:查询语句;"`
	Content          string     `json:"content" gorm:"column:content;type:text;comment:漏洞验证参数;"`
	State            uint       `json:"state" gorm:"column:state;type:int(11);default:0;comment:状态;"`
	UserID           uint       `json:"user_id" gorm:"column:user_id;type:int(11);comment:用户ID;"`
	Price            uint       `json:"price" gorm:"column:price;type:int(11);default:0;comment:价格;"`
	Score            uint       `json:"score" gorm:"column:score;type:int(11);default:0;comment:评分;"`
	CommentsCount    uint       `json:"comments_count" gorm:"column:comments_count;type:int(11);default:0;comment:评论数;"`
	FofaRecords      uint       `json:"fofa_records" gorm:"column:fofa_records;type:int(11);default:0;comment:fofa影响ip数;"`
	Level            uint8      `json:"level" gorm:"column:level;type:tinyint(4);default:3;comment:等级;"`
	XdaySt           string     `json:"xday_st" gorm:"column:xday_st;type:varchar(100);default:0;comment:0dya,nday;"`
	AffectAmount     uint       `json:"affect_amount" gorm:"column:affect_amount;type:int(11);default:0;comment:影响总量;"`
	AffectEnterprise string     `json:"affect_enterprise" gorm:"column:affect_enterprise;type:text;comment:影响企业;"`
	RejectReason     string     `json:"reject_reason" gorm:"column:reject_reason;type:varchar(500);comment:拒绝理由;"`
	VulDefine        string     `json:"vul_define" gorm:"column:vul_define;type:varchar(100);comment:漏洞定义;"`
	ProductionDefine string     `json:"production_define" gorm:"column:production_define;type:varchar(100);comment:产品定义;"`
	PriceDefine      string     `json:"price_define" gorm:"column:price_define;type:varchar(100);comment:价格定义;"`
	TaskState        uint       `json:"task_state" gorm:"column:task_state;type:int(11);default:0;comment:任务状态;"`
	TaskPercent      string     `json:"task_percent" gorm:"column:task_percent;type:varchar(10);comment:任务进度;"`
	CreatedAt        *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt        *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
	LastScanedAt     *time.Time `json:"last_scaned_at" gorm:"column:last_scaned_at;type:datetime;null;comment:最后扫描时间;"`
	LastTid          float64    `json:"last_tid" gorm:"column:last_tid;type:decimal(20,0);comment:最新任务ID;"`
	Impact           string     `json:"impact" gorm:"column:impact;type:mediumtext;comment:漏洞危害;"`
	Recommandation   string     `json:"recommandation" gorm:"column:recommandation;type:mediumtext;comment:修复建议;"`
	HasExp           bool       `json:"has_exp" gorm:"column:has_exp;type:tinyint(1);default:0;comment:是否可验证;"`
	ThreatCount      uint       `json:"threat_count" gorm:"column:threat_count;type:int(11);comment:漏洞数;"`
	ScanTaskID       uint       `json:"scan_task_id" gorm:"column:scan_task_id;type:int(11);comment:扫描任务ID;"`
	BeginScanTime    *time.Time `json:"begin_scan_time" gorm:"column:begin_scan_time;type:datetime;comment:开始扫描时间;"`
	CveID            string     `json:"cveId" gorm:"column:cveId;type:varchar(2000);comment:Cve编号;"`
	VulType          string     `json:"vulType" gorm:"column:vulType;type:varchar(2000);default:null;comment:漏洞类型;"`
	DisclosureDate   *time.Time `json:"disclosure_date" gorm:"column:disclosure_date;type:datetime;comment:披露;"`
	VulNum           *string    `json:"vulNum" gorm:"unique;column:vulNum;type:varchar(255);comment:内部漏洞编号;"` // 自定义唯一标识
}
