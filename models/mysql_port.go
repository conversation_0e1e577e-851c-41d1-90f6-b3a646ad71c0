package models

import (
	"time"
)

// Table-related constant definitions.
const (
	TableNameOfScanPort = "scan_ports"
	TableDescOfScanPort = "端口表"
)

// Add way related constant definitions.
const (
	AddWayOfSelfAdd      = "self_add"     // 自定义添加
	AddWayOfSystemPreset = "system"       // 系统预制
	AddWayOfIPortIpCsv   = "iport_ip_csv" // 导入添加
	AddWayOfScanAdd      = "scan_add"     // 扫描添加
	AddWayOfFlow         = "flow"         // 流量分析
)

// ScanPortState related constant definition
const (
	ScanPortStateOfEnabled int = iota
	ScanPortStateOfDisabled
)

// ScanPort scan_ports table all fields
type ScanPort struct {
	ID             int        `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID"`
	Port           int        `json:"port" gorm:"type:int(11);default:0;comment:端口号;uniqueIndex"`
	Desc           string     `json:"desc" gorm:"size:500;null;comment:协议名称"`
	AddWay         string     `json:"add_way" gorm:"size:50;null;comment:添加方式"`
	AllProtocol    uint8      `json:"all_protocol" gorm:"type:tinyint(1);null;comment:遍历所有协议"`
	ScanPortTypeID int        `json:"scan_port_type_id" gorm:"type:int(11);default:0;comment:协议类型分组ID;index"`
	State          int        `json:"state" gorm:"type:int(11);default:0;comment:端口状态(0:启用,1:禁用)"`
	CustomSort     int        `json:"custom_sort" gorm:"type:int(11);default:0;comment:自定义排序"`
	CreatedAt      *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt      *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
}

// TableName specifies the name of the table.
func (ScanPort) TableName() string {
	return TableNameOfScanPort
}

type TaskDccPorts struct {
	ID         int    `json:"id"`
	ScanPortId int    `json:"scan_port_id"`
	ProtocolId int    `json:"protocol_id"`
	Port       int    `json:"port"`
	Category   string `json:"category"`
	Name       string `json:"name"`
}
