package models

type EngineType string

func (t EngineType) String() string {
	return string(t)
}

const (
	EngineTypeOfAssetScan        EngineType = "AssetScanEngine"         // 资产扫描引擎
	EngineTypeOfAssetSync        EngineType = "AssetSyncEngine"         // 资产同步引擎
	EngineTypeOfAttributeExtract EngineType = "TheExtractionEngine"     // 属性提取引擎
	EngineTypeOfTagging          EngineType = "TaggingEngine"           // 打标签引擎
	EngineTypeOfVulnerability    EngineType = "VulnerabilityScanEngine" // 漏洞扫描引擎
	EngineTypeOfReport           EngineType = "ReportEngine"            // 生成报告引擎
	EngineTypeOfComplete         EngineType = "Complete"                // 完成通知
)
