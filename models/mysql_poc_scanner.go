package models

type Scanned struct {
	Connection   interface{} `json:"Connection"`
	FileName     string      `json:"FileName"`
	HostInfo     string      `json:"HostInfo"`
	LastResponse interface{} `json:"LastResponse"` // Show on vulnerable is true
	Level        interface{} `json:"Level"`        // Show on vulnerable is true
	Name         string      `json:"Name"`
	VulURL       interface{} `json:"VulURL"` // Show on vulnerable is true
	Vulnerable   bool        `json:"Vulnerable"`
}
