package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfRule = "rules"
	TableDescOfRule = "规则列表"
)

type Rule struct {
	ID              uint      `gorm:"type:int(11) unsigned auto_increment;primaryKey;not null;comment:记录ID"`
	Company         string    `gorm:"type:varchar(255);comment:厂商"`
	Product         string    `gorm:"type:varchar(255);uniqueIndex:index_rule_on_product_and_rule_and_user_id,type:btree,length:50;comment:规则名称"`
	ProductUrl      string    `gorm:"column:producturl;type:varchar(255);comment:应用网站"`
	Rule            string    `gorm:"type:text;uniqueIndex:index_rule_on_product_and_rule_and_user_id,type:btree,length:250;comment:规则内容"`
	CreatedAt       time.Time `gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt       time.Time `gorm:"type:timestamp null comment '更新时间';"`
	UserID          int       `gorm:"column:user_id;type:int(11);uniqueIndex:index_rule_on_product_and_rule_and_user_id,type:btree;comment:用户ID;"`
	Published       uint      `gorm:"type:tinyint(1);default:0;"`
	FromRuleID      int       `gorm:"type:int(11);"`
	Taged           uint      `gorm:"type:tinyint(4);default:null;"`
	CompanyTaged    uint      `gorm:"type:tinyint(4);default:null;"`
	PubFofaShow     uint      `gorm:"type:tinyint(4);default:null;"`
	EncryptedRule   string    `gorm:"type:varchar(6000);comment:规则名称"`
	EncryptedRuleIv string    `gorm:"type:varchar(255);comment:规则名称"`
	GarbageRule     int       `gorm:"type:int(11)"`
	CountryCode     int       `gorm:"type:int(11);"`
	SoftHardCode    int       `gorm:"type:int(11);"`
	LevelCode       int       `gorm:"type:int(11);"`
	IndustryId      int       `gorm:"type:int(11);"`
	Price           float64   `gorm:"type:decimal(20,2);default:0.00"`
	Desc            string    `gorm:"type:text;"`
	CompanyDesc     string    `gorm:"type:text;"`
	EnCompany       string    `gorm:"type:varchar(255);"`
	EnProduct       string    `gorm:"type:varchar(255);"`
}

// TableName specifies the name of the table.
func (Rule) TableName() string {
	return TableNameOfRule
}
