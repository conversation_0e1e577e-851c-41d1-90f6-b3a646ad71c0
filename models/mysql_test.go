package models

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/stretchr/testify/suite"
)

func TestMySQLSuite(t *testing.T) {
	suite.Run(t, new(MySQLSuite))
}

type MySQLSuite struct {
	suite.Suite
}

func (suite *MySQLSuite) BeforeTest(suiteName, testName string) {

}

func (suite *MySQLSuite) Test_TaskPoc() {
	model := &TaskPoc{}
	assert.Equal(suite.T(), "task_pocs", model.TableName())
}

func (suite *MySQLSuite) Test_HostUrls() {
	model := &HostUrls{}
	assert.Equal(suite.T(), "host_urls", model.TableName())
}

func (suite *MySQLSuite) Test_Tag() {
	model := &Tag{}
	assert.Equal(suite.T(), "tags", model.TableName())
}

func (suite *MySQLSuite) Test_Task() {
	model := &Task{}
	assert.Equal(suite.T(), "tasks", model.TableName())
}

func (suite *MySQLSuite) Test_System() {
	model := &System{}
	assert.Equal(suite.T(), "systems", model.TableName())
}

func (suite *MySQLSuite) Test_Blacklist() {
	model := &Blacklist{}
	assert.Equal(suite.T(), "blacklists", model.TableName())
}

func (suite *MySQLSuite) Test_ScanPortTemplate() {
	model := &ScanPortTemplate{}
	assert.Equal(suite.T(), "scan_port_templates", model.TableName())
}

func (suite *MySQLSuite) Test_ScanPort() {
	model := &ScanPort{}
	assert.Equal(suite.T(), "scan_ports", model.TableName())
}

func (suite *MySQLSuite) Test_IPRangeConditions() {
	model := &IPRangeConditions{}
	assert.Equal(suite.T(), "ip_range_conditions", model.TableName())
}

func (suite *MySQLSuite) Test_Protocol() {
	model := &Protocol{}
	assert.Equal(suite.T(), "protocols", model.TableName())
}

func (suite *MySQLSuite) Test_ProtocolRule() {
	model := &Rule{}
	assert.Equal(suite.T(), "rules", model.TableName())
}

func (suite *MySQLSuite) Test_PortTemplate() {
	model := &PortTemplate{}
	assert.Equal(suite.T(), "ports_templates", model.TableName())
}

func (suite *MySQLSuite) Test_IPRangeTag() {
	model := &IPRangeTag{}
	assert.Equal(suite.T(), "ip_range_tags", model.TableName())
}

func (suite *MySQLSuite) Test_ProtocolScanPort() {
	model := &ProtocolScanPort{}
	assert.Equal(suite.T(), "protocols_scan_ports", model.TableName())
}
