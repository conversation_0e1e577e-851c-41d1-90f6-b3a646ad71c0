package models

import (
	"time"
)

// Table-related constant definitions.
const (
	TableNameOfScanPortTemplate = "scan_port_templates"
	TableDescOfScanPortTemplate = "端口分组表"
)

const (
	RecommendedOfName    = "全部常用端口"
	NonStandardPortGroup = "非标端口组"
)

// ScanPortTemplate scan port template reference database table.
type ScanPortTemplate struct {
	ID            uint       `gorm:"type:int(11) unsigned auto_increment;primaryKey;not null;comment:记录ID"`
	Name          string     `gorm:"size:50;null;comment:端口分组名称;"`
	AddWay        string     `gorm:"size:50;null;default:system;comment:添加方式"`
	IsRecommended uint8      `gorm:"tinyint(1);default:0;comment:是否是推荐分组(0:否,1:是)"`
	CreatedAt     *time.Time `gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt     *time.Time `gorm:"type:timestamp null comment '更新时间';"`
	TitleKey      string     `gorm:"size(50);null;comment:I DON'T KNOW"`
}

// TableName specifies the name of the table.
func (ScanPortTemplate) TableName() string {
	return TableNameOfScanPortTemplate
}
