package models

import (
	"time"

	"github.com/thoas/go-funk"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
)

// Table-related constant definitions.
const (
	TableNameOfIPRange = "ip_ranges"
	TableDescOfIPRange = "IP段管理表"
)

const (
	AddWayOfSelfDefined = "self_defined"
)

// IP type related constant definitions.
const (
	IpTypeOfIPV4 = 1
	IpTypeOfIPV6 = 2
)

// IP range scan related constant definitions.
const (
	ScanOfTRUE  = 1
	ScanOfFALSE = 0
)

const (
	SingleIPAddress = 1
)

const (
	ErrorOfOverflowCustomAdditionMaximumNumber = "最多可以自定义3个标签分类"
)

type IPRanges []*IPRange

func (ipranges IPRanges) Hosts() exchange.Hosts {
	var hosts = make(exchange.Hosts, 0)

	for _, item := range ipranges {
		// 通过关系表获取对应的 域名信息
		for _, domain := range item.Domains {
			hosts = append(hosts, domain.Name)
		}
	}

	return funk.UniqString(hosts)
}

func (ipranges IPRanges) IPRanges() exchange.IPRanges {
	var ranges = make(exchange.IPRanges, 0)

	for _, item := range ipranges {
		if item.IpRange != "" {
			ranges = append(ranges, item.IpRange)
		}
	}

	return ranges
}

func (ipranges IPRanges) Length() int {
	return len(ipranges)
}

type IPRange struct {
	ID            uint       `json:"id" gorm:"type:int(11) unsigned auto_increment;primaryKey;not null;comment:记录ID"`
	UserID        uint       `json:"user_id" gorm:"type:int(11);null;comment:记录ID"`
	Name          string     `json:"name" gorm:"size:1000;null;comment:名称;"`
	Host          string     `json:"host" gorm:"size:1000;null;comment:主机;"`
	IpRange       string     `json:"ip_range" gorm:"size:30;null;comment:IP段信息;index:index_ip_ranges_on_ip_range_and_gid"`
	Province      string     `json:"province" gorm:"size:100;null;comment:地理位置,省份"`
	City          string     `json:"city" gorm:"size:100;null;comment:地理位置,市"`
	AddWay        string     `json:"add_way" gorm:"size:100;null;default:self_defined;comment:添加方式"`
	Scan          uint8      `json:"scan" gorm:"type:tinyint(1);default:1;comment:是否扫描(0:不扫描,1:扫描)"`
	ComputerRoom  string     `json:"computer_room" gorm:"size:200;null;comment:机房信息"`
	Company       string     `json:"company" gorm:"size:100;null;comment:管理单元"`
	BusinessApp   string     `json:"business_app" gorm:"size:150;null;comment:业务系统"`
	ManagerName   string     `json:"manager_name" gorm:"size:30;null;comment:负责人"`
	ManagerMobile string     `json:"manager_mobile" gorm:"size:40;null;comment:负责人手机号"`
	ManagerEmail  string     `json:"manager_email" gorm:"size:200;null;comment:负责人邮箱"`
	BelongUserID  *uint      `json:"belong_user_id" gorm:"type:int(11);null;index;comment:负责人ID;"`
	GroupName     string     `json:"group_name" gorm:"size:150;null;comment:分组名称;"`
	Gid           string     `json:"gid" gorm:"size:50;null;comment:负责人ID;index:index_ip_ranges_on_ip_range_and_gid"`
	IpType        uint       `json:"ip_type" gorm:"type:int(11);null;comment:IP类型(1:IPv4,2:IPv6)"`
	Domains       []Domains  `json:"domains" gorm:"many2many:domain_ip_relations;"`
	CreatedAt     *time.Time `json:"created_at" gorm:"type:timestamp default current_timestamp comment '添加时间';"`
	UpdatedAt     *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
	DeletedAt     *time.Time `json:"deleted_at" gorm:"type:timestamp null comment '删除时间';"`
}

type IPRangeCidr struct {
	ID      uint   `json:"id"`
	IpRange string `json:"ip_range"`
	//IpCidrs address.IPRangeCIDRs `json:"ip_cidrs"`
	IpCount int             `json:"ip_count"`
	Cidrs   []*address.CIDR `json:"cidrs"`
	Type    string          `json:"type"`
}

// TableName specifies the name of the table.
func (IPRange) TableName() string {
	return TableNameOfIPRange
}

type TaskIpRanges struct {
	ID      int    `json:"id"`
	Vals    string `json:"vals"`
	IpRange string `json:"ip_range"`
	Host    string `json:"host"`
}

// IPRangeReferenceTags IpRangesResult
type IPRangeReferenceTags struct {
	Id             int    `gorm:"column:id"`
	Name           string `gorm:"column:name"`
	RealName       string `gorm:"column:realname"`
	Ancestry       int    `gorm:"column:ancestry"`
	ParentName     string `gorm:"column:parentname"`
	ParentRealName string `gorm:"column:parentrealname"`
	ManagerEmail   string `gorm:"column:manager_email"`
	ManagerMobile  string `gorm:"column:manager_mobile"`
	City           string `gorm:"column:city"`
	Province       string `gorm:"column:province"`
	IpRange        string `gorm:"column:ip_range"`
	AddWay         string `gorm:"column:add_way"`
}

// IPRangeTags IPRange tags.
type IPRangeTags struct {
	Country       string `json:"country"`
	City          string `json:"city"`
	Province      string `json:"province"`
	ComputerRoom  string `json:"computer_room"` // '机房信息',
	Company       string `json:"company"`       // '管理单元',
	BusinessApp   string `json:"business_app"`  // '业务系统',
	AssetLevel    string `json:"asset_level"`   // '资产等级',
	UserName      string `json:"user_name"`
	ManagerEmail  string `json:"manager_email"`  // 管理者邮箱
	ManagerMobile string `json:"manager_mobile"` // 管理者手机号
	// ParentTag     []string               `json:"parent_tag"`
	// Tag           []string               `json:"tag"`
	CustomNames  map[string]interface{} `json:"custom_names"`
	CustomFields map[string]interface{} `json:"custom_fields"`
	AddWay       string                 `json:"add_way"`

	IpRange   string `json:"ip_range"`
	IpRangeId uint   `json:"ip_range_id"`
}
