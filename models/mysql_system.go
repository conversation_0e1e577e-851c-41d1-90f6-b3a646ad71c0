package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfSystem = "systems"
	TableDescOfSystem = "系统表"
)

const (
	SystemKeyOfGetwayMac = "getway_mac"
)

// System database table.
type System struct {
	Id        int        `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID;"`
	Key       string     `json:"key" gorm:"type:varchar(50);default null;comment:键;"`
	Value     string     `json:"value" gorm:"type:varchar(200);default null;comment:值;"`
	Category  string     `json:"category" gorm:"type:varchar(50);default:ip_config;comment:分类;"`
	CreatedAt *time.Time `json:"created_at" gorm:"type:datetime;not null;comment:创建时间;"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"type:datetime;not null;comment:更新时间"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"type:datetime;null;comment:删除时间"`
}

// TableName Override table name.
func (m *System) TableName() string {
	return TableNameOfSystem
}
