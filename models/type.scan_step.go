package models

import "fmt"

type ScanStep string

func (s ScanStep) String() string {
	return string(s)
}

func (s ScanStep) JoinSuffix(suffix string) ScanStep {
	return ScanStep(fmt.Sprintf("%s_%s", s, suffix))
}

const (
	ScanStepOfAssetScan         ScanStep = "masscan"
	ScanStepOfAssetSync         ScanStep = "syncasset"
	ScanStepOfAttributeExtract  ScanStep = "extraction"
	ScanStepOfTagging           ScanStep = "tagging"
	ScanStepOfVulnerabilityScan ScanStep = "vulscan"
	ScanStepOfReport            ScanStep = "report"
)
