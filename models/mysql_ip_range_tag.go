package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfIPRangeTag = "ip_range_tags"
	TableDescOfIPRangeTag = "IP段与标签表中间表"
)

type IPRangeTag struct {
	Id        int        `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID"`
	IpRangeID int        `json:"ip_range_id" gorm:"type:int(11);uniqueIndex:idx_ip_range_id_and_tag_id;comment:IPRangeId"`
	TagID     int        `json:"tag_id" gorm:"type:int(11);uniqueIndex:idx_ip_range_id_and_tag_id;comment:TagId"`
	CreatedAt *time.Time `json:"created_at" gorm:"type:timestamp;default current_timestamp;comment:添加时间;"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"type:timestamp null comment '更新时间';"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"type:timestamp null comment '删除时间';"`
}

// TableName specifies the name of the table.
func (IPRangeTag) TableName() string {
	return TableNameOfIPRangeTag
}
