package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestOtherConfigRaw_ToStruct(t *testing.T) {
	config := `{
		"treck_scan":false,
		"ping":true,
		"cycle_protocol":true,
		"deep_recognition":true,
		"protocol_concurrency":100,
		"crack_dns":false,
		"version_recognition":true
	}`

	raw := OtherConfigRaw(config)
	actual, err := raw.ToStruct()
	assert.NoError(t, err)
	assert.NotNil(t, actual)
}
