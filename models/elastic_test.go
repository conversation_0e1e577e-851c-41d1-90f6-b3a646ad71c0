package models

import (
	"testing"

	"git.gobies.org/foeye-dependencies/fsfire"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestElasticStoreSuite(t *testing.T) {
	suite.Run(t, &ElasticStoreSuite{})
}

type ElasticStoreSuite struct {
	suite.Suite
	err             error
	baseDir         string
	testTaskDataDir string
	data            *elastic.SearchHit
}

func (suite *ElasticStoreSuite) BeforeTest(suiteName, testName string) {
	suite.baseDir = "./../"
	suite.testTaskDataDir, suite.err = fsfire.GetFilePathWithFileSystemPath(
		suite.baseDir,
		fsfire.WithSpecificFileSystemPath("test/data/task"),
	)
	assert.NoError(suite.T(), suite.err)
}

func (suite *ElasticStoreSuite) TestAppendUpdates() {
	migrate := MigrateToAsset{}
	update := &AssetDocument{IP: "*******"}
	migrate.AppendUpdates(update)
	assert.Equal(suite.T(), 1, len(migrate.Updates))
}

func (suite *ElasticStoreSuite) TestAppendInserts() {
	migrate := MigrateToAsset{}
	insert := &AssetDocument{IP: "*******"}
	migrate.AppendInserts(insert)
	assert.Equal(suite.T(), 1, len(migrate.Inserts))
}

func (suite *ElasticStoreSuite) TestLength() {
	docs := &AssetDocuments{&AssetDocument{TaskId: 1, IP: "************"}}
	length := docs.Length()
	assert.Equal(suite.T(), 1, length)
}

func (suite *ElasticStoreSuite) TestIPs() {
	suite.T().Run("no ips", func(t *testing.T) {
		docs := &AssetDocuments{}
		ips := docs.IPs()
		assert.Equal(suite.T(), []string([]string(nil)), ips)
	})
	suite.T().Run("has ips", func(t *testing.T) {
		docs := &AssetDocuments{&AssetDocument{TaskId: 1, IP: "************"}}
		ips := docs.IPs()
		assert.Equal(suite.T(), []string{"************"}, ips)
	})
}

func (suite *ElasticStoreSuite) TestIPsForContainer() {
	suite.T().Run("no ips", func(t *testing.T) {
		docs := &AssetDocuments{}
		container := docs.IPsForContainer()
		assert.Equal(suite.T(), nil, container)
	})
	suite.T().Run("has ips", func(t *testing.T) {
		docs := &AssetDocuments{&AssetDocument{TaskId: 1, IP: "************"}}
		container := docs.IPsForContainer()
		assert.NotNil(suite.T(), container)
	})
}
