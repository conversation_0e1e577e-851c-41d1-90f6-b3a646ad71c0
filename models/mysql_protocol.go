package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfProtocols = "protocols"
	TableDescOfProtocols = "协议表"
)

const (
	ProtocolCategoryOfTCP = "T"
	ProtocolCategoryOfUDP = "U"
)

// Protocol database table.
type Protocol struct {
	Id        int        `json:"id" gorm:"type:int(11) unsigned auto_increment;primary_key;not null;comment:记录ID;"`
	Name      string     `json:"name" gorm:"type:varchar(100);null;comment:协议名称;"`
	Category  string     `json:"category" gorm:"type:varchar(20);null;comment:协议分类;"`
	CreatedAt *time.Time `json:"created_at" gorm:"type:datetime;not null;comment:创建时间"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"type:datetime;not null;comment:更新时间"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"type:datetime;null;comment:更新时间"`
}

// TableName Override table name.
func (Protocol) TableName() string {
	return TableNameOfProtocols
}
