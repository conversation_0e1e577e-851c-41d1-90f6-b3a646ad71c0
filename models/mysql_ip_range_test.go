package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestIPRangesSuite(t *testing.T) {
	suite.Run(t, &IPRangesSuite{})
}

type IPRangesSuite struct {
	suite.Suite
	IPRanges IPRanges
}

func (suite *IPRangesSuite) BeforeTest(suiteName, testName string) {
	suite.IPRanges = IPRanges{
		{
			Host:    "127.0.0.1",
			IpRange: "**********/24",
		},
	}
}

func (suite *IPRangesSuite) Test_IPRanges_Host() {
	actual := suite.IPRanges.Hosts()
	assert.NotNil(suite.T(), actual)
}

func (suite *IPRangesSuite) Test_IPRanges_IPRanges() {
	actual := suite.IPRanges.IPRanges()
	assert.NotNil(suite.T(), actual)
}

func (suite *IPRangesSuite) Test_IPRanges_Length() {
	actual := suite.IPRanges.IPRanges()
	assert.Len(suite.T(), actual, 1)
}
