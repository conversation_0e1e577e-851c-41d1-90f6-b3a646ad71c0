package models

import "time"

const (
	TableNameOfTaskPoc = "task_pocs"
	TableDescOfTaskPoc = "任务poc表"
)

// TaskPocState task poc state related constant definition.
const (
	TaskPocStateOfWaiting = iota + 1
	TaskPocStateOfScanning

	TaskPocStateOfFinished = iota + 3
	TaskPocStateOfUnusual
)

// TaskPoc model structure.
type TaskPoc struct {
	ID          int        `json:"id" gorm:"column:id;type:int(11) unsigned auto_increment;not null"`
	TaskID      int        `json:"task_id" gorm:"index:index_task_pocs_on_task_id;column:task_id;type:int(11)"`
	PocID       int        `json:"poc_id" gorm:"index:index_task_pocs_on_poc_id;column:poc_id;type:int(11)"`
	Filename    string     `json:"filename" gorm:"column:filename;type:varchar(255)" json:"filename"`
	TaskState   int        `json:"task_state" gorm:"column:task_state;type:int(11);default:0"`
	TaskPercent string     `json:"task_percent" gorm:"column:task_percent;type:varchar(10);default:0;comment:当前POC扫描进度"`
	CreatedAt   *time.Time `json:"created_at" gorm:"column:created_at;type:datetime;not null"`
	UpdatedAt   *time.Time `json:"updated_at" gorm:"column:updated_at;type:datetime;not null"`
}

// TableName Override table name.
func (m *TaskPoc) TableName() string {
	return TableNameOfTaskPoc
}
