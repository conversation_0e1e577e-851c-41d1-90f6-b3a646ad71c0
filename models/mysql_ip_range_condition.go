package models

import "time"

// Table-related constant definitions.
const (
	TableNameOfIPRangeCondition = "ip_range_conditions"
	TableDescOfIPRangeCondition = "IP段状况表(应该是多态关联表)"
)

const (
	IPRangeConditionableTypeOfTask = "Task"
)

const (
	IPRangeConditionableCategoryOfSpecial = "special"
)

// IPRangeConditions 生成数据的条件
type IPRangeConditions struct {
	ID                       uint      `gorm:"primaryKey;column:id;type:int(11) unsigned auto_increment;not null;comment:记录ID;"`
	ValId                    string    `gorm:"column:val_id;type:varchar(100);comment:其它关联类型数据ID" json:"val_id"`
	ValType                  string    `gorm:"column:val_type;type:varchar(255);comment:其它关联类型" json:"val_type"`
	Vals                     string    `gorm:"column:vals;type:varchar(100);comment:对应关联资源记录ID" json:"vals"`
	IPRangeConditionableType string    `gorm:"index:ip_range_conditionable_poly;column:ip_range_conditionable_type;type:varchar(255);comment:产生该记录的相对资源类型;" json:"ip_range_conditionable_type"`
	IPRangeConditionableID   uint      `gorm:"index:ip_range_conditionable_poly;column:ip_range_conditionable_id;type:int(11);comment:产生该记录的相对资源ID;" json:"ip_range_conditionable_id"`
	Category                 string    `gorm:"column:category;type:varchar(50);comment:资源类型一般用于产生该记录生成者指定生成" json:"category"`
	CreatedAt                time.Time `gorm:"column:created_at;type:datetime;not null;comment:添加时间;" json:"created_at"`
	UpdatedAt                time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间;" json:"updated_at"`
}

// TableName get sql table name.获取数据库表名
func (m *IPRangeConditions) TableName() string {
	return TableNameOfIPRangeCondition
}
