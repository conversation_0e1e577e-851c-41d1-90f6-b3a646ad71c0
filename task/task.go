package task

import (
	"context"
	"fmt"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/location"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/message"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/system"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"

	"github.com/olivere/elastic"
	"github.com/pkg/errors"

	"git.gobies.org/foeye-dependencies/embedsfs"
	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye-dependencies/sidekip"
)

const (
	RedisStringKeyOfTaskIncrease       = "task_increase"
	RedisStringKeyOfCurrentScanInfo    = "foeye_current_scan_info"
	RedisStringKeyOfQuickTransferState = "task_quick_transfer_state"
)

const (
	RedisHashKeyOfScanTask                     = "scan_task"
	RedisHashKeyOfScanTaskFieldWithTaskPCT     = "task_pct"
	RedisHashKeyOfScanTaskFieldWithOldProgress = "old_progress"
)

const (
	DefaultAllPort = `0-65535,U:53,67,69,80,88,113,123,137,138,161,162,391,427,443,500,520,623,626,705,853,1027,1194,1434,1604,1645,1701,1812,1900,1967,1993,2083,2094,2123,2152,2424,2425,2427,3283,3333,3391,3478,3671,3702,3784,4050,4070,4500,4800,5000,5001,5002,5004,5005,5006,5007,5008,5050,5060,5061,5093,5094,5095,5351,5353,5554,5632,5673,5683,6002,6003,6006,6060,6881,6969,7000,7001,7003,7005,8002,8888,9000,9100,9600,10001,17185,20000,28784,30310,30311,30312,30313,30718,32768,34962,34963,34964,44818,47808,48899,59110`
)

type Tasker interface {
	WatchQueueTask(ctx context.Context)
	ScannerProcess(ctx context.Context)
}

type WithWorkerartParams struct {
	Task     *models.Task `json:"task"`
	Poc      *models.Poc
	IPRanges []string
}

// Task task structure definition.
type Task struct {
	baseDir           string
	mockDataDir       string
	binDataDir        string
	DomainIpRelations map[string][]map[string]interface{}
	accessStatus      sync.RWMutex
	originalMessage   *message.OriginalMessage
	configure         *config.Configure
	factory           store.Factory
	queue             *sidekip.SideKip
	access            sync.RWMutex
	location          *location.Location
	domains           *sync.Map
	taskObj           *models.Task
}

// Queue Get the current task sidekip instance.
func (task *Task) Queue() *sidekip.SideKip {
	task.access.RLock()
	defer task.access.RUnlock()

	return task.queue
}

// IsOverflowAssetLimitNum Check asset limit number is overflow.
func (task *Task) IsOverflowAssetLimitNum(ctx context.Context, single *models.Task) (bool, error) {
	// Asset number.
	info, err := task.systemInfo()
	if err != nil {
		return false, err
	}
	logger.Infow("[TASK_ASSET_LIMIT]System info", "detail", info)

	count, err := task.factory.Elastic().Assets().Count(ctx)
	if err != nil {
		return false, err
	}

	unused := info.AssetLimitNum - count

	// Vulnerability
	if single.IsVulnerability() {
		logger.Infow("[TASK_ASSET_LIMIT]Current task is vulnerability,"+
			" asset limit validation will ignore",
			"limit", info.AssetLimitNum,
			"used", count,
			"unused", unused,
		)
		return false, nil
	}

	logger.Infow("[TASK_ASSET_LIMIT]Asset unused total",
		"limit", info.AssetLimitNum,
		"used", count,
		"unused", unused,
	)

	if unused <= 0 {
		return true, nil
	}

	return false, nil
}

func (task *Task) AssetUnused(ctx context.Context) (int, error) {
	info, err := task.systemInfo()
	if err != nil {
		return 0, errors.Wrap(err, "Get system info to failed")
	}

	count, err := task.factory.Elastic().Assets().Count(ctx)
	if err != nil {
		return 0, errors.Wrap(err, "Get assets indexes total to failed")
	}

	unused := info.AssetLimitNum - count
	return unused, nil
}

const (
	IndexNameOfAssets     = "fofaee_assets"
	IndexNameOfTaskAssets = "fofaee_task_assets"
	TypeNameOfCommon      = "ips"
)

func (task *Task) execute(ctx context.Context, tasks []*models.AssetDocument, migrate *models.MigrateToAsset) error {
	client := task.factory.Elastic().Client()
	bulk := client.Bulk()

	params := make([]interface{}, 0)

	unused, err := task.AssetUnused(ctx)
	if err != nil {
		return err
	}

	if unused > 0 {
		for _, item := range migrate.Inserts {
			doc := elastic.NewBulkIndexRequest().
				Index(IndexNameOfAssets).
				Type(TypeNameOfCommon).
				Id(item.IP).
				Doc(item)
			bulk.Add(doc)
		}
	}

	for _, item := range migrate.Updates {
		doc := elastic.NewBulkUpdateRequest().
			Index(IndexNameOfAssets).
			Type(TypeNameOfCommon).
			Id(item.IP).
			Doc(item)
		bulk.Add(doc)
	}

	for _, item := range tasks {
		id := fmt.Sprintf("%v_%s", item.TaskId, item.IP)
		doc := elastic.NewBulkUpdateRequest().
			Index(IndexNameOfTaskAssets).
			Type(TypeNameOfCommon).
			Id(id).
			Doc(item)
		bulk.Add(doc)
	}

	params = append(params, "numbers", bulk.NumberOfActions())
	response, err := bulk.Do(ctx)
	if err != nil {
		return errors.Wrap(err, "Bulk index to failed")
	}

	params = append(params, "failed", response.Failed())
	logger.Infow("[START@SYNC]Elastic index of batch information", params...)

	failed := response.Failed()
	if len(failed) > 0 {
		return fmt.Errorf("elastic index of batch operation has error, "+
			"indexed failure: %d", len(failed))
	}

	return nil
}

func (task *Task) systemInfo() (*exchange.SystemInfo, error) {
	url := system.GetMicroKernelRequestUrlForSystemInfo()

	systemInfo, err := system.GetSystemInfoWithUrl(url, task.isMock())
	if err != nil {
		return nil, err
	}
	return systemInfo, nil
}

func (task *Task) isMock() bool {
	return task.configure.System.RunMode == config.RunModeOfDevelop
}

// NewTask initialize task instance.
func NewTask(baseDir string, config *config.Configure, factory store.Factory, queue *sidekip.SideKip, fs *embedsfs.EmbedsFS) *Task {
	path, err := fsfire.GetFilePathWithFileSystemPath(baseDir, fsfire.WithSpecificFileSystemPath("test/data/task"))
	if err != nil {
		panic(any(err))
	}

	loc, err := location.NewLocationOr(fs)
	if err != nil {
		panic(any(err))
	}

	t := &Task{
		baseDir:     baseDir,
		mockDataDir: path,
		configure:   config,
		factory:     factory,
		queue:       queue,
		location:    loc,
	}

	return t
}
