package task

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/jrallison/go-workers"
	"github.com/olivere/elastic"
	"github.com/pkg/errors"
	"github.com/thoas/go-funk"

	"git.gobies.org/foeye-dependencies/interside"
	"git.gobies.org/foeye-dependencies/logger"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/batch"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/ipparser"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/location"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/message"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/queue"
	elastic2 "git.gobies.org/foeye/foeye-engine-syncasset/store/elastic"
)

// SyncAssetStart watch queue task, will dispatch to the internal processing queue.
func (task *Task) SyncAssetStart(ctx context.Context) {
	logger.Infow("[START]watch queue be ready")
	task.queue.Process(queue.NameOfAssetSyncStart.String(), task.start(ctx), queue.Concurrency)
}

func (task *Task) start(ctx context.Context) func(msg *workers.Msg) {
	return func(msg *workers.Msg) {
		logger.Infow("[START]watch queue received message", "args", msg.Args())

		data, err := msg.Args().MarshalJSON()
		if err != nil {
			desc := "[START]watch marshal received message to failed"
			logger.Warnw(desc, "error", err)
			return
		}

		var upstream *message.UpstreamTask
		if err = json.Unmarshal(data, &upstream); err != nil {
			desc := "[START]watch unmarshal received upstream message to failed"
			logger.Warnw(desc, "error", err)
			return
		}
		// 从subdomain里获取爬虫解析的ip,加入到
		task.getTaskAssetDomainIpsToUpstream(upstream)

		args, err := msg.Args().Map()
		if err != nil {
			desc := "[START]watch marshal received message to map failed"
			logger.Warnw(desc, "error", err)
			return
		}

		start := time.Now()
		logger.Infow("[START]Assets sync starting...", "task_id", upstream.TaskId)
		err = task.sync(ctx, upstream)
		if err != nil {
			logger.Warnw("[START]watch scheduling to failed", "error", err)
		}

		logger.Infow("[START]assets sync finished ...", "task_id", upstream.TaskId, "use", time.Now().Sub(start).String())
		task.finished(err, args)
	}
}

func (task *Task) finished(err error, args map[string]interface{}) {
	name := queue.NameOfBrokerCompleted.String()
	class := queue.ClassOfCommentTask.String()

	enqueue, err := task.queue.Enqueue(name, class, args)
	if err != nil {
		logger.Warnw(fmt.Sprintf("Write task message into queue %s to failed", name), "error", err)
		return
	}
	logger.Infow(fmt.Sprintf("Write task message into queue %s to success", name), "id", enqueue, "message", args)
}

func (task *Task) sync(ctx context.Context, upstream *message.UpstreamTask) error {
	taskId := upstream.TaskId

	// Will sync task asset total number.
	total, err := task.factory.Elastic().TaskAssets().CountByTaskId(ctx, taskId)
	if err != nil {
		return err
	}

	batchSize := task.configure.System.BatchSize
	batchTotal := batch.Calc(total, batchSize)
	logger.Infow("[START@SYNC]Batch calc", "data_total", total, "batch_total", batchTotal, "batch_size", batchSize)

	taskObj, err := task.factory.MYSQL().IPRange().GetTaskWithId(ctx, taskId)
	if err != nil {
		logger.Warnw("[START@SYNC]GetTaskWithId failed", "error", err)
	}
	task.taskObj = taskObj
	domainRelations := ConvertDomainIpRelations(upstream.DomainIpRelations)

	// 获取所有ip段、域名及对应的cidr
	ipRangeCidrs, err := task.factory.MYSQL().IPRange().GetIPRangeCIDR(int(taskObj.IPType), domainRelations)

	if err = task.domain(ctx, domainRelations); err != nil {
		logger.Warnw("[START@SYNC]task.domain to failed", "error", err)
	}

	task.DomainIpRelations = upstream.DomainIpRelations

	for i := 0; i < batchTotal; i++ {
		start := batch.Start(i, batchSize)
		task.migration(ctx, taskId, start, batchSize, ipRangeCidrs)
	}

	Tags := task.factory.MYSQL().IPRange().GetDomainTags(task.getAllDomains())
	// 资产数据同步完成后，统一处理域名相关逻辑！
	task.clearDomainForAsset()
	task.processingDomainAsset(ctx, Tags)
	task.processingTaskDomainAsset(Tags, taskId)
	// 最后步骤同步域名总库中的域名到资产总库的 hosts 中
	task.syncDomainToAssetHosts()

	return nil
}

func ConvertDomainIpRelations(domainIpRelations map[string][]map[string]interface{}) map[string][]string {
	result := make(map[string][]string)
	for domain, entries := range domainIpRelations {
		for _, entry := range entries {
			ip, ok := entry["ip"].(string)
			if !ok {
				continue // 跳过无效数据
			}
			result[domain] = append(result[domain], ip)
		}
	}

	return result
}

func (task *Task) domain(ctx context.Context, domainIps map[string][]string) error {
	task.domains = &sync.Map{}
	ipDomains := map[string][]string{}

	if len(domainIps) > 0 {
		for domain, ips := range domainIps {
			for _, ip := range ips {
				ipDomains[ip] = append(ipDomains[ip], domain)
			}
		}
		for ip, domains := range ipDomains {
			hostArr := funk.UniqString(domains)
			logger.Infow("[TASK@DOMAIN] Domains", "hostArr", hostArr)
			logger.Infow("[TASK@DOMAIN] Domains", "ip", ip)

			if len(hostArr) > 0 && ip != "" {
				task.domains.Store(ip, hostArr)
			}
		}
	}

	logger.Infow("[TASK@DOMAIN] Domains", "info", task.domains)
	return nil
}

func (task *Task) migration(ctx context.Context, taskId int, start int, batchSize int, ipRangeCidrs []*models.IPRangeCidr) error {
	var tasks []*models.AssetDocument

	taskAssets, err := task.factory.Elastic().TaskAssets().GetListAssignToAssets(ctx, taskId, start, batchSize)
	if err != nil {
		logger.Warnw("[START@SYNC]Get task asset data to failed", "error", err)
		return err
	}

	// IPs
	var ips = taskAssets.IPsForContainer()
	if ips == nil {
		logger.Warnw("[START@SYNC]Get task asset IPs to failed", "error", err)
		return errors.New("Get task asset IPs to failed")
	}

	// Assets
	assets, err := task.factory.Elastic().Assets().List(ctx, ips)
	if err != nil {
		logger.Warnw("[START@SYNC]Get asset data to failed", "error", err)
	}

	logger.Warnw("[START@SYNC]GetIpTags params for ips ", "ips", ips)
	logger.Warnw("[START@SYNC]GetIpTags params for ipRangeCidrs ", "ipRangeCidrs", ipRangeCidrs)
	ipTag, err := task.factory.MYSQL().IPRange().GetIpTags(ips.StringSlice(), ipRangeCidrs)
	if err != nil {
		logger.Warnw("[START@SYNC]Get ip tag data to failed", "error", err)
	}
	logger.Warnw("[START@SYNC]Get ip tag data", "ipTag", ipTag)

	// Processing task asset data.
	tasks = task.processingTaskAsset(ctx, taskId, taskAssets, ipTag)
	migrate := task.processingAsset(ctx, taskId, taskAssets, assets, ipTag)

	err = task.execute(ctx, tasks, migrate)
	if err != nil {
		logger.Warnw("[START@SYNC]Execute migrate data to failed", "error", err)
	}
	return err
}

// processingAsset处理总库数据
func (task *Task) processingAsset(ctx context.Context, taskId int, taskAssets models.AssetDocuments, assets *sync.Map, ipTag map[string]*models.IPRangeTags) *models.MigrateToAsset {
	var migrate = &models.MigrateToAsset{}
	var category = models.TaskCategory(task.taskObj.Category)

	ips := task.MergeTaskAssetInfo(taskAssets, ipTag, assets, category, migrate)

	// 获取资产的垃圾网站标识
	fraudCharsets, err := task.factory.Elastic().Subdomain().GetFraudAndCharsetByIps(ips)
	if err != nil {
		logger.Warnw("[START@SYNC]Get fraud and charset fofaee_subdomain to failed", "error", err)
	}
	for _, doc := range migrate.Updates {
		for i, port := range doc.PortList {
			if v, ok := fraudCharsets[fmt.Sprintf("%s:%d", doc.IP, port.Port)]; ok {
				doc.PortList[i].FraudName = v.FraudName
				doc.PortList[i].IsFraud = v.IsFraud
				doc.PortList[i].Charset = v.Charset
			}
		}
	}
	for _, doc := range migrate.Inserts {
		for i, port := range doc.PortList {
			if v, ok := fraudCharsets[fmt.Sprintf("%s:%d", doc.IP, port.Port)]; ok {
				doc.PortList[i].FraudName = v.FraudName
				doc.PortList[i].IsFraud = v.IsFraud
				doc.PortList[i].Charset = v.Charset
			}
		}
	}
	for _, doc := range migrate.Updates {
		for _, port := range doc.PortList {
			if doc.IsHoneypot {
				break
			} else {
				isHoneyBot, _ := port.IsHoneypot.(bool)
				doc.IsHoneypot = doc.IsHoneypot || isHoneyBot
			}
		}
		for _, title := range doc.PortList {
			if doc.IsFraud {
				break
			} else {
				doc.IsFraud = doc.IsFraud || title.IsFraud
			}
		}
		// 如果是ipv6,就把原始ipv6地址添加到新字段
		setIpv6Raw(doc, ipTag)
	}
	for _, doc := range migrate.Inserts {
		for _, port := range doc.PortList {
			if doc.IsHoneypot {
				break
			} else {
				isHoneyBot, _ := port.IsHoneypot.(bool)
				doc.IsHoneypot = doc.IsHoneypot || isHoneyBot
			}
		}
		for _, title := range doc.PortList {
			if doc.IsFraud {
				break
			} else {
				doc.IsFraud = doc.IsFraud || title.IsFraud
			}
		}
		// 如果是ipv6,就把原始ipv6地址添加到新字段
		setIpv6Raw(doc, ipTag)
	}

	return migrate
}

func (task *Task) MergeTaskAssetInfo(taskAssets models.AssetDocuments, ipTag map[string]*models.IPRangeTags, assets *sync.Map, category models.TaskCategory, migrate *models.MigrateToAsset) []string {
	ips := make([]string, 0)
	// mosso.DebugShowContentWithJSON(assets)
	for _, taskAsset := range taskAssets {
		tag, _ := ipTag[taskAsset.IP]

		loc, err := task.location.City().FindLocation(taskAsset.IP)
		if err != nil {
			logger.Warnw("[START@SYNC]Parse iprange location to failed", "error", err)
		} else {
			//logger.Infow("[START@SYNC]Parse iprange location to success", "ip", taskAsset.IP)
		}

		//logger.Infow("[START@SYNC]Will sync task asset", "ip", taskAsset.IP)
		iprangeAddWay := models.AddWayOfSelfDefined
		load, ok := assets.Load(taskAsset.IP)
		// mosso.DebugShowContentWithJSON(load)
		if ok {
			founded, ok := load.(models.AssetDocument)
			if !ok {
				logger.Warnw("[START@SYNC]Receiver UN-AssetDocument data will ignore")
				continue
			}

			founded.LastUpdateTime = taskAsset.LastUpdateTime
			founded.Ports = task.mergePorts(founded, taskAsset)
			founded.Protocols = task.mergeProtocols(founded, taskAsset)
			founded.PortList = task.mergePortList(founded, taskAsset)
			founded.TitleList = task.mergeTitleList(founded, taskAsset)
			// 更新port_size
			founded.PortSize = len(founded.Ports)

			netIP := net.ParseIP(founded.IP)
			if !founded.IsIpv6 {
				founded.IPBNet = netIP.Mask(net.IPv4Mask(0xff, 0xff, 0, 0)).String() + "/16"
				founded.IPCNet = netIP.Mask(net.IPv4Mask(0xff, 0xff, 0xff, 0)).String() + "/24"
			}

			if tag == nil {
				founded.ComputerRoom = ""
				founded.Username = ""
				founded.Company = ""
				founded.ManagerEmail = ""
				founded.ManagerMobile = ""
				founded.AssetLevel = ""
				founded.BusinessApp = ""
				founded.CustomNames = nil
				founded.CustomFields = nil
				founded.AddWay = iprangeAddWay
				founded.LastCheckTime = founded.LastUpdateTime
			} else {
				founded.ComputerRoom = tag.ComputerRoom
				founded.Username = tag.UserName
				founded.ManagerEmail = tag.ManagerEmail
				founded.ManagerMobile = tag.ManagerMobile
				founded.Company = tag.Company
				founded.BusinessApp = tag.BusinessApp
				founded.AssetLevel = tag.AssetLevel
				founded.CustomFields = tag.CustomFields
				founded.CustomNames = tag.CustomNames
				founded.AddWay = iprangeAddWay
				founded.LastCheckTime = founded.LastUpdateTime
			}

			// Update mac information
			founded.Mac = taskAsset.Mac
			// 增加信创标识
			founded.IsXc = taskAsset.IsXc

			// Update position information
			task.position(loc, &founded, tag)
			if category.IsFlow() {
				founded.AddWay = models.ElasticAssetAddWayOfFlow
			}

			// IPRange reference host
			domain, exists := task.domains.Load(founded.IP)
			if exists {
				founded.Hosts = domain
				if domains, ok := domain.([]string); ok {
					founded.Host = strings.Join(funk.UniqString(domains), ",")
				} else {
					fmt.Println("domain is not of type []string")
					founded.Host = ""
				}
			}

			// 资产存活状态
			founded.State = taskAsset.State

			founded.Createtime = nil
			migrate.AppendUpdates(&founded)
			ips = append(ips, founded.IP)
		} else {
			insert := taskAsset
			insert.City = loc.City
			if tag == nil {
				insert.ComputerRoom = ""
				insert.Username = ""
				insert.ManagerEmail = ""
				insert.ManagerMobile = ""
				insert.Company = ""
				insert.BusinessApp = ""
				insert.CustomNames = nil
				insert.CustomFields = nil
				insert.LastCheckTime = taskAsset.LastUpdateTime
				insert.AddWay = iprangeAddWay
			} else {
				// insert.TaskId = nil
				insert.ComputerRoom = tag.ComputerRoom
				insert.Username = tag.UserName
				insert.ManagerEmail = tag.ManagerEmail
				insert.ManagerMobile = tag.ManagerMobile
				insert.Company = tag.Company
				insert.BusinessApp = tag.BusinessApp
				insert.CustomFields = tag.CustomFields
				insert.CustomNames = tag.CustomNames
				insert.LastCheckTime = taskAsset.LastUpdateTime
				insert.AddWay = iprangeAddWay
			}

			netIP := net.ParseIP(insert.IP)
			if !insert.IsIpv6 {
				insert.IPBNet = netIP.Mask(net.IPv4Mask(0xff, 0xff, 0, 0)).String() + "/16"
				insert.IPCNet = netIP.Mask(net.IPv4Mask(0xff, 0xff, 0xff, 0)).String() + "/24"
			}

			// Update position information
			task.position(loc, insert, tag)
			if category.IsFlow() {
				insert.AddWay = models.ElasticAssetAddWayOfFlow
			}

			// IPRange reference host
			domain, exists := task.domains.Load(insert.IP)
			if exists {
				insert.Hosts = domain
				if domains, ok := domain.([]string); ok {
					insert.Host = strings.Join(funk.UniqString(domains), ",")
				} else {
					insert.Host = ""
				}
			}

			migrate.AppendInserts(insert)
			ips = append(ips, insert.IP)
		}
	}
	return ips
}

func setIpv6Raw(doc *models.AssetDocument, ipTag map[string]*models.IPRangeTags) {
	if strings.Contains(doc.IP, ":") && ipTag[doc.IP] != nil {
		ip := net.ParseIP(ipTag[doc.IP].IpRange)
		if ip != nil && doc.IP == ip.String() {
			doc.Ipv6Raw = ipTag[doc.IP].IpRange
		}
	}
}

func (task *Task) position(loc ipparser.Location, document *models.AssetDocument, tag *models.IPRangeTags) {
	if loc.Country == location.CountryOfLocalAreaNetwork {
		document.Country = location.CountryDefaultOfChina
	} else {
		document.Country = loc.Country
	}

	if loc.Province == location.CountryOfLocalAreaNetwork {
		document.Province = ""
	} else {
		document.Province = loc.Province
	}

	if loc.Province == location.CountryOfLocalAreaNetwork {
		document.City = ""
	} else {
		document.City = loc.City
	}

	if tag != nil && tag.Province != "" {
		document.Province = tag.Province
	}
	if tag != nil && tag.City != "" {
		document.City = tag.City
	}
}

func (task *Task) host(founded models.AssetDocument) string {
	logger.Infof("[START@SYNC]Hosts: %+v, type: %T", founded.Hosts, founded.Hosts)
	var hosts string

	var values, ok = founded.Hosts.([]interface{})
	if ok {
		var container = interside.NewContainer()
		container.Append(values...)
		slice := container.StringSlice()
		hosts = strings.Join(slice, ",")
	}

	logger.Infof("[START@SYNC]Hosts processed: %v", hosts)
	return hosts
}

// processingTaskAsset processing task asset
func (task *Task) processingTaskAsset(ctx context.Context, taskId int, taskAssets models.AssetDocuments, ipTag map[string]*models.IPRangeTags) models.AssetDocuments {
	var docs = make([]*models.AssetDocument, 0, len(taskAssets))
	var category = models.TaskCategory(task.taskObj.Category)

	ips := make([]string, 0)
	for _, asset := range taskAssets {
		tag, _ := ipTag[asset.IP]

		//logger.Infow("[START@SYNC]Get iprange info", "task_id", taskId, "tag", tag, "ip", asset.IP)

		loc, err := task.location.City().FindLocation(asset.IP)
		if err != nil {
			logger.Warnw("[START@SYNC]Parse iprange location to failed", "error", err)
		} else {
			//logger.Infow("[START@SYNC]Parse iprange location to success", "ip", asset.IP)
		}
		//logger.Infow("[START@SYNC]Receiver task asset", "doc", asset.IP)

		// 更新任务索引城市，标签等数据
		if tag == nil {
			asset.ComputerRoom = ""
			asset.Username = ""
			asset.ManagerEmail = ""
			asset.ManagerMobile = ""
			asset.Company = ""
			asset.BusinessApp = ""
			asset.AssetLevel = ""
			asset.CustomNames = nil
			asset.CustomFields = nil
			asset.AddWay = models.AddWayOfSelfDefined
			asset.LastCheckTime = asset.LastUpdateTime
		} else {
			asset.ComputerRoom = tag.ComputerRoom
			asset.Username = tag.UserName
			asset.ManagerEmail = tag.ManagerEmail
			asset.ManagerMobile = tag.ManagerMobile
			asset.Company = tag.Company
			asset.BusinessApp = tag.BusinessApp
			asset.AssetLevel = tag.AssetLevel

			asset.CustomFields = tag.CustomFields
			asset.CustomNames = tag.CustomNames
			asset.AddWay = tag.AddWay
			asset.LastCheckTime = asset.LastUpdateTime
		}

		domain, exists := task.domains.Load(asset.IP)
		if exists {
			asset.Hosts = domain
			if domains, ok := domain.([]string); ok {
				asset.Host = strings.Join(funk.UniqString(domains), ",")
			} else {
				asset.Host = ""
			}
		}

		task.position(loc, asset, tag)
		if category.IsFlow() {
			asset.AddWay = models.ElasticAssetAddWayOfFlow
		}

		docs = append(docs, asset)
		ips = append(ips, asset.IP)
	}

	// 获取资产的垃圾网站标识和charset
	fraudCharsets, err := task.factory.Elastic().Subdomain().GetFraudAndCharsetByIps(ips)
	if err != nil {
		logger.Errorw("[START@SYNC]Get fraud and charset fofaee_subdomain to failed", "error", err)
	}
	for _, doc := range docs {
		for i, port := range doc.PortList {
			if v, ok := fraudCharsets[fmt.Sprintf("%s:%d", doc.IP, port.Port)]; ok {
				doc.PortList[i].FraudName = v.FraudName
				doc.PortList[i].IsFraud = v.IsFraud
				doc.PortList[i].Charset = v.Charset
			}
		}
	}
	for _, doc := range docs {
		for _, port := range doc.PortList {
			if doc.IsHoneypot {
				break
			} else {
				isHoneyBot, _ := port.IsHoneypot.(bool)
				doc.IsHoneypot = doc.IsHoneypot || isHoneyBot
			}
		}
		for _, title := range doc.PortList {
			if doc.IsFraud {
				break
			} else {
				doc.IsFraud = doc.IsFraud || title.IsFraud
			}
		}
		// 如果是ipv6,就把原始ipv6地址添加到新字段
		setIpv6Raw(doc, ipTag)
	}

	return docs
}

// processingTaskDomainAsset
// @Summary 处理任务域名资产
func (task *Task) processingTaskDomainAsset(ipTag map[string]*models.IPRangeTags, taskId int) bool {
	var docs = make([]*models.DomainDocument, 0)
	client := task.factory.Elastic().Client()
	bulk := client.Bulk()
	params := make([]interface{}, 0)

	for domain, resolutions := range task.DomainIpRelations {
		domainAsset := &models.DomainDocument{
			TaskId:     taskId,
			Domain:     domain,
			Ip:         domain, // 兼容与资产索引进行联合删除的情况，如果删除的主键不一致，会导致删除失败。
			Resolution: task.formatDomainIpRelations(resolutions),
		}

		tag, _ := ipTag[domain]

		if tag == nil {
			domainAsset.ComputerRoom = ""
			domainAsset.Username = ""
			domainAsset.ManagerEmail = ""
			domainAsset.ManagerMobile = ""
			domainAsset.Company = ""
			domainAsset.BusinessApp = ""
			domainAsset.AssetLevel = ""
			domainAsset.CustomNames = nil
			domainAsset.CustomFields = nil
			domainAsset.AddWay = models.AddWayOfSelfDefined
			domainAsset.LastCheckTime = time.Now().Format("2006-01-02 15:04:05")
			domainAsset.LastUpdateTime = time.Now().Format("2006-01-02 15:04:05")
		} else {
			domainAsset.ComputerRoom = tag.ComputerRoom
			domainAsset.Username = tag.UserName
			domainAsset.ManagerEmail = tag.ManagerEmail
			domainAsset.ManagerMobile = tag.ManagerMobile
			domainAsset.Company = tag.Company
			domainAsset.BusinessApp = tag.BusinessApp
			domainAsset.AssetLevel = tag.AssetLevel
			domainAsset.CustomFields = tag.CustomFields
			domainAsset.CustomNames = tag.CustomNames
			domainAsset.AddWay = tag.AddWay
			domainAsset.LastCheckTime = time.Now().Format("2006-01-02 15:04:05")
			domainAsset.LastUpdateTime = time.Now().Format("2006-01-02 15:04:05")
		}

		docs = append(docs, domainAsset)
	}

	// TODO 暂未规划 域名资产对应的端口信息处理方式，如果一个域名多个IP，无规划如何填充端口信息。
	//for _, doc := range docs {
	//	for _, port := range doc.PortList {
	//		if doc.IsHoneypot {
	//			break
	//		} else {
	//			isHoneyBot, _ := port.IsHoneypot.(bool)
	//			doc.IsHoneypot = doc.IsHoneypot || isHoneyBot
	//		}
	//	}
	//	for _, title := range doc.PortList {
	//		if doc.IsFraud {
	//			break
	//		} else {
	//			doc.IsFraud = doc.IsFraud || title.IsFraud
	//		}
	//	}
	//
	//	// 如果是ipv6,就把原始ipv6地址添加到新字段
	//	setIpv6RawForDomain(doc, ipTag)
	//}

	for _, item := range docs {
		id := fmt.Sprintf("%v_%s", item.TaskId, item.Domain)
		doc := elastic.NewBulkIndexRequest().
			Index(elastic2.IndexNameOfTaskDomainAssets).
			Type(TypeNameOfCommon).
			Id(id).
			Doc(item)
		bulk.Add(doc)
	}

	if len(docs) == 0 {
		return false
	}

	params = append(params, "numbers", bulk.NumberOfActions())
	response, err := bulk.Do(context.TODO())
	if err != nil {
		return false
	}

	params = append(params, "failed", response.Failed())
	logger.Infow("[START@SYNC]Elastic index of batch information", params...)

	failed := response.Failed()
	if len(failed) > 0 {
		logger.Infow("elastic index of batch operation has error, "+
			"indexed failure: %d", len(failed))

		return true
	}

	return true
}

// processingDomainAsset
// @Summary 处理域名总库资产
func (task *Task) processingDomainAsset(ctx context.Context, ipTag map[string]*models.IPRangeTags) bool {
	var docs = make([]*models.DomainDocument, 0)
	client := task.factory.Elastic().Client()
	bulk := client.Bulk()
	params := make([]interface{}, 0)

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("domain", task.getAllDomains()...),
	)
	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("domain")
	res, err := client.Search().Index(elastic2.IndexNameOfDomainAssets).Type(elastic2.TypeNameOfDomainAssets).FetchSourceContext(fetchSourceContext).Size(60000).Query(query).Do(ctx)
	if err != nil {
		logger.Warnw("[models@SYNC]GetDomainIpsByDomains info", "client.Search() err  ", err.Error())
	}

	// 创建一个 map 来保存域名
	domainMap := make(map[string]bool)
	// 遍历查询结果
	if res != nil && res.Hits != nil && len(res.Hits.Hits) > 0 {
		for _, hit := range res.Hits.Hits {
			// 通过 hit.Source 获取原始文档内容
			var doc map[string]interface{}
			if err := json.Unmarshal(*hit.Source, &doc); err != nil {
				logger.Warnw("[models@SYNC]GetDomainIpsByDomains info", "Error unmarshalling document:  ", err.Error())
				continue
			}

			// 提取 domain 字段
			if domain, ok := doc["domain"].(string); ok {
				domainMap[domain] = true
			}
		}
	}

	// 在入库前，清理域名资产，防止存在冗余数据。
	for domain, resolutions := range task.DomainIpRelations {
		domainAsset := &models.DomainDocument{
			Domain:     domain,
			Ip:         domain, // 兼容与资产索引进行联合删除的情况，如果删除的主键不一致，会导致删除失败。
			Resolution: task.formatDomainIpRelations(resolutions),
		}

		tag, _ := ipTag[domain]

		if tag == nil {
			domainAsset.ComputerRoom = ""
			domainAsset.Username = ""
			domainAsset.ManagerEmail = ""
			domainAsset.ManagerMobile = ""
			domainAsset.Company = ""
			domainAsset.BusinessApp = ""
			domainAsset.AssetLevel = ""
			domainAsset.CustomNames = nil
			domainAsset.CustomFields = nil
			domainAsset.AddWay = models.AddWayOfSelfDefined
			domainAsset.LastCheckTime = time.Now().Format("2006-01-02 15:04:05")
			domainAsset.LastUpdateTime = time.Now().Format("2006-01-02 15:04:05")
		} else {
			domainAsset.ComputerRoom = tag.ComputerRoom
			domainAsset.Username = tag.UserName
			domainAsset.ManagerEmail = tag.ManagerEmail
			domainAsset.ManagerMobile = tag.ManagerMobile
			domainAsset.Company = tag.Company
			domainAsset.BusinessApp = tag.BusinessApp
			domainAsset.AssetLevel = tag.AssetLevel
			domainAsset.CustomFields = tag.CustomFields
			domainAsset.CustomNames = tag.CustomNames
			domainAsset.AddWay = tag.AddWay
			domainAsset.LastCheckTime = time.Now().Format("2006-01-02 15:04:05")
			domainAsset.LastUpdateTime = time.Now().Format("2006-01-02 15:04:05")
		}

		docs = append(docs, domainAsset)
	}

	for _, item := range docs {
		id := fmt.Sprintf("%s", item.Domain)

		d, _ := json.Marshal(domainMap)
		logger.Warnw("[models@SYNC]GetDomainIpsByDomains info", "domainMap  ", string(d))
		logger.Warnw("[models@SYNC]GetDomainIpsByDomains info", "item.Domain  ", item.Domain)

		if domainMap[item.Domain] {
			doc := elastic.NewBulkUpdateRequest().
				Index(elastic2.IndexNameOfDomainAssets).
				Type(TypeNameOfCommon).
				Id(id).
				Doc(item)
			bulk.Add(doc)
		} else {
			item.Createtime = time.Now().Format("2006-01-02 15:04:05")

			doc := elastic.NewBulkIndexRequest().
				Index(elastic2.IndexNameOfDomainAssets).
				Type(TypeNameOfCommon).
				Id(id).
				Doc(item)
			bulk.Add(doc)
		}
	}

	if len(docs) == 0 {
		return false
	}

	params = append(params, "numbers", bulk.NumberOfActions())
	response, err := bulk.Do(ctx)
	if err != nil {
		return false
	}

	params = append(params, "failed", response.Failed())
	logger.Infow("[START@SYNC]Elastic index of batch information", params...)

	failed := response.Failed()
	if len(failed) > 0 {
		logger.Infow("elastic index of batch operation has error, "+
			"indexed failure: %d", len(failed))

		return true
	}

	return true
}

func (task *Task) formatDomainIpRelations(resolutions []map[string]interface{}) []models.Resolution {
	var entries []models.Resolution
	for _, entry := range resolutions {
		e := models.Resolution{}
		eByte, _ := json.Marshal(entry)
		_ = json.Unmarshal(eByte, &e)

		entries = append(entries, e)
	}

	return entries
}

func (task *Task) getAllDomains() []interface{} {
	var domains []interface{}

	for domain := range task.DomainIpRelations {
		domains = append(domains, domain)
	}

	return domains
}

func (task *Task) getTaskAssetDomainIpsToUpstream(upstream *message.UpstreamTask) {
	domains := make([]string, 0)
	for domain, _ := range upstream.DomainIpRelations {
		domains = append(domains, domain)
	}

	domainIps, err := task.factory.Elastic().TaskAssets().GetIpsByDomains(domains, upstream.TaskId)
	if err != nil {
		logger.Errorw("[getSubdomainIpsToArgs@SYNC]GetDomainIpsByDomains failed", "error", err)
		return
	}
	for domain, ips := range domainIps {
		if v, ok := upstream.DomainIpRelations[domain]; ok {
			oldIps := make([]string, len(v))
			for i, dr := range v {
				oldIps[i] = dr["ip"].(string)
			}
			_, ipNewArr := funk.DifferenceString(oldIps, ips)
			for _, ip := range ipNewArr {
				typeName := "A"
				if strings.Contains(ip, ":") {
					typeName = "AAAA"
				}
				ipMap := map[string]interface{}{
					"ip":   ip,
					"rtt":  23000,
					"ttl":  61,
					"type": typeName,
				}
				upstream.DomainIpRelations[domain] = append(upstream.DomainIpRelations[domain], ipMap)
			}
		}
	}
	logger.Errorw("DomainIpRelations", "new value", upstream.DomainIpRelations)
}

// clearDomainForAsset
// @Summary 每次同步资产前，先将资产总库中所有资产的 hosts 和 host 字段设置为 nil
func (task *Task) clearDomainForAsset() {
	client := task.factory.Elastic().Client()
	// 构建查询条件
	query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())

	// 构建脚本，将 `hosts` 和 `host` 字段设置为 nil
	script := elastic.NewScriptInline(`
    if (ctx._source.containsKey('hosts')) {
        ctx._source.hosts = null;
		ctx._source.host = null;
    }
`)

	// 执行 UpdateByQuery
	response, err := client.UpdateByQuery(elastic2.IndexNameOfAssets).Type(TypeNameOfCommon).
		Query(query).
		Script(script).
		Refresh("true"). // 确保实时刷新
		Do(context.TODO())

	if err != nil {
		logger.Errorw("[clearDomainForAsset@SYNC]Error updating documents", "error", err)
	} else {
		logger.Errorw("[clearDomainForAsset@SYNC] Updated documents successfully. ", "response.Updated", response.Updated)
	}
}

// syncDomainToAssetHosts
// @Summary 同步域名到资产的 hosts 和 host 字段
func (task *Task) syncDomainToAssetHosts() bool {
	client := task.factory.Elastic().Client()
	// 查询所有 `fofaee_domain_assets` 数据
	domainQuery := elastic.NewMatchAllQuery()
	scroll := client.Scroll(elastic2.IndexNameOfDomainAssets).
		Type(TypeNameOfCommon).
		Query(domainQuery).
		Size(1000) // 每次拉取1000条
	defer scroll.Clear(context.TODO())

	// 存储域名和 IP 的映射
	ipToDomains := make(map[string][]string)

	// 遍历 scroll 查询结果
	for {
		results, err := scroll.Do(context.TODO())

		if err == io.EOF {
			logger.Warnw("[syncDomainToAssetHosts@SYNC]scroll.Do(context.TODO())", "io.EOF ", err.Error())
			break
		}

		if err != nil {
			logger.Warnw("[syncDomainToAssetHosts@SYNC]Error scrolling domain assets ", "scrolling err  ", err.Error())
		}

		for _, hit := range results.Hits.Hits {
			var domainAsset struct {
				Domain     string `json:"domain"`
				Resolution []struct {
					IP string `json:"ip"`
				} `json:"resolution"`
			}

			// 解引用 *json.RawMessage
			if err := json.Unmarshal(*hit.Source, &domainAsset); err != nil {
				logger.Warnw("[syncDomainToAssetHosts@SYNC] ", "Error unmarshalling domain asset:  ", err.Error())
				continue
			}

			// 将域名与解析的 IP 关联
			for _, resolution := range domainAsset.Resolution {
				ip := resolution.IP
				ipToDomains[ip] = append(ipToDomains[ip], domainAsset.Domain)
			}
		}
	}

	// 去重 IP -> Domains 的映射
	for ip, domains := range ipToDomains {
		uniqueDomains := make(map[string]struct{})
		for _, domain := range domains {
			uniqueDomains[domain] = struct{}{}
		}

		// 转换为去重后的切片
		ipToDomains[ip] = make([]string, 0, len(uniqueDomains))
		for domain := range uniqueDomains {
			ipToDomains[ip] = append(ipToDomains[ip], domain)
		}
	}

	// 使用并发批量更新 `fofaee_assets`
	var wg sync.WaitGroup
	batchSize := 100 // 每批更新100条文档
	ipChan := make(chan string, len(ipToDomains))

	// 启动固定数量的 worker
	workerCount := 10
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for ip := range ipChan {
				updateAssets(client, ip, ipToDomains[ip], batchSize)
			}
		}()
	}

	// 分发 IP
	for ip := range ipToDomains {
		ipChan <- ip
	}
	close(ipChan)

	// 等待所有更新完成
	wg.Wait()

	logger.Warnw("[syncDomainToAssetHosts@SYNC] ", "info  ", "All updates completed!")
	return true
}

func updateAssets(client *elastic.Client, ip string, domains []string, batchSize int) bool {
	// 查找匹配 IP 的 `fofaee_assets`
	query := elastic.NewTermQuery("ip.ip_raw", ip)
	searchResult, err := client.Search().
		Index(IndexNameOfAssets).
		Type(TypeNameOfCommon).
		Query(query).
		Size(batchSize). // 一次拉取 batchSize 个文档
		Do(context.TODO())
	if err != nil {
		logger.Warnw("[updateAssets@SYNC] ", "Error searching assets for IP  ", ip, "  err: ", err)
		return false
	}

	// 批量更新文档
	bulkRequest := client.Bulk()
	for _, hit := range searchResult.Hits.Hits {
		updateScript := elastic.NewScriptInline(`
			if (ctx._source.containsKey('hosts')) {
				if (ctx._source.hosts == null) {
					ctx._source.hosts = [];
				}
				for (domain in params.domains) {
					if (!ctx._source.hosts.contains(domain)) {
						ctx._source.hosts.add(domain);
					}
				}
				ctx._source.host = String.join(",", ctx._source.hosts);
			} else {
				ctx._source.hosts = params.domains;
				ctx._source.host = String.join(",", params.domains);
			}
		`).Param("domains", domains)

		logger.Warnw("[updateAssets@SYNC] ", "hit.Id ==  ", hit.Id)
		request := elastic.NewBulkUpdateRequest().
			Index(IndexNameOfAssets).
			Type(TypeNameOfCommon).
			Id(hit.Id).
			Script(updateScript)
		bulkRequest = bulkRequest.Add(request)
	}

	// 执行批量更新
	if bulkRequest.NumberOfActions() > 0 {
		_, err := bulkRequest.Do(context.TODO())
		if err != nil {
			logger.Warnw("[updateAssets@SYNC] ", "Error executing bulk update for IP  ", ip, "  err: ", err)
		}
	}

	return true
}
