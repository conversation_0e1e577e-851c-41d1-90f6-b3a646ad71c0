package task

import (
	"context"
	"embed"
	"encoding/json"
	"errors"
	"reflect"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/embedsfs"
	"git.gobies.org/foeye-dependencies/fsfire"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/ipparser"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/location"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/message"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/queue"
	"git.gobies.org/foeye/foeye-engine-syncasset/store/storage"
)

func TestTaskSuite(t *testing.T) {
	suite.Run(t, &TaskSuite{})
}

type TaskSuite struct {
	suite.Suite
	configure *config.Configure
	baseDir   string
	task      *Task
}

var embeds embed.FS

func (suite *TaskSuite) SetupSuite() {
	suite.baseDir = "../"
	suite.configure = config.GetConfigure(configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath(suite.baseDir),
		configure.WithSpecificConfigName("conf.test"),
	)

	defer gomonkey.ApplyFuncReturn(location.NewLocationOr, &location.Location{}, nil).Reset()

	fs := embedsfs.NewEmbedsFS(embeds, embedsfs.WithPath("../embeds"))
	sidekip, _ := queue.NewSidekipOr(suite.configure)
	suite.task = NewTask(suite.baseDir, suite.configure, storage.NewStorage(suite.configure, embeds), sidekip, fs)
	suite.task.taskObj = &models.Task{Id: 1, IPType: 1}

	assert.NotNil(suite.T(), suite.task)
}

func (suite *TaskSuite) TearDownSuite() {

}

func (suite *TaskSuite) TestSync() {
	suite.T().Run("CountByTaskId err", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().TaskAssets(), "CountByTaskId", 1, errors.New("CountByTaskId err")).Reset()

		upstream := &message.UpstreamTask{}
		err := suite.task.sync(context.Background(), upstream)

		assert.Equal(t, "CountByTaskId err", err.Error())
	})

	suite.T().Run("success", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().TaskAssets(), "CountByTaskId", 1, nil).Reset()
		defer gomonkey.ApplyMethod(reflect.TypeOf(&gorm.DB{}), "Last", func(_ *gorm.DB, out interface{}) *gorm.DB {
			task := models.Task{}
			task.Id = 1
			task.IPType = 1
			reflect.ValueOf(out).Elem().Set(reflect.ValueOf(&task))
			return &gorm.DB{Error: nil}
		}).Reset()
		defer gomonkey.ApplyPrivateMethod(suite.task, "migration", func() {}).Reset()

		upstream := &message.UpstreamTask{}
		err := suite.task.sync(context.Background(), upstream)

		assert.NoError(t, err)
	})
}

func (suite *TaskSuite) TestDomain() {
	suite.T().Run("success", func(t *testing.T) {
		domainIps := map[string][]string{"a.com": []string{"**********", "**********83"}, "b.com": []string{"**********83"}}
		err := suite.task.domain(context.Background(), domainIps)

		assert.NoError(t, err)
		host, _ := suite.task.domains.Load("**********83")
		assert.Contains(t, host.([]string), "a.com")
		assert.Contains(t, host.([]string), "b.com")

		host, _ = suite.task.domains.Load("**********")
		assert.Equal(t, []string{"a.com"}, host)
	})
}

func (suite *TaskSuite) TestMigration() {
	suite.T().Run("success", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()
		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().TaskAssets(), "GetListAssignToAssets", models.AssetDocuments{&models.AssetDocument{TaskId: 1, IP: "**********83"}}, nil).Reset()
		defer gomonkey.ApplyPrivateMethod(suite.task,
			"processingTaskAsset",
			func() models.AssetDocuments {
				return models.AssetDocuments{&models.AssetDocument{TaskId: 1, IP: "**********83"}}
			},
		).Reset()
		defer gomonkey.ApplyPrivateMethod(suite.task,
			"processingAsset",
			func() *models.MigrateToAsset {
				return &models.MigrateToAsset{}
			},
		).Reset()

		defer gomonkey.ApplyPrivateMethod(suite.task,
			"execute",
			func() error {
				return nil
			},
		).Reset()

		ipRangeCidrs := []*models.IPRangeCidr{{ID: 1, IpRange: "**********/24"}}
		err := suite.task.migration(context.Background(), 1, 1, 10, ipRangeCidrs)

		assert.NoError(t, err)
	})
}

func (suite *TaskSuite) TestProcessingAsset() {
	defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
	defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()
	suite.T().Run("No assets", func(t *testing.T) {
		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		taskAssets := models.AssetDocuments{{TaskId: 1, IP: "**********83"}}
		assets := &sync.Map{}
		ipTag := map[string]*models.IPRangeTags{"a": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		migrate := suite.task.processingAsset(context.Background(), 1, taskAssets, assets, ipTag)

		assert.NotNil(t, migrate)
	})

}

func (suite *TaskSuite) TestMergeTaskAssetInfo() {
	suite.T().Run("mergeTaskAssetInfo founded tag is nil", func(t *testing.T) {
		taskAssets := models.AssetDocuments{
			&models.AssetDocument{IP: "*******", Ports: []string{"80"}, LastUpdateTime: "2024-08-09 12:00:01"},
		}
		ipTag := map[string]*models.IPRangeTags{}
		assets := sync.Map{}
		assets.Store("*******", models.AssetDocument{IP: "*******", Ports: []string{"80"}})
		var category models.TaskCategory
		category = "flow"
		migrate := models.MigrateToAsset{}

		task := Task{location: &location.Location{}, domains: &sync.Map{}}
		defer gomonkey.ApplyMethodReturn(task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()

		ips := task.MergeTaskAssetInfo(taskAssets, ipTag, &assets, category, &migrate)
		assert.Equal(t, 1, len(ips))
		assert.Equal(t, "2024-08-09 12:00:01", migrate.Updates[0].LastUpdateTime)
	})

	suite.T().Run("mergeTaskAssetInfo founded tag is not nil", func(t *testing.T) {
		taskAssets := models.AssetDocuments{
			&models.AssetDocument{IP: "*******", Ports: []string{"80"}, LastUpdateTime: "2024-08-09 12:00:01"},
		}
		ipTag := map[string]*models.IPRangeTags{"*******": {Province: "河南省", City: "商丘市", Company: "华顺"}}
		assets := sync.Map{}
		assets.Store("*******", models.AssetDocument{IP: "*******", Ports: []string{"80"}})
		var category models.TaskCategory
		category = "flow"
		migrate := models.MigrateToAsset{}

		task := Task{location: &location.Location{}, domains: &sync.Map{}}
		defer gomonkey.ApplyMethodReturn(task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()

		ips := task.MergeTaskAssetInfo(taskAssets, ipTag, &assets, category, &migrate)
		assert.Equal(t, 1, len(ips))
		assert.Equal(t, "2024-08-09 12:00:01", migrate.Updates[0].LastUpdateTime)
	})

	suite.T().Run("mergeTaskAssetInfo insert tag is nil", func(t *testing.T) {
		taskAssets := models.AssetDocuments{
			&models.AssetDocument{IP: "*******", Ports: []string{"80"}, LastUpdateTime: "2024-08-09 12:00:01"},
		}
		ipTag := map[string]*models.IPRangeTags{}
		assets := sync.Map{}
		var category models.TaskCategory
		category = "flow"
		migrate := models.MigrateToAsset{}

		task := Task{location: &location.Location{}, domains: &sync.Map{}}
		defer gomonkey.ApplyMethodReturn(task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()

		ips := task.MergeTaskAssetInfo(taskAssets, ipTag, &assets, category, &migrate)
		assert.Equal(t, 1, len(ips))
		assert.Equal(t, "2024-08-09 12:00:01", migrate.Inserts[0].LastUpdateTime)
	})

	suite.T().Run("mergeTaskAssetInfo insert tag is not nil", func(t *testing.T) {
		taskAssets := models.AssetDocuments{
			&models.AssetDocument{IP: "*******", Ports: []string{"80"}, LastUpdateTime: "2024-08-09 12:00:01"},
		}
		ipTag := map[string]*models.IPRangeTags{"*******": {Province: "河南省", City: "商丘市", Company: "华顺"}}
		assets := sync.Map{}
		var category models.TaskCategory
		category = "flow"
		migrate := models.MigrateToAsset{}

		task := Task{location: &location.Location{}, domains: &sync.Map{}}
		defer gomonkey.ApplyMethodReturn(task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()

		ips := task.MergeTaskAssetInfo(taskAssets, ipTag, &assets, category, &migrate)
		assert.Equal(t, 1, len(ips))
		assert.Equal(t, "2024-08-09 12:00:01", migrate.Inserts[0].LastUpdateTime)
	})
	// mosso.DebugShowContentWithJSON(assets)

}

func (suite *TaskSuite) TestSetIpv6Raw() {
	doc := &models.AssetDocument{IP: "afff::128f"}
	ipTag := map[string]*models.IPRangeTags{"afff::128f": &models.IPRangeTags{IpRangeId: 1, IpRange: "afff::128f"}}
	setIpv6Raw(doc, ipTag)

	assert.Equal(suite.T(), "afff::128f", doc.Ipv6Raw)

}

func (suite *TaskSuite) TestPosition() {
	t := suite.T()
	loc := ipparser.Location{Province: "abbb", City: "bbbb"}
	document := models.AssetDocument{Province: "aaa"}
	tag := &models.IPRangeTags{}
	suite.task.position(loc, &document, tag)

	assert.Equal(t, "abbb", document.Province)
	assert.Equal(t, "bbbb", document.City)

	loc = ipparser.Location{Province: location.CountryOfLocalAreaNetwork, City: location.CountryOfLocalAreaNetwork, Country: location.CountryOfLocalAreaNetwork}
	document = models.AssetDocument{Province: "aaa"}
	suite.task.position(loc, &document, tag)

	assert.Equal(t, location.CountryDefaultOfChina, document.Country)
	assert.Equal(t, "", document.Province)
	assert.Equal(t, "", document.City)

	tag = &models.IPRangeTags{Province: "Province", City: "City"}
	suite.task.position(loc, &document, tag)

	assert.Equal(t, location.CountryDefaultOfChina, document.Country)
	assert.Equal(t, "Province", document.Province)
	assert.Equal(t, "City", document.City)
}

func (suite *TaskSuite) TestHost() {
	t := suite.T()
	hostArr := []interface{}{"a.com", "b.com"}
	founded := models.AssetDocument{Hosts: hostArr}
	hosts := suite.task.host(founded)

	assert.Equal(t, "a.com,b.com", hosts)
}

func (suite *TaskSuite) TestProcessingTaskAsset() {
	suite.T().Run("tag is nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		taskAssets := models.AssetDocuments{{TaskId: 1, IP: "**********83"}}
		ipTag := map[string]*models.IPRangeTags{"a": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		docs := suite.task.processingTaskAsset(context.Background(), 1, taskAssets, ipTag)

		assert.Len(t, docs, 1)
	})

	suite.T().Run("tag is not nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		taskAssets := models.AssetDocuments{{TaskId: 1, IP: "**********83"}}
		ipTag := map[string]*models.IPRangeTags{"**********83": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		docs := suite.task.processingTaskAsset(context.Background(), 1, taskAssets, ipTag)

		assert.Len(t, docs, 1)
	})
}

func (suite *TaskSuite) Test_processingTaskDomainAsset() {
	suite.T().Run("tag is nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		ipTag := map[string]*models.IPRangeTags{"a": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		suite.task.DomainIpRelations = map[string][]map[string]interface{}{
			"baimaohui.net": []map[string]interface{}{
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
			},
			"fofa.info": []map[string]interface{}{
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
			},
		}
		docs := suite.task.processingTaskDomainAsset(ipTag, 1)

		assert.True(t, docs)
	})

	suite.T().Run("tag is not nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		ipTag := map[string]*models.IPRangeTags{"baimaohui.net": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		suite.task.DomainIpRelations = map[string][]map[string]interface{}{
			"baimaohui.net": []map[string]interface{}{
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
			},
			"fofa.info": []map[string]interface{}{
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
			},
		}
		docs := suite.task.processingTaskDomainAsset(ipTag, 1)

		assert.True(t, docs)
	})
}

func (suite *TaskSuite) Test_processingDomainAsset() {
	suite.T().Run("tag is nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		ipTag := map[string]*models.IPRangeTags{"a": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		suite.task.DomainIpRelations = map[string][]map[string]interface{}{
			"baimaohui.net": []map[string]interface{}{
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
			},
			"fofa.info": []map[string]interface{}{
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
			},
		}
		docs := suite.task.processingDomainAsset(context.Background(), ipTag)

		assert.True(t, docs)
	})

	suite.T().Run("tag is not nil", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.location.City(), "FindLocation", ipparser.Location{City: "aa"}, nil).Reset()
		suite.task.domains = &sync.Map{}
		ipTag := map[string]*models.IPRangeTags{"baimaohui.net": &models.IPRangeTags{IpRangeId: 1, IpRange: "**********83"}}
		suite.task.DomainIpRelations = map[string][]map[string]interface{}{
			"baimaohui.net": []map[string]interface{}{
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
			},
			"fofa.info": []map[string]interface{}{
				{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
				{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
			},
		}
		docs := suite.task.processingDomainAsset(context.Background(), ipTag)

		assert.True(t, docs)
	})
}

func (suite *TaskSuite) Test_GetTaskAssetDomainIpsToUpstream() {
	defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
	defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

	res := map[string][]string{"baimaohui.net": []string{"************"}, "fofa.info": []string{"**********01"}}
	defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().TaskAssets(), "GetIpsByDomains", res, nil).Reset()

	upstream := &message.UpstreamTask{DomainIpRelations: map[string][]map[string]interface{}{
		"baimaohui.net": []map[string]interface{}{
			{"ip": "**********83", "type": "A", "ttl": 60, "rtt": 10},
			{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
		},
		"fofa.info": []map[string]interface{}{
			{"ip": "**********84", "type": "A", "ttl": 60, "rtt": 10},
			{"ip": "**********", "type": "A", "ttl": 60, "rtt": 10},
		},
	}}

	suite.task.getTaskAssetDomainIpsToUpstream(upstream)

	assert.Equal(suite.T(), "************", upstream.DomainIpRelations["baimaohui.net"][2]["ip"])
	assert.Equal(suite.T(), "**********01", upstream.DomainIpRelations["fofa.info"][2]["ip"])
}

func (suite *TaskSuite) Test_syncDomainToAssetHosts() {
	defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
	defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

	searchResult := elastic.SearchResult{}
	source := &json.RawMessage{}
	source.UnmarshalJSON([]byte(`{"ip":"**********00","host":"**********00","port":80,"protocol":"http"}`))
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(10), Hits: []*elastic.SearchHit{
		{
			Source: source,
		},
	}}
	defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Client().Search().Index(""), "Do", &searchResult, nil).Reset()

	assert.True(suite.T(), suite.task.syncDomainToAssetHosts())
}
