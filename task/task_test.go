package task

import (
	"context"
	"errors"
	"testing"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/system"
)

func (suite *TaskSuite) TestIsOverflowAssetLimitNum() {
	defer gomonkey.ApplyPrivateMethod(suite.task, "isMock", func() bool { return true }).Reset()
	suite.T().Run("systeminfo error", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(system.GetSystemInfoWithUrl, nil, errors.New("systeminfo err")).Reset()
		single := &models.Task{TaskType: 1}
		limit, err := suite.task.IsOverflowAssetLimitNum(context.Background(), single)

		assert.Equal(suite.T(), "systeminfo err", err.Error())
		assert.False(suite.T(), limit)
	})
	suite.T().Run("Get Assets Count error", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 0, errors.New("get count err")).Reset()
		single := &models.Task{TaskType: 1}
		limit, err := suite.task.IsOverflowAssetLimitNum(context.Background(), single)

		assert.NotNil(t, err)
		assert.False(suite.T(), limit)
	})

	suite.T().Run("Is Vulnerability", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyPrivateMethod(suite.task, "systemInfo", func() (*exchange.SystemInfo, error) {
			s := &exchange.SystemInfo{AssetLimitNum: 1000}
			return s, nil
		}).Reset()
		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 10, nil).Reset()
		single := &models.Task{TaskType: 2}
		limit, err := suite.task.IsOverflowAssetLimitNum(context.Background(), single)

		assert.NoError(suite.T(), err)
		assert.False(suite.T(), limit)
	})

	suite.T().Run("unused <= 0", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyPrivateMethod(suite.task, "systemInfo", func() (*exchange.SystemInfo, error) {
			s := &exchange.SystemInfo{AssetLimitNum: 5}
			return s, nil
		}).Reset()
		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 10, nil).Reset()
		single := &models.Task{TaskType: 1}
		limit, err := suite.task.IsOverflowAssetLimitNum(context.Background(), single)

		assert.NoError(suite.T(), err)
		assert.True(suite.T(), limit)
	})

	suite.T().Run("unused > 0", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyPrivateMethod(suite.task, "systemInfo", func() (*exchange.SystemInfo, error) {
			s := &exchange.SystemInfo{AssetLimitNum: 15}
			return s, nil
		}).Reset()
		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 10, nil).Reset()
		single := &models.Task{TaskType: 1}
		limit, err := suite.task.IsOverflowAssetLimitNum(context.Background(), single)

		assert.NoError(suite.T(), err)
		assert.False(suite.T(), limit)
	})
}

func (suite *TaskSuite) TestAssetUnused() {
	defer gomonkey.ApplyPrivateMethod(suite.task, "isMock", func() bool { return true }).Reset()
	suite.T().Run("systeminfo error", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(system.GetSystemInfoWithUrl, nil, errors.New("systeminfo err")).Reset()
		unused, err := suite.task.AssetUnused(context.Background())

		assert.Equal(suite.T(), "Get system info to failed: systeminfo err", err.Error())
		assert.Equal(suite.T(), 0, unused)
	})

	suite.T().Run("Get Assets Count error", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 0, errors.New("get count err")).Reset()
		unused, err := suite.task.AssetUnused(context.Background())

		assert.NotNil(t, err)
		assert.Equal(suite.T(), 0, unused)
	})

	suite.T().Run("success", func(t *testing.T) {
		defer gomonkey.ApplyFuncReturn(fsfire.GetFileContentBytesWithEmbedFS, []byte{}, nil).Reset()
		defer gomonkey.ApplyFuncReturn(elasticsearch.CreateIndexIfNotExists, nil, nil).Reset()

		defer gomonkey.ApplyMethodReturn(suite.task.factory.Elastic().Assets(), "Count", 10, nil).Reset()
		defer gomonkey.ApplyPrivateMethod(suite.task, "systemInfo", func() (*exchange.SystemInfo, error) {
			s := &exchange.SystemInfo{AssetLimitNum: 15}
			return s, nil
		}).Reset()
		unused, err := suite.task.AssetUnused(context.Background())

		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), 5, unused)
	})
}
