package task

import (
	"sync"

	"git.gobies.org/foeye-dependencies/interside"
	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	mergel "git.gobies.org/foeye/foeye-engine-syncasset/module/merge"
)

// HandlerPortListFunc Default support string merge.
var HandlerPortListFunc mergel.HandlerFunc = func(val interface{}, sets *sync.Map) bool {
	var exists bool

	sets.Range(func(key, value interface{}) bool {
		find, findOk := val.(models.AssetPortListItem)
		load, loadOk := value.(models.AssetPortListItem)
		if findOk && loadOk {
			if find.Port == load.Port {
				exists = true
			}
		}

		return true
	})

	return exists
}

// HandlerTitleListFunc Default support string merge.
var HandlerTitleListFunc mergel.HandlerFunc = func(val interface{}, sets *sync.Map) bool {
	var exists bool

	sets.Range(func(key, value interface{}) bool {
		find, findOk := val.(models.AssetTitleListItem)
		load, loadOk := value.(models.AssetTitleListItem)
		if findOk && loadOk {
			if find.Port == load.Port {
				exists = true
			}
		}

		return true
	})

	return exists
}

func (task *Task) mergeTitleList(founded models.AssetDocument, taskAsset *models.AssetDocument) []models.AssetTitleListItem {
	mergeless := mergel.NewMerge()

	for _, item := range founded.TitleList {
		mergeless.Append(item)
	}

	for _, item := range taskAsset.TitleList {
		mergeless.Append(item)
	}

	merged := mergeless.Merged(HandlerTitleListFunc)
	logger.Infow("[MERGE]Merged title list",
		"asset_ip", founded.IP,
		"asset_title_list_len", len(founded.TitleList),
		"task_asset_ip", taskAsset.IP,
		"task_asset_title_list_len", len(taskAsset.TitleList),
		"merged_len", merged.Len(),
	)

	var sets []models.AssetTitleListItem
	for _, item := range merged.Value() {
		if sets == nil {
			sets = make([]models.AssetTitleListItem, 0)
		}

		portList, ok := item.(models.AssetTitleListItem)
		if ok {
			sets = append(sets, portList)
		}
	}

	return sets
}

func (task *Task) mergePorts(founded models.AssetDocument, taskAsset *models.AssetDocument) []string {
	mergeless := mergel.NewMerge()

	for _, item := range founded.Ports {
		mergeless.Append(item)
	}

	for _, item := range taskAsset.Ports {
		mergeless.Append(item)
	}

	merged := mergeless.Merged(mergel.HandlerStringFunc)
	logger.Infow("[MERGE]Merged ports",
		"asset_ip", founded.IP,
		"asset_ports_len", len(founded.Ports),
		"task_asset_ip", taskAsset.IP,
		"task_asset_ports_len", len(taskAsset.Ports),
		"merged_len", merged.Len(),
	)

	if merged.Len() == 0 {
		return nil
	}

	container := interside.NewContainer()
	container.Append(merged.Value()...)
	slice := container.StringSlice()

	return slice
}

func (task *Task) mergeProtocols(founded models.AssetDocument, taskAsset *models.AssetDocument) []string {
	mergeless := mergel.NewMerge()

	for _, item := range founded.Protocols {
		mergeless.Append(item)
	}

	for _, item := range taskAsset.Protocols {
		mergeless.Append(item)
	}

	merged := mergeless.Merged(mergel.HandlerStringFunc)
	logger.Infow("[MERGE]Merged protocols",
		"asset_ip", founded.IP,
		"asset_protocols_len", len(founded.Protocols),
		"task_asset_ip", taskAsset.IP,
		"task_asset_protocols_len", len(taskAsset.Protocols),
		"merged", merged.Len(),
	)

	if merged.Len() == 0 {
		return nil
	}

	container := interside.NewContainer()
	container.Append(merged.Value()...)
	slice := container.StringSlice()

	return slice
}

func (task *Task) mergePortList(founded models.AssetDocument, taskAsset *models.AssetDocument) []models.AssetPortListItem {
	mergeless := mergel.NewMerge()

	for _, item := range taskAsset.PortList {
		mergeless.Append(item)
	}

	for _, item := range founded.PortList {
		mergeless.Append(item)
	}

	merged := mergeless.Merged(HandlerPortListFunc)
	logger.Infow("[MERGE]Merged port list",
		"asset_ip", founded.IP,
		"asset_port_list_len", len(founded.PortList),
		"task_asset_ip", taskAsset.IP,
		"task_asset_port_list_len", len(taskAsset.PortList),
		"merged", len(merged.Value()),
	)

	var sets []models.AssetPortListItem
	for _, item := range merged.Value() {
		if sets == nil {
			sets = make([]models.AssetPortListItem, 0)
		}

		portList, ok := item.(models.AssetPortListItem)
		if ok {
			sets = append(sets, portList)
		}
	}

	return sets
}
