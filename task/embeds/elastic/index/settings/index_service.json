{"settings": {"index": {"number_of_shards": "5", "blocks": {"read_only_allow_delete": "false"}, "max_result_window": "10000000", "analysis": {"analyzer": {"fofa_dot_analyzer": {"type": "custom", "char_filter": ["fofa_dot_to_space"], "tokenizer": "fofa_dot_tokenizer"}}, "char_filter": {"fofa_dot_to_space": {"pattern": "[.:/]", "type": "pattern_replace", "replacement": " "}}, "tokenizer": {"fofa_dot_tokenizer": {"type": "whitespace"}}}, "number_of_replicas": "1"}}, "mappings": {"service": {"dynamic_templates": [{"string_fields": {"match": "*", "match_mapping_type": "string", "mapping": {"fields": {"raw": {"ignore_above": 256, "index": true, "type": "keyword"}}, "index": true, "norms": false, "type": "text"}}}], "properties": {"appserver": {"type": "keyword"}, "banner": {"type": "text", "fields": {"banner_raw": {"type": "keyword", "ignore_above": 32765}}}, "base_protocol": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cert": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "certs": {"properties": {"domain": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "issuer_cn": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "issuer_org": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "not_after": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "not_before": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "sig_alth": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "sn": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "subject_cn": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "subject_org": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "v": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}, "dbs": {"properties": {"Count": {"type": "long"}, "DbSize": {"type": "long"}, "Records": {"type": "long"}}}, "geoip": {"dynamic": "true", "properties": {"city_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "continent_code": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_code2": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_code3": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "latitude": {"type": "long"}, "location": {"type": "geo_point"}, "longitude": {"type": "long"}, "postal_code": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "real_region_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "region_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "timezone": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}, "gid": {"type": "keyword"}, "ip": {"type": "ip", "fields": {"ip_raw": {"type": "keyword"}, "ipstr": {"type": "text", "analyzer": "fofa_dot_analyzer"}}}, "ipcnet": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "is_ipv6": {"type": "boolean"}, "lastchecktime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "lastupdatetime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "mac": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "netbios_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "notretry": {"type": "boolean"}, "os": {"type": "keyword"}, "port": {"type": "keyword", "fields": {"port": {"type": "integer"}, "port_raw": {"type": "keyword"}}}, "port_state": {"type": "keyword"}, "product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "protocol": {"type": "keyword"}, "rule_tags": {"properties": {"category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_company": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_parent_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "company": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "level": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "parent_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "rule_id": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "softhard": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}, "structinfo": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "tags": {"type": "keyword"}, "time": {"type": "date"}, "user_tags": {"type": "keyword"}, "v": {"type": "long"}, "version": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}}}