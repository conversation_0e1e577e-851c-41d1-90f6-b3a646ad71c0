{"id": 7155, "name": "农友政务系统LandInfoEdit.asp文件id参数SQL注入", "description": "农友软件多年来致力于农村、农业、农民的“三农”信息化建设，是国内领先的“三农”信息化建设全面解决方案提供商，同时也是国内最大的“三农”信息化服务提供商。农友政务系统_LandInfoEdit.asp文件id参数SQL注入，会导致直接执行SQL语句，从而获取数据，修改数据，删除数据等。", "filename": "nongyou_LandInfoEdit.asp_id_Sql.json", "author": "少年阿基米德", "product": "农友政务系统", "homepage": "http://www.nongyou.com.cn/", "references": "https://github.com/cflq3/poc/blob/master/bugscan/exp-2801.py", "fofaquery": "body=\"ExtWebModels\" || body=\"var vdomainname=\\\"http://\\\"+aUrls[2]+\\\"\"", "content": "[{\"desc\":\"\",\"name\":\"sql\",\"type\":\"select\",\"value\":\"user,database,version\"}]", "state": 1, "user_id": null, "price": null, "score": null, "comments_count": 0, "fofa_records": 0, "level": 2, "xday_st": null, "affect_amount": 0, "affect_enterprise": null, "reject_reason": null, "vul_define": null, "production_define": null, "price_define": null, "task_state": 0, "task_percent": null, "created_at": "2021-09-13 17:43:18", "updated_at": "2021-09-13 17:46:01", "last_scaned_at": null, "last_tid": null, "impact": "<p>黑客可以直接执行SQL语句，从而控制整个服务器：获取数据、修改数据、删除数据等。</p>", "recommandation": "<p>1.在网页代码中需要对用户输入的数据进行严格过滤。</p><p>2.部署Web应用防火墙，对数据库操作进行监控。</p><p>3.升级至最新版本。</p>", "has_exp": 1, "threat_count": null, "scan_task_id": null, "begin_scan_time": null, "cveId": null, "vulType": "SQL注入", "disclosure_date": "2015-06-11", "vulNum": "CVD-2015-0381"}