[{"id": 3037, "name": "1039驾校管理系统未授权访问", "description": "1039公司是国内第一家专业从事培训行业标准化软件开发和大型应用性平台的高科技企业，是培训行业信息化建设的最佳合作伙伴。此系统存在未授权访问问题，泄露公民敏感信息。", "filename": "1039soft_unauthorized_access.json", "author": "<PERSON><PERSON>", "product": "1039家校通", "homepage": "http://www.1039soft.com", "references": "https://shuimugan.com/bug/view?bug_no=0132856", "fofaquery": "title=\"登录_1039家校通\" || (body=\"/Handler/ValidateCode.ashx?id=\" && body=\"txtyzm\")", "content": "[]", "state": 1, "user_id": null, "price": null, "score": null, "comments_count": 0, "fofa_records": 0, "level": 1, "xday_st": null, "affect_amount": 0, "affect_enterprise": null, "reject_reason": null, "vul_define": null, "production_define": null, "price_define": null, "task_state": 0, "task_percent": null, "created_at": "2021-09-13 16:56:59", "updated_at": "2021-09-13 17:20:57", "last_scaned_at": null, "last_tid": null, "impact": "<p>泄露公民身份信息，获得公司流水与财务统计，造成严重的信息泄露，威胁用户信息安全。<br></p>", "recommandation": "<p>一、包含敏感信息的文件请加密并妥善保存；</p><p>二、如非必要，禁止公网访问该系统；</p><p>三、白名单限制可访问IP。</p>", "has_exp": 0, "threat_count": null, "scan_task_id": null, "begin_scan_time": null, "cveId": null, "vulType": "未授权访问", "disclosure_date": "2019-08-27", "vulNum": "CVD-2019-1110"}, {"id": 3122, "name": "AGILE-BPM 敏捷工作流开发平台默认口令", "description": "AgileBPM 采用B/S框架开发，拥有全新的SpringBoot+SpringCloud前后端分离架构模式，有强大的报表+BI大屏+工作流引擎设计器，一站式开发PC+移动等多端业务系统。该系统存在默认口令，攻击者可利用默认口令进入后台，查看信息，可以修改相关数据对公司造成影响以及严重损失。", "filename": "AgileBPM_default_password.json", "author": "<EMAIL>", "product": "AgileBPM", "homepage": "http://www.agilebpm.cn/", "references": "https://fofa.so/", "fofaquery": "app=\"AgileBPM\" || body=\"class=\\\"logo-element\\\">BPM\" || body=\"class=\\\"logo-element\\\">AGILE-BPM\"", "content": "[]", "state": 1, "user_id": null, "price": null, "score": null, "comments_count": 0, "fofa_records": 0, "level": 2, "xday_st": null, "affect_amount": 0, "affect_enterprise": null, "reject_reason": null, "vul_define": null, "production_define": null, "price_define": null, "task_state": 0, "task_percent": null, "created_at": "2021-09-13 16:57:00", "updated_at": "2021-09-13 16:57:00", "last_scaned_at": null, "last_tid": null, "impact": "<p>AgileBPM 采用B/S框架开发，拥有全新的SpringBoot+SpringCloud前后端分离架构模式，有强大的报表+BI大屏+工作流引擎设计器，一站式开发PC+移动等多端业务系统。</p><p><span style=\"color: rgb(51, 51, 51); font-size: 16px;\">AgileBPM&nbsp;</span>系统存在默认口令，攻击者可利用默认口令进入后台，查看信息，可以修改相关数据对公司造成影响以及严重损失。<br></p>", "recommandation": "<p style=\"text-align: start;\">1、修改默认口令，密码最好包含大小写字母、数字和特殊字符等且位数大于8位。</p><p style=\"text-align: start;\">2、如非必要，禁止公网访问该设备。</p><p style=\"text-align: start;\">3、白名单限制可访问IP。</p>", "has_exp": 0, "threat_count": null, "scan_task_id": null, "begin_scan_time": null, "cveId": null, "vulType": "默认口令", "disclosure_date": "2020-03-22", "vulNum": null}]