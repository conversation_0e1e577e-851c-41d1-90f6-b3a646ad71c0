[{"id": 1, "ip_range": "**********/24", "created_at": "2022-01-13 11:49:22", "updated_at": "2022-01-13 11:49:22", "name": null, "host": null, "business_app": null, "group_name": null, "province": null, "city": null, "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 2, "ip_range": "************/32", "created_at": "2022-01-15 20:49:47", "updated_at": "2022-01-15 20:49:47", "name": null, "host": "http://************", "domains": [{"name": "http://************"}], "business_app": null, "group_name": null, "province": null, "city": null, "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 3, "ip_range": "**********-50", "created_at": "2022-01-15 20:52:15", "updated_at": "2022-01-15 20:52:15", "name": null, "host": "", "business_app": null, "group_name": null, "province": "河北省", "city": "石家庄市", "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 4, "ip_range": "***********-100", "created_at": "2022-01-15 20:52:15", "updated_at": "2022-01-15 20:52:15", "name": null, "host": "", "business_app": null, "group_name": null, "province": null, "city": "北京市", "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 5, "ip_range": "2000::1:2345:6789:abcd", "created_at": "2022-01-15 20:52:15", "updated_at": "2022-01-15 20:52:15", "name": null, "host": "", "business_app": null, "group_name": null, "province": "河北省", "city": "张家口市", "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 2}, {"id": 6, "ip_range": "**********85/32", "created_at": "2022-01-15 21:04:46", "updated_at": "2022-01-15 21:04:46", "name": null, "host": "http://**********85", "domains": [{"name": "http://**********85"}], "business_app": null, "group_name": null, "province": null, "city": null, "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 11, "ip_range": "***********-30", "created_at": "2022-01-15 21:04:46", "updated_at": "2022-01-15 21:04:46", "name": null, "host": "", "business_app": null, "group_name": null, "province": null, "city": null, "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}, {"id": 18, "ip_range": "***********-50", "created_at": "2022-01-15 21:04:46", "updated_at": "2022-01-15 21:04:46", "name": null, "host": "", "business_app": null, "group_name": null, "province": null, "city": null, "company": null, "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": null, "manager_name": null, "manager_mobile": null, "manager_email": null, "belong_user_id": null, "gid": null, "ip_type": 1}]