[{"id": 1, "val_id": "1", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 1, "category": "user_input", "created_at": "2022-09-05 19:14:37", "updated_at": "2022-09-05 19:14:37", "val_type": "", "vals": "1"}, {"id": 2, "val_id": "4", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 2, "category": "ip_ranges", "created_at": "2022-09-05 19:31:27", "updated_at": "2022-09-05 19:31:27", "val_type": "", "vals": "4"}, {"id": 3, "val_id": "3", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 2, "category": "ip_ranges", "created_at": "2022-09-05 19:31:27", "updated_at": "2022-09-05 19:31:27", "val_type": "", "vals": "3"}, {"id": 4, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 2, "category": "ip_ranges", "created_at": "2022-09-05 19:31:27", "updated_at": "2022-09-05 19:31:27", "val_type": "", "vals": "2"}, {"id": 5, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 3, "category": "ip_ranges", "created_at": "2022-09-05 21:34:14", "updated_at": "2022-09-05 21:34:14", "val_type": "", "vals": "2"}, {"id": 6, "val_id": "3", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 3, "category": "ip_ranges", "created_at": "2022-09-05 21:34:14", "updated_at": "2022-09-05 21:34:14", "val_type": "", "vals": "3"}, {"id": 7, "val_id": "4", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 3, "category": "ip_ranges", "created_at": "2022-09-05 21:34:14", "updated_at": "2022-09-05 21:34:14", "val_type": "", "vals": "4"}, {"id": 8, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 4, "category": "business_app", "created_at": "2022-09-05 21:39:49", "updated_at": "2022-09-05 21:39:49", "val_type": "", "vals": "2"}, {"id": 9, "val_id": "4", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 4, "category": "business_app", "created_at": "2022-09-05 21:39:49", "updated_at": "2022-09-05 21:39:49", "val_type": "", "vals": "4"}]