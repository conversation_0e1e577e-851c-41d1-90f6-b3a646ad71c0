[{"id": 1, "name": "管理单元", "tag_type": "1b37b4a3-7a8b-43a1-af30-9d32338a5a43", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "company", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 2, "name": "业务系统", "tag_type": "1ffdcc50-6f2c-4c7a-8835-0649f94f7cfa", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "business_app", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 3, "name": "负责人", "tag_type": "responsible_person", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "username", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 4, "name": "机房信息", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "computer_room", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 101, "name": "资产等级", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 0, "position": 0, "mode": 0, "children_count": 0, "realname": "asset_level", "email": "", "phone": "", "created_at": "2022-09-05 10:15:39", "updated_at": "2022-09-05 10:15:39", "deleted_at": null}, {"id": 108, "name": "一般", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 101, "position": 0, "mode": 1, "children_count": 0, "realname": "yiban", "email": "", "phone": "", "created_at": "2022-09-05 19:08:33", "updated_at": "2022-09-05 19:08:33", "deleted_at": null}, {"id": 109, "name": "重要", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 101, "position": 0, "mode": 1, "children_count": 0, "realname": "zhong<PERSON>o", "email": "", "phone": "", "created_at": "2022-09-05 19:08:41", "updated_at": "2022-09-05 19:08:41", "deleted_at": null}, {"id": 110, "name": "青岛机房", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 4, "position": 0, "mode": 1, "children_count": 0, "realname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "phone": "", "created_at": "2022-09-05 19:09:46", "updated_at": "2022-09-05 19:09:46", "deleted_at": null}, {"id": 111, "name": "北京机房", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 4, "position": 0, "mode": 1, "children_count": 0, "realname": "beijingjifang", "email": "", "phone": "", "created_at": "2022-09-05 19:09:55", "updated_at": "2022-09-05 19:09:55", "deleted_at": null}, {"id": 112, "name": "tom", "tag_type": "responsible_person", "ancestry": 3, "position": 0, "mode": 1, "children_count": 0, "realname": "tom", "email": "<EMAIL>", "phone": "13717995000", "created_at": "2022-09-05 19:10:14", "updated_at": "2022-09-05 19:10:14", "deleted_at": null}, {"id": 113, "name": "key", "tag_type": "responsible_person", "ancestry": 3, "position": 0, "mode": 1, "children_count": 0, "realname": "key", "email": "<EMAIL>", "phone": "13717994000", "created_at": "2022-09-05 19:10:51", "updated_at": "2022-09-05 19:10:51", "deleted_at": null}, {"id": 114, "name": "CMS", "tag_type": "1ffdcc50-6f2c-4c7a-8835-0649f94f7cfa", "ancestry": 2, "position": 0, "mode": 1, "children_count": 0, "realname": "CMS", "email": "", "phone": "", "created_at": "2022-09-05 19:10:56", "updated_at": "2022-09-05 19:10:56", "deleted_at": null}, {"id": 115, "name": "BMS", "tag_type": "1ffdcc50-6f2c-4c7a-8835-0649f94f7cfa", "ancestry": 2, "position": 0, "mode": 1, "children_count": 0, "realname": "BMS", "email": "", "phone": "", "created_at": "2022-09-05 19:11:08", "updated_at": "2022-09-05 19:11:08", "deleted_at": null}, {"id": 116, "name": "仓储", "tag_type": "1b37b4a3-7a8b-43a1-af30-9d32338a5a43", "ancestry": 1, "position": 0, "mode": 1, "children_count": 0, "realname": "cangchu", "email": "", "phone": "", "created_at": "2022-09-05 19:11:56", "updated_at": "2022-09-05 19:11:56", "deleted_at": null}, {"id": 117, "name": "物流", "tag_type": "1b37b4a3-7a8b-43a1-af30-9d32338a5a43", "ancestry": 1, "position": 0, "mode": 1, "children_count": 0, "realname": "wuliu", "email": "", "phone": "", "created_at": "2022-09-05 19:12:02", "updated_at": "2022-09-05 19:12:02", "deleted_at": null}, {"id": 118, "name": "大区", "tag_type": "0d96cc68-fdfb-3da6-a930-42f4b22b22bb", "ancestry": 0, "position": 0, "mode": 1, "children_count": 0, "realname": "daqu", "email": "", "phone": "", "created_at": "2022-09-05 19:12:47", "updated_at": "2022-09-05 19:12:47", "deleted_at": null}, {"id": 119, "name": "华北大区", "tag_type": "0d96cc68-fdfb-3da6-a930-42f4b22b22bb", "ancestry": 118, "position": 0, "mode": 1, "children_count": 0, "realname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "phone": "", "created_at": "2022-09-05 19:13:07", "updated_at": "2022-09-05 19:13:07", "deleted_at": null}, {"id": 120, "name": "华南大区", "tag_type": "0d96cc68-fdfb-3da6-a930-42f4b22b22bb", "ancestry": 118, "position": 0, "mode": 1, "children_count": 0, "realname": "huan<PERSON><PERSON>u", "email": "", "phone": "", "created_at": "2022-09-05 19:13:17", "updated_at": "2022-09-05 19:13:17", "deleted_at": null}]