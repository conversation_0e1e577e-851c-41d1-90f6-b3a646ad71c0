[{"id": 1, "ip_range": "**********/24", "created_at": "2022-09-05 19:14:37", "updated_at": "2022-09-05 19:14:37", "name": "", "host": "", "business_app": "", "group_name": "", "province": "", "city": "", "company": "", "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": "", "manager_name": "", "manager_mobile": "", "manager_email": "", "belong_user_id": null, "gid": "", "ip_type": 1, "deleted_at": null, "asset_level": ""}, {"id": 2, "ip_range": "**********", "created_at": "2022-09-05 19:28:42", "updated_at": "2022-09-05 19:28:42", "name": "", "host": "", "business_app": "", "group_name": "", "province": "", "city": "", "company": "", "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": "", "manager_name": "", "manager_mobile": "", "manager_email": "", "belong_user_id": null, "gid": "", "ip_type": 1, "deleted_at": null, "asset_level": ""}, {"id": 3, "ip_range": "***********-50", "created_at": "2022-09-05 19:29:59", "updated_at": "2022-09-05 19:29:59", "name": "", "host": "", "business_app": "", "group_name": "", "province": "", "city": "", "company": "", "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": "", "manager_name": "", "manager_mobile": "", "manager_email": "", "belong_user_id": null, "gid": "", "ip_type": 1, "deleted_at": null, "asset_level": ""}, {"id": 4, "ip_range": "***********-60", "created_at": "2022-09-05 19:30:27", "updated_at": "2022-09-05 19:30:27", "name": "", "host": "", "business_app": "", "group_name": "", "province": "", "city": "", "company": "", "add_way": "self_defined", "scan": 1, "user_id": 1, "computer_room": "", "manager_name": "", "manager_mobile": "", "manager_email": "", "belong_user_id": null, "gid": "", "ip_type": 1, "deleted_at": null, "asset_level": ""}]