[{"id": 11, "name": "管理单元", "tag_type": "1b37b4a3-7a8b-43a1-af30-9d32338a5a43", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "company", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 12, "name": "业务系统", "tag_type": "1ffdcc50-6f2c-4c7a-8835-0649f94f7cfa", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "business_app", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 13, "name": "负责人", "tag_type": "responsible_person", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "username", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 14, "name": "机房信息", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 0, "position": null, "mode": 0, "children_count": 0, "realname": "computer_room", "email": null, "phone": null, "created_at": "2020-03-30 11:57:16", "updated_at": "2022-07-13 11:06:02", "deleted_at": null}, {"id": 100, "name": "资产等级", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 0, "position": 0, "mode": 0, "children_count": 0, "realname": "asset_level", "email": "", "phone": "", "created_at": "2022-09-05 10:15:39", "updated_at": "2022-09-05 10:15:39", "deleted_at": null}, {"id": 102, "name": "Level1", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 101, "position": 0, "mode": 1, "children_count": 0, "realname": "Level1", "email": "", "phone": "", "created_at": "2022-09-05 10:16:33", "updated_at": "2022-09-05 10:16:33", "deleted_at": null}, {"id": 103, "name": "Level2", "tag_type": "1a09384d-976b-3d5e-9b76-a4018f149610", "ancestry": 101, "position": 0, "mode": 1, "children_count": 0, "realname": "Level2", "email": "", "phone": "", "created_at": "2022-09-05 10:16:38", "updated_at": "2022-09-05 10:16:38", "deleted_at": null}, {"id": 104, "name": "北京事业部", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 4, "position": 0, "mode": 1, "children_count": 0, "realname": "be<PERSON>ng<PERSON><PERSON><PERSON>", "email": "", "phone": "", "created_at": "2022-09-05 10:16:56", "updated_at": "2022-09-05 10:16:56", "deleted_at": null}, {"id": 105, "name": "上海事业部", "tag_type": "d4bc5242-8153-42a5-92cc-9faf1a1fa54f", "ancestry": 4, "position": 0, "mode": 1, "children_count": 0, "realname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "phone": "", "created_at": "2022-09-05 10:17:05", "updated_at": "2022-09-05 10:17:05", "deleted_at": null}, {"id": 106, "name": "tom", "tag_type": "responsible_person", "ancestry": 3, "position": 0, "mode": 1, "children_count": 0, "realname": "tom", "email": "<EMAIL>", "phone": "13717995000", "created_at": "2022-09-05 10:17:35", "updated_at": "2022-09-05 10:17:35", "deleted_at": null}, {"id": 107, "name": "CMS", "tag_type": "1ffdcc50-6f2c-4c7a-8835-0649f94f7cfa", "ancestry": 2, "position": 0, "mode": 1, "children_count": 0, "realname": "CMS", "email": "", "phone": "", "created_at": "2022-09-05 10:17:39", "updated_at": "2022-09-05 10:17:39", "deleted_at": null}]