[{"id": 1, "val_id": "1", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 1, "category": "user_input", "created_at": "2022-01-14 03:49:22", "updated_at": "2022-01-14 03:49:22"}, {"id": 2, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 2, "category": "user_input", "created_at": "2022-01-16 12:49:47", "updated_at": "2022-01-16 12:49:47"}, {"id": 3, "val_id": "3332", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 3, "category": "special", "created_at": "2021-12-17 07:09:02", "updated_at": "2021-12-17 07:09:02"}, {"id": 4, "val_id": "3462", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 3, "category": "special", "created_at": "2021-12-17 07:09:02", "updated_at": "2021-12-17 07:09:02"}, {"id": 5, "val_id": "1", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 4, "category": "user_input", "created_at": "2021-12-16 23:09:02", "updated_at": "2021-12-16 23:09:02"}, {"id": 6, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 4, "category": "user_input", "created_at": "2021-12-16 23:09:02", "updated_at": "2021-12-16 23:09:02"}, {"id": 7, "val_id": "17", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 7, "category": "username", "created_at": "2022-01-16 12:55:45", "updated_at": "2022-01-16 12:55:45"}, {"id": 8, "val_id": "10", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 7, "category": "username", "created_at": "2022-01-16 12:55:45", "updated_at": "2022-01-16 12:55:45"}, {"id": 9, "val_id": "6", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 8, "category": "user_input", "created_at": "2022-01-16 13:04:46", "updated_at": "2022-01-16 13:04:46"}, {"id": 10, "val_id": "2", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 10, "category": "ip_ranges", "created_at": "2022-01-16 13:07:58", "updated_at": "2022-01-16 13:07:58"}, {"id": 11, "val_id": "8", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 11, "category": "company", "created_at": "2022-01-16 13:08:31", "updated_at": "2022-01-16 13:08:31"}, {"id": 12, "val_id": "15", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 11, "category": "company", "created_at": "2022-01-16 13:08:31", "updated_at": "2022-01-16 13:08:31"}, {"id": 13, "val_id": "9", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 12, "category": "business_app", "created_at": "2022-01-16 13:09:54", "updated_at": "2022-01-16 13:09:54"}, {"id": 14, "val_id": "16", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 12, "category": "business_app", "created_at": "2022-01-16 13:09:54", "updated_at": "2022-01-16 13:09:54"}, {"id": 15, "val_id": "10", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 13, "category": "username", "created_at": "2022-01-16 13:11:11", "updated_at": "2022-01-16 13:11:11"}, {"id": 16, "val_id": "17", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 13, "category": "username", "created_at": "2022-01-16 13:11:11", "updated_at": "2022-01-16 13:11:11"}, {"id": 17, "val_id": "11", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 14, "category": "computer_room", "created_at": "2022-01-16 13:13:15", "updated_at": "2022-01-16 13:13:15"}, {"id": 18, "val_id": "18", "ip_range_conditionable_type": "Task", "ip_range_conditionable_id": 14, "category": "computer_room", "created_at": "2022-01-16 13:13:15", "updated_at": "2022-01-16 13:13:15"}]