{"_index":"fofaee_threats","_type":"threats","_id":"57de78fa1ccfd37198d1f5296f82f51d","_score":1,"_source":{"hosts":null,"ip":"**********","port":2379,"name":"WPS 开放平台","common_title":"CoreOS ETCD集群API未授权访问漏洞","mac":"b4:96:91:98:4b:f8","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","OpenSSH","Oracle-MySQL","APACHE-ZooKeeper","Prometheus-Time-Series-Collection-and-Processing-Server","Prometheus-Alertmanager","jQuery","Bootstrap","jQuery_UI","七牛云-七牛CDN"],"cat_tags":["服务","其他支撑系统","数据库系统","大数据处理","中间件","开发框架"],"company_tags":["其他","Oracle Corporation","Apache Software Foundation.","The Linux Foundation.","The jQuery Foundation.","twitter","上海七牛信息技术有限公司"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://**********:2379","vulfile":"CoreOS_etcd_api_disclosure.json","url":"http://**********:2379/v2/keys/?recursive=true","obj_type":1,"object":"**********","intranet_ip":1,"addition":null,"merge_md5":"ead45f4aa73085ac956cc51a2dfcc3b5","scan_engine":4,"port_list":[{"protocol":"unknown","port":9100,"banner":"","certs":null},{"protocol":"zookeeper","port":2181,"banner":"Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:51222[0](queued=0,recved=1,sent=0)\n /*.*.*.*:49382[1](queued=0,recved=37794,sent=37795)\n\nLatency min/avg/max: 0/0/59\nReceived: 88067\nSent: 88067\nConnections: 2\nOutstanding: 0\nZxid: 0x2d9\nMode: standalone\nNode count: 166\n","certs":null},{"protocol":"http","port":9093,"banner":"HTTP/1.0 200 OK\r\nAccept-Ranges: bytes\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Length: 1314\r\nContent-Type: text/html; charset=utf-8\r\nExpires: 0\r\nLast-Modified: Thu, 01 Jan 1970 00:00:01 GMT\r\nPragma: no-cache\r\nDate: Wed, 24 Nov 2021 06:54:27 GMT","certs":null},{"protocol":"http","port":9200,"banner":"HTTP/1.0 401 Unauthorized\r\nWWW-Authenticate: Basic realm=\"security\" charset=\"UTF-8\"\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 381\r\n\r\n{\"error\":{\"root_cause\":[{\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}}],\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}},\"status\":401}","certs":null},{"protocol":"kafka","port":9092,"banner":"Kafka\nAddr: **********:9092\nTopics: DOCS_CPS_TASK __consumer_offsets DOCS_STAT_APP_ACTIVITY","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.33-log \r\nN\\x00\\x00\\x00\n5.7.33-log\\x007[\\x00\\x00?]a\\x15Gp\\x16/\\x00\\xff\\xff\\xe0\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x1c4%\\x17{|6Y\u000b1-Y\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":5000,"banner":"HTTP/1.0 200 OK\r\nCache-Control: no-cache\r\nDate: Wed, 24 Nov 2021 07:07:06 GMT\r\nContent-Length: 0","certs":null},{"protocol":"etcd","port":2379,"banner":"Name: etcd1\nId: 80286fd956d980e5\nStartTime: 2021-11-18T11:28:14.475600154Z\nVersion: 3.2.0+git\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 7975\r\nConnection: close\r\nVary: Accept-Encoding\r\nServer: APISIX/2.0\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:08:59 GMT\r\nVary: Accept-Encoding\r\nLast-Modified: Tue, 26 Oct 2021 15:52:59 GMT\r\nVary: Accept-Encoding\r\nETag: \"6178245b-1f27\"\r\nAccept-Ranges: bytes\r\nx-origin: http://**********\r\nEncryption: 0","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"https","port":8081,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:11:39 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 323\r\nConnection: close","certs":{"not_after":"2119-04-16 15:24:08","subject_cn":"**********","issuer_cn":"WPS","not_before":"2021-10-26 15:24:08","v":"v3","domain":"localhost","sn":"11157282297511052792","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":9090,"banner":"HTTP/1.0 302 Found\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /prometheus\r\nDate: Wed, 24 Nov 2021 07:13:22 GMT\r\nContent-Length: 34","certs":null},{"protocol":"http","port":8888,"banner":"HTTP/1.0 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Length: 6586\r\nX-Content-Type-Options: nosniff\r\nReferrer-Policy: same-origin","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,8081],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":21157,"second_cat_tag":"大数据处理","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Apache Software Foundation.","ports":[2181],"title":"APACHE-ZooKeeper"},{"belong_level":0,"rule_id":338397,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"The Linux Foundation.","ports":[9090],"title":"Prometheus-Time-Series-Collection-and-Processing-Server"},{"belong_level":0,"rule_id":338447,"second_cat_tag":"其他支撑系统","soft_hard_code":0,"first_cat_tag":"支撑系统","level_code":5,"company":"The Linux Foundation.","ports":[9093],"title":"Prometheus-Alertmanager"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8888],"title":"jQuery"},{"belong_level":0,"rule_id":139,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"twitter","ports":[8888],"title":"Bootstrap"},{"belong_level":0,"rule_id":255,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8888],"title":"jQuery_UI"},{"belong_level":0,"rule_id":4605,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"上海七牛信息技术有限公司","ports":[8888],"title":"七牛云-七牛CDN"}],"common_description":"ETCD是CoreOS公司发起的一个开源项目，ETCD是用于共享配置和服务发现的分布式、一致性的KV存储系统。CoreOS ETCD集群API存在未授权访问漏洞，ETCD用作所有群集数据的Kubernetes后备存储区，该漏洞可能会泄露大量的敏感信息。","common_impact":"<p>攻击者可以获取AWS密钥和API密钥以及一系列服务的敏感信息，使用获取的密钥可以控制该集群进行进一步攻击，严重威胁用户的数据安全。</p>","recommandation":"<p>1、请参考官方设置身份验证文档增加身份验证：<a href=\"https://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md\">https://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md</a>，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。</p><p>2、如非必要，禁止公网访问该服务。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Length: 37\r\nContent-Type: application/json\r\nDate: Wed, 24 Nov 2021 07:15:42 GMT\r\nX-Etcd-Cluster-Id: b2a60b81abc136b4\r\nX-Etcd-Index: 15\r\nX-Raft-Index: 1561\r\nX-Raft-Term: 14\r\n\r\n{\"action\":\"get\",\"node\":{\"dir\":true}}\n","has_response":1,"createtime":"2021-11-24 15:14:57","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:14:57"}}
{"_index":"fofaee_threats","_type":"threats","_id":"a572fc294e357f104a7f82200c7b23ad","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":"00:0c:29:cf:b0:f2","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["elastic-Elasticsearch","NGINX","OpenSSH","Oracle-MySQL"],"cat_tags":["数据库系统","服务","其他支撑系统"],"company_tags":["Elastic, Inc.","其他","Oracle Corporation"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"a538b9231b18fd11fae61cf07f1121d6","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 493\r\n\r\n{\n  \"name\" : \"BbkUZzB\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"WXLhvbQ_QyWtd1tHrS_arA\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:31:37 GMT\r\nContent-Type: text/html\r\nContent-Length: 6192\r\nLast-Modified: Thu, 11 Nov 2021 08:33:27 GMT\r\nConnection: close\r\nETag: \"618cd557-1830\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:35:05 GMT\r\nContent-Type: text/html\r\nContent-Length: 6192\r\nLast-Modified: Thu, 11 Nov 2021 08:33:27 GMT\r\nConnection: close\r\nETag: \"618cd557-1830\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":{"not_after":"2020-06-15 23:59:59","subject_cn":"focii.cn","issuer_cn":"Sectigo RSA Domain Validation Secure Server CA","issuer_org":["Sectigo Limited"],"not_before":"2019-05-13 00:00:00","v":"v3","domain":"focii.cn","sn":"182191660422272240828351337638536332243","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":8000,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:38:57 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close","certs":null},{"protocol":"mysql","port":3306,"banner":"G\\x00\\x00\\x00\\xffj\\x04Host '************' is not allowed to connect to this MariaDB server","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[8000,443,80],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"720a171f930640b42021953dfdb5e5b4","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"登录-互联网暴露面风险自查平台","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:c0:7d:06","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","redis","Linux-操作系统","NGINX","php","OpenSSH","elastic-Elasticsearch","jQuery","Bootstrap","layer.js"],"cat_tags":["数据库系统","操作系统","服务","脚本语言","其他支撑系统","中间件","开发框架"],"company_tags":["Oracle Corporation","Redis Labs","其他","PHP Group","Elastic, Inc.","The jQuery Foundation.","twitter"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"a018b0087507bfcea80757576b467541","scan_engine":4,"port_list":[{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.52-MariaDB \r\nR\\x00\\x00\\x00\n5.5.52-MariaDB\\x00\\x99.\\x00\\x00U(eFlnd+\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00F`2tuoMs{1~L\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\nContent-Type: application/json; charset=UTF-8\r\nContent-Length: 317\r\n\r\n{\n  \"name\" : \"Aqueduct\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"version\" : {\n    \"number\" : \"2.4.0\",\n    \"build_hash\" : \"ce9f0c7394dee074091dd1bc4e9469251181fc55\",\n    \"build_timestamp\" : \"2016-08-29T09:14:17Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"5.5.2\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 302 Moved Temporarily\r\nServer: nginx/1.14.0\r\nDate: Wed, 24 Nov 2021 07:01:22 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Powered-By: PHP/5.4.16\r\nCache-control: no-cache,must-revalidate\r\nLocation: /Home/account/login","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Moved Temporarily\r\nServer: nginx/1.14.0\r\nDate: Wed, 24 Nov 2021 07:07:46 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Powered-By: PHP/5.4.16\r\nCache-control: no-cache,must-revalidate\r\nLocation: /Home/account/login","certs":{"not_after":"2029-03-08 11:35:08","subject_cn":"cii.gov110.cn","issuer_cn":"cii.gov110.cn","subject_org":["FRI"],"issuer_org":["FRI"],"not_before":"2019-03-11 11:35:08","v":"v1","sn":"10791619571119056400","sig_alth":"SHA256-RSA"}},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2250\r\n# Server\r\nredis_version:3.2.3\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:672aed6eb816ad6c\r\nredis_mode:standalone\r\nos:Linux 3.10.0-327.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1190\r\nrun_id:6d025824403824521460fed893d6ae055fc054d6\r\ntcp_port:6379\r\nuptime_in_seconds:503688\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:********\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:108\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:74\r\n\r\n# Memory\r\nused_memory:8295416\r\nused_memory_human:7.91M\r\nused_memory_rss:12361728\r\nused_memory_rss_human:11.79M\r\nused_memory_peak:10212104\r\nused_memory_peak_human:9.74M\r\ntotal_system_memory:12431106048\r\ntotal_system_memory_human:11.58G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:1.49\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:4224\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637737549\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:220\r\ntotal_commands_processed:34692982\r\ninstantaneous_ops_per_sec:74\r\ntotal_net_input_bytes:1867781895\r\ntotal_net_output_bytes:441374296\r\ninstantaneous_input_kbps:4.13\r\ninstantaneous_output_kbps:0.64\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:1\r\nevicted_keys:0\r\nkeyspace_hits:553822\r\nkeyspace_misses:107438\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:227\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:1280.34\r\nused_cpu_user:703.15\r\nused_cpu_sys_children:24.34\r\nused_cpu_user_children:100.94\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=697,expires=11,avg_ttl=56197\r\ndb1:keys=1,expires=0,avg_ttl=0\r\n\r\n","certs":null},{"protocol":"telnet","port":23,"banner":"\\xff\\xfd\\x18\\xff\\xfd \\xff\\xfd#\\xff\\xfd'\\xff\\xfb\\x03\\xff\\xfd\\x01\\xff\\xfd\\x1f\\xff\\xfb\\x05\\xff\\xfd!\\xff\\xfe\\x01\\xff\\xfb\\x01\r\nKernel 3.10.0-327.el7.x86_64 on an x86_64\r\n\r\nKernel 3.10.0-327.el7.x86_64 on an x86_64\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":266,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"PHP Group","ports":[443,80],"title":"php"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443,80],"title":"jQuery"},{"belong_level":0,"rule_id":139,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"twitter","ports":[443,80],"title":"Bootstrap"},{"belong_level":0,"rule_id":20771,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443,80],"title":"layer.js"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Length: 432\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"527d139444e94e492e89b96a2477ce33","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"CIF API Documentation (production mode)","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:08:8a:87","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ubuntu-系统","IBM-Postfix","elastic-Elasticsearch","NGINX","OpenSSH","jQuery","Amaze-UI"],"cat_tags":["操作系统","电子邮件系统","数据库系统","服务","其他支撑系统","中间件","开发框架"],"company_tags":["Canonical Ltd.","International Business Machines Corporation","Elastic, Inc.","其他","The jQuery Foundation.","红芯时代（北京）科技有限公司"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"0665ec9779cbd459146d3aea7fdd8eb6","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\nContent-Type: application/json; charset=UTF-8\r\nContent-Length: 335\r\n\r\n{\n  \"status\" : 200,\n  \"name\" : \"node102\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"version\" : {\n    \"number\" : \"1.4.5\",\n    \"build_hash\" : \"2aaf797f2a571dcb779a3b61180afe8390ab61f9\",\n    \"build_timestamp\" : \"2015-04-27T08:06:06Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"4.10.4\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.4.6 (Ubuntu)\r\nDate: Wed, 24 Nov 2021 06:59:00 GMT\r\nContent-Type: text/html\r\nContent-Length: 4556\r\nLast-Modified: Thu, 13 Apr 2017 04:36:22 GMT\r\nConnection: close\r\nVary: Accept-Encoding\r\nETag: \"58ef0046-11cc\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET,POST,OPTIONS\r\nAccept-Ranges: bytes","certs":null},{"protocol":"smtp","port":25,"banner":"220 localhost ESMTP Postfix (Ubuntu)\r\n250-localhost\r\n250-PIPELINING\r\n250-SIZE 10240000\r\n250-VRFY\r\n250-ETRN\r\n250-STARTTLS\r\n250-ENHANCEDSTATUSCODES\r\n250-8BITMIME\r\n250 DSN\r\n502 5.5.2 Error: command not recognized\r\n","certs":null},{"protocol":"dns","port":53,"banner":"\\x00'x\\xae\\x81\\x82\\x00\\x01\\x00\\x00\\x00\\x00\\x00\\x01\\x06google\\x03com\\x00\\x00\\x01\\x00\\x01\\x00\\x00)\\x10\\x00\\x00\\x00\\x00\\x00\\x00\\x00\n\\x00P\\x00\\x06\\x85\\x00\\x00\\x01\\x00\\x01\\x00\\x01\\x00\\x00\\x07version\\x04bind\\x00\\x00\\x10\\x00\\x03\\xc0\f\\x00\\x10\\x00\\x03\\x00\\x00\\x00\\x00\\x00\\x18\\x179.9.5-3ubuntu0.8-Ubuntu\\xc0\f\\x00\\x02\\x00\\x03\\x00\\x00\\x00\\x00\\x00\\x02\\xc0\f","certs":null},{"protocol":"http","port":5000,"banner":"HTTP/1.0 200 OK\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 07:04:59 GMT\r\nContent-Length: 16178\r\nX-CIF-Media-Type: cif.v2\r\nConnection: close","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1p1 Ubuntu-2ubuntu2.8\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":4528,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Canonical Ltd.","ports":[53,25,80,22],"title":"ubuntu-系统"},{"belong_level":0,"rule_id":8304,"second_cat_tag":"电子邮件系统","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"International Business Machines Corporation","ports":[25],"title":"IBM-Postfix"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[80,5000],"title":"jQuery"},{"belong_level":0,"rule_id":7361,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"红芯时代（北京）科技有限公司","ports":[80],"title":"Amaze-UI"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Length: 368\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"53db92e72e0370bc43e23f0982302b8d","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":"00:0c:29:d7:58:8d","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["OpenSSH","NGINX","php","elastic-Elasticsearch","Oracle-MySQL","jQuery"],"cat_tags":["其他支撑系统","服务","脚本语言","数据库系统","中间件"],"company_tags":["其他","PHP Group","Elastic, Inc.","Oracle Corporation","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"1d51c5f4e5fe3b19e7b3c776b7e8871c","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\nContent-Type: application/json; charset=UTF-8\r\nContent-Length: 361\r\n\r\n{\n  \"name\" : \"Gravity\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"GNtcKlvgQ_62l0QtGSC1fw\",\n  \"version\" : {\n    \"number\" : \"2.4.3\",\n    \"build_hash\" : \"d38a34e7b75af4e17ead16f156feffa432b22be3\",\n    \"build_timestamp\" : \"2016-12-07T16:28:56Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"5.5.2\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 500 Internal Server Error\r\nServer: nginx/1.10.2\r\nDate: Wed, 24 Nov 2021 07:03:08 GMT\r\nContent-Type: text/html; charset=UTF-8\r\nConnection: close\r\nX-Powered-By: PHP/7.1.26","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.10.2\r\nDate: Wed, 24 Nov 2021 07:10:19 GMT\r\nContent-Type: text/html\r\nContent-Length: 1652\r\nLast-Modified: Tue, 02 Apr 2019 08:15:46 GMT\r\nConnection: close\r\nETag: \"5ca31a32-674\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.52-MariaDB \r\nR\\x00\\x00\\x00\n5.5.52-MariaDB\\x00\\xa5\\x00\\x00\\x00yY31:n<#\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x008208j/GW_Xz|\\x00mysql_native_password\\x00","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443],"title":"NGINX"},{"belong_level":0,"rule_id":266,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"PHP Group","ports":[80],"title":"php"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443],"title":"jQuery"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Length: 432\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"59cede3eb4725cdaeac72e0b0be2f3d0","_score":1,"_source":{"hosts":null,"ip":"***********","port":0,"name":"Sign in · GitLab","common_title":"GitLab信息泄露漏洞","mac":"00:0c:29:7d:6b:f4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Ruby","GitLab","RAILS-Framework"],"cat_tags":["服务","脚本语言","其他企业应用","开发框架"],"company_tags":["其他","GitLab Inc"],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"https://***********","vulfile":"GitLab_graphql_infoleak.json","url":"https://***********/api/graphql","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"1114360ebc68125ff1e619f8365b0b08","scan_engine":4,"port_list":[{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:55:56 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 99\r\nConnection: close\r\nCache-Control: no-cache\r\nLocation: https://***********/users/sign_in\r\nX-Content-Type-Options: nosniff\r\nX-Download-Options: noopen\r\nX-Frame-Options: DENY\r\nX-Permitted-Cross-Domain-Policies: none\r\nX-Request-Id: 01FN8BNGBTTFFNG7GKG2920F24\r\nX-Runtime: 0.147905\r\nX-Ua-Compatible: IE=edge\r\nX-Xss-Protection: 1; mode=block\r\nStrict-Transport-Security: max-age=63072000\r\nReferrer-Policy: strict-origin-when-cross-origin","certs":{"not_after":"2027-02-11 14:03:44","subject_cn":"*.baimaohui.net","issuer_cn":"*.baimaohui.net","subject_org":["Baimaohui"],"issuer_org":["Baimaohui"],"not_before":"2017-02-13 14:03:44","v":"v3","sn":"12132677475066784369","sig_alth":"SHA256-RSA"}}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443],"title":"NGINX"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":469,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"GitLab Inc","ports":[443],"title":"GitLab"},{"belong_level":0,"rule_id":9473,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"RAILS-Framework"}],"common_description":"GitLab是美国GitLab公司的一款使用Ruby on Rails开发的、自托管的、Git（版本控制系统）项目仓库应用程序。该程序可用于查阅项目的文件内容、提交历史、Bug列表等。Git是一套免费、开源的分布式版本控制系统。\nGitLab CE/EE affecting all versions starting from 13.4 before 13.6.2存在信息泄露漏洞，该漏洞源于通过GraphQL公开信息会导致用户的电子邮件可见。目前没有详细的漏洞细节提供。","common_impact":"<p>GitLab是美国GitLab公司的一款使用Ruby on Rails开发的、自托管的、Git（版本控制系统）项目仓库应用程序。该程序可用于查阅项目的文件内容、提交历史、Bug列表等。Git是一套免费、开源的分布式版本控制系统。</p><p>GitLab CE/EE affecting all versions starting from 13.4 before 13.6.2存在信息泄露漏洞，该漏洞源于通过GraphQL公开信息会导致用户的电子邮件可见。目前没有详细的漏洞细节提供。</p>","recommandation":"<p>厂商尚未提供漏洞修复方案，请关注厂商主页更新：</p><p><a href=\"https://gitlab.com/gitlab-org/cves/-/blob/master/2020/CVE-2020-26413.json\" style=\"\">https://gitlab.com/gitlab-org/cves/-/blob/master/2020/CVE-2020-26413.json</a></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"信息泄露","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nTransfer-Encoding: chunked\r\nCache-Control: max-age=0, private, must-revalidate\r\nConnection: keep-alive\r\nContent-Type: application/json; charset=utf-8\r\nDate: Wed, 24 Nov 2021 07:15:53 GMT\r\nEtag: W/\"fa37c25f072d26908d5a1fcfb53ba9cf\"\r\nPermissions-Policy: interest-cohort=()\r\nReferrer-Policy: strict-origin-when-cross-origin\r\nServer: nginx\r\nStrict-Transport-Security: max-age=63072000\r\nVary: Accept-Encoding\r\nVary: Origin\r\nX-Content-Type-Options: nosniff\r\nX-Download-Options: noopen\r\nX-Frame-Options: DENY\r\nX-Permitted-Cross-Domain-Policies: none\r\nX-Request-Id: 01FN8CT17DCNB4A5DHM2VJNSD6\r\nX-Runtime: 0.176133\r\nX-Ua-Compatible: IE=edge\r\nX-Xss-Protection: 1; mode=block\r\n\r\n{\"data\":{\"users\":{\"edges\":[{\"node\":{\"username\":\"dushixiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhangxiangqin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"mashiyun\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"renmengqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lijiahang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"shenhaidi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhaochenglong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gaojie\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"shiyalei\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"bilibin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"caiwanqiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"kangzejian\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhujiahao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lijialiang\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/240/avatar.png\",\"status\":{\"emoji\":\"soccer\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"wangyongqiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"majun\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xuehaichao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"qinjinlei\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liuqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xuhongzhou\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_615_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhangyanxu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhangyuwei\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"yanzeliang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangsen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wuyanbing\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"liuyuhan\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chenpf\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wumingyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"futianyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"caosiqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chentongcong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"fangzheng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_520_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"fuyuhang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"haochaoyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_545_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_581_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_548_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wufan\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wufang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"dengchengjie\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"huliankuo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"majingni\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"hewenchao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liangjing\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"douxianqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"licunyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangjianyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"xuzhiyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lirui\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"sunjiaqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liusong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_519_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhayubo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangpeibin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gongdaomeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liuyifan\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/195/avatar.png\",\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"sleeeeeping\",\"messageHtml\":\"sleeeeeping\"}}},{\"node\":{\"username\":\"liuyifang\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"sleeping\",\"messageHtml\":\"sleeping\"}}},{\"node\":{\"username\":\"gaolumeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wengfangchen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wenhaijiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"duanenjian\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"duy\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"anxianglang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhanfeiyang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lizhe\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"yuanmengting\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"qiaoshubo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lizhuang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xihuichao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lin3er\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lijiandong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"jinwenyan\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/179/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"shengguangsheng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/178/avatar.png\",\"status\":{\"emoji\":\"rosette\",\"message\":\"happy\",\"messageHtml\":\"happy\"}}},{\"node\":{\"username\":\"wangtianyang\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/177/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"rongjiale\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/176/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"chenhaiming\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lanjinkai\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"likai\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_447_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wushaohua\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/171/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"yangqianru\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/170/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"zhangzhen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"maojunpeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"alert-bot\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/167/alert-bot.png\",\"status\":null}},{\"node\":{\"username\":\"chaidakun\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"guoguangxing\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gaopeng2\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"support-bot\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/163/support-bot.png\",\"status\":null}},{\"node\":{\"username\":\"rentianyun\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"shiyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"guanshanqiu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chenyingao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"dengxinke\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"baolei\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"bicyclist\",\"message\":\"Making wheels...\",\"messageHtml\":\"Making wheels...\"}}},{\"node\":{\"username\":\"zhangqingcheng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/156/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"wangli\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/155/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"changxiaoyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wanghaima\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}}]}}}","has_response":1,"createtime":"2021-11-24 15:15:09","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:09"}}
{"_index":"fofaee_threats","_type":"threats","_id":"e7211739e1948e0238d9a1ddd41f1e01","_score":1,"_source":{"hosts":null,"ip":"************","port":9090,"name":"Grafana","common_title":"Prometheus监控告警平台未授权访问","mac":"30:9c:23:2c:ce:7b","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["OpenSSH","NGINX","Grafana_Labs-公司产品","jQuery","Bootstrap","Prometheus-Time-Series-Collection-and-Processing-Server"],"cat_tags":["其他支撑系统","服务","组件","中间件","开发框架"],"company_tags":["其他","Grafana Labs ","The jQuery Foundation.","twitter","The Linux Foundation."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9090","vulfile":"Prometheus_unauthorized.json","url":"http://************:9090/graph","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"3bfec50f6b7cfb7682186dfe98749941","scan_engine":4,"port_list":[{"protocol":"http","port":9090,"banner":"HTTP/1.0 302 Found\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /graph\r\nDate: Wed, 24 Nov 2021 06:54:18 GMT\r\nContent-Length: 29","certs":null},{"protocol":"unknown","port":9100,"banner":"","certs":null},{"protocol":"http","port":3000,"banner":"HTTP/1.0 302 Found\r\nCache-Control: no-cache\r\nContent-Type: text/html; charset=utf-8\r\nExpires: -1\r\nLocation: /login\r\nPragma: no-cache\r\nSet-Cookie: redirect_to=%2F; Path=/; HttpOnly; SameSite=Lax\r\nX-Frame-Options: deny\r\nDate: Wed, 24 Nov 2021 07:07:58 GMT\r\nContent-Length: 29","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:08:29 GMT\r\nContent-Type: text/html\r\nContent-Length: 162\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:10:35 GMT\r\nContent-Type: text/html\r\nContent-Length: 4396\r\nLast-Modified: Mon, 22 Nov 2021 02:27:18 GMT\r\nConnection: close\r\nETag: \"619b0006-112c\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443],"title":"NGINX"},{"belong_level":0,"rule_id":10043,"second_cat_tag":"组件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Grafana Labs ","ports":[3000],"title":"Grafana_Labs-公司产品"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[9090],"title":"jQuery"},{"belong_level":0,"rule_id":139,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"twitter","ports":[9090],"title":"Bootstrap"},{"belong_level":0,"rule_id":338397,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"The Linux Foundation.","ports":[9090],"title":"Prometheus-Time-Series-Collection-and-Processing-Server"}],"common_description":"Prometheus 是由 SoundCloud 开源监控告警解决方案。prometheus存储的是时序数据，即按相同时序(相同名称和标签)，以时间维度存储连续的数据的集合。该系统存在未授权访问问题，任何人都可随意进入该系统，泄露了系统中的信息。","common_impact":"<p>攻击者可直接进入该系统，获取服务器敏感信息，查看系统运行日志，并利用泄露的信息进行进一步攻击。&nbsp;<br></p>","recommandation":"<p>1、添加身份验证，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。</p><p>2、如非必要，禁止公网访问该系统。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nTransfer-Encoding: chunked\r\nContent-Type: text/html; charset=utf-8\r\nDate: Wed, 24 Nov 2021 07:16:55 GMT\r\n\r\n<!DOCTYPE html>\n<html lang=\"en\">\n    <head>\n        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n        <meta name=\"robots\" content=\"noindex,nofollow\">\n        <title>Prometheus Time Series Collection and Processing Server</title>\n        <link rel=\"shortcut icon\" href=\"/static/img/favicon.ico?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n        <script src=\"/static/vendor/js/jquery-3.3.1.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>    \n        <script src=\"/static/vendor/js/popper.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n        <script src=\"/static/vendor/bootstrap-4.3.1/js/bootstrap.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n\n        <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/vendor/bootstrap-4.3.1/css/bootstrap.min.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n        <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/css/prometheus.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n        <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/vendor/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n\n        <script>\n            var PATH_PREFIX = \"\";\n            var BUILD_VERSION = \"eba3fdcbf0d378b66600281903e3aab515732b39\";\n            $(function () {\n                $('[data-toggle=\"tooltip\"]').tooltip()\n            })\n        </script>\n\n        \n    <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/vendor/rickshaw/rickshaw.min.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n    <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/vendor/eonasdan-bootstrap-datetimepicker/bootstrap-datetimepicker.min.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n\n    <script src=\"/static/vendor/rickshaw/vendor/d3.v3.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/rickshaw/vendor/d3.layout.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/rickshaw/rickshaw.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/moment/moment.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/moment/moment-timezone-with-data.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/eonasdan-bootstrap-datetimepicker/bootstrap-datetimepicker.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/bootstrap3-typeahead/bootstrap3-typeahead.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/fuzzy/fuzzy.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n\n    <script src=\"/static/vendor/mustache/mustache.min.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    <script src=\"/static/vendor/js/jquery.selection.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n    \n\n    <script src=\"/static/js/graph/index.js?v=eba3fdcbf0d378b66600281903e3aab515732b39\"></script>\n\n    <script id=\"graph_template\" type=\"text/x-handlebars-template\"></script>\n\n    <link type=\"text/css\" rel=\"stylesheet\" href=\"/static/css/graph.css?v=eba3fdcbf0d378b66600281903e3aab515732b39\">\n\n    </head>\n\n    <body>\n        <nav class=\"navbar fixed-top navbar-expand-sm navbar-dark bg-dark\">\n            <div class=\"container-fluid\">      \n\n                <button type=\"button\" class=\"navbar-toggler\" data-toggle=\"collapse\" data-target=\"#nav-content\" aria-expanded=\"false\" aria-controls=\"nav-content\" aria-label=\"Toggle navigation\">\n                    <span class=\"navbar-toggler-icon\"></span>\n                    \n                </button>\n\n                <a class=\"navbar-brand\" href=\"/\">Prometheus</a>\n\n\n                <div id=\"nav-content\" class=\"navbar-collapse collapse\">\n                    <ul class=\"navbar-nav\">\n                        \n                        \n                        <li class=\"nav-item\"><a class=\"nav-link\" href=\"/alerts\">Alerts</a></li>\n                        <li class=\"nav-item\"><a class=\"nav-link\" href=\"/graph\">Graph</a></li>\n                        <li class=\"nav-item dropdown\">\n                            <a href=\"#\" class=\"nav-link dropdown-toggle\" data-toggle=\"dropdown\" role=\"button\" aria-haspopup=\"true\" aria-expanded=\"false\">Status <span class=\"caret\"></span></a>\n                            <div class=\"dropdown-menu\">\n                                <a class=\"dropdown-item\" href=\"/status\">Runtime &amp; Build Information</a>\n                                <a class=\"dropdown-item\" href=\"/flags\">Command-Line Flags</a>\n                                <a class=\"dropdown-item\" href=\"/config\">Configuration</a>\n                                <a class=\"dropdown-item\" href=\"/rules\">Rules</a>\n                                <a class=\"dropdown-item\" href=\"/targets\">Targets</a>\n                                <a class=\"dropdown-item\" href=\"/service-discovery\">Service Discovery</a>\n                            </div>\n                        </li>\n                        <li class= \"nav-item\" >\n                            <a class =\"nav-link\" href=\"https://prometheus.io/docs/prometheus/latest/getting_started/\" target=\"_blank\">Help</a>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n        </nav>\n\n        \n    <div id=\"graph_container\" class=\"container-fluid\">\n      <div class=\"clearfix\">\n        <div class=\"query-history\">\n          <i class=\"glyphicon glyphicon-unchecked\"></i>\n          <button type=\"button\" class=\"search-history\" title=\"search previous queries\">Enable query history</button>\n        </div>\n        <button type=\"button\" class=\"btn btn-link btn-sm new_ui_button\" onclick=\"window.location.pathname='/new/graph'\">Try experimental React UI</button>\n      </div>\n    </div>\n\n    <div class=\"container-fluid\">\n      <div><input class=\"btn btn-primary\" type=\"submit\" value=\"Add Graph\" id=\"add_graph\"></div>\n    </div>\n\n    </body>\n</html>\n\n\n\n","has_response":1,"createtime":"2021-11-24 15:15:39","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:39"}}
{"_index":"fofaee_threats","_type":"threats","_id":"7918d62e88caf7e10c4cc3d383526335","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"暴露面资产清查系统","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:89:15:17","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["APACHE-Tomcat","Oracle-MySQL","OpenSSH","elastic-Elasticsearch"],"cat_tags":["服务","数据库系统","其他支撑系统"],"company_tags":["Apache Software Foundation.","Oracle Corporation","其他","Elastic, Inc."],"task_ids":[2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"0840405f09796c49daa69264f2372a89","scan_engine":4,"port_list":[{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"https","port":8090,"banner":"HTTP/1.1 200 OK\r\nServer: Apache-Coyote/1.1\r\nCache-Control: private\r\nExpires: Thu, 01 Jan 1970 08:00:00 CST\r\nContent-Type: application/json;charset=UTF-8\r\nContent-Length: 66\r\nDate: Wed, 24 Nov 2021 07:08:05 GMT\r\nConnection: close","certs":{"not_after":"2027-11-14 06:05:11","subject_cn":"无名氏","issuer_cn":"无名氏","subject_org":["信息安全"],"issuer_org":["信息安全"],"not_before":"2017-11-16 06:05:11","v":"v3","sn":"1662554853","sig_alth":"SHA256-RSA"}},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 487\r\n\r\n{\n  \"name\" : \"node-1\",\n  \"cluster_name\" : \"fofa-es6\",\n  \"cluster_uuid\" : \"4aKDS2SiS1yX-d8M0H3_Pw\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.68-MariaDB \r\nR\\x00\\x00\\x00\n5.5.68-MariaDB\\x00\\xd7\\x01\\x00\\x00~aH')7xx\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00rQ!NFRLd5x},\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.0 302 Found\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nX-Download-Options: noopen\r\nX-Permitted-Cross-Domain-Policies: none\r\nReferrer-Policy: strict-origin-when-cross-origin\r\nLocation: http://************:8080/users/sign_in\r\nContent-Type: text/html; charset=utf-8\r\nCache-Control: no-cache\r\nSet-Cookie: _baolumian_session=d8BXUHGqCefcWiuojOo5uJsN67s2fCqJydqbQeGuZRI7bL5MFBOpm9SV0HT42t5uREjKEGWDx9zaJ4WmrJTzYKBXuV3qZ1Dp9PBzsaTGfJjyAWZe2xX86OwzzpYbA6Zd2ioMuOIAooBkEkvxPsUEvxb9%2BCZriYGd6%2FSmKyC%2BOnEYrAtgIZJhJQF63W46JjJVv%2BXLNzUQHOb9Onvb01cL98ZQVfzvpF%2FK%2Bc01TVy47dLD%2FufqNgy34khpVbz6qWMAr1ybkHyvX%2FYkvvzXAeN3isBMOKsTlUwn1kA%3D--lES%2BJFNigQUYP59P--LWAzUXVGuFLQh%2BffiJt0Hg%3D%3D; path=/; HttpOnly\r\nX-Request-Id: 3f030699-b1ee-4a62-b4a7-4ba1812e8857\r\nX-Runtime: 0.005553","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":210,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"Apache Software Foundation.","ports":[8090],"title":"APACHE-Tomcat"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"94d7c0d4778b3d066e71a6a0d9ed3578","_score":1,"_source":{"hosts":null,"ip":"***********","port":9200,"name":"通信网络安全防护管理系统","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:0d:f2:a7","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["redis","Linux-操作系统","NGINX","elastic-Elasticsearch","Oracle-MySQL","OpenSSH","jQuery"],"cat_tags":["数据库系统","操作系统","服务","其他支撑系统","中间件"],"company_tags":["Redis Labs","其他","Elastic, Inc.","Oracle Corporation","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://***********:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://***********:9200/_cat","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"a23a93e6c587a200cc3c188d2cb759bf","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2245\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6363\r\nrun_id:f120b8c1236351870c37adcec0f5fc025502e359\r\ntcp_port:6379\r\nuptime_in_seconds:502521\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10346492\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:13\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:1213440\r\nused_memory_human:1.16M\r\nused_memory_rss:7249920\r\nused_memory_rss_human:6.91M\r\nused_memory_peak:1694112\r\nused_memory_peak_human:1.62M\r\ntotal_system_memory:16657199104\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:49152\r\nused_memory_lua_human:48.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:5.97\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:31\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637736292\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:20102\r\ntotal_commands_processed:7221311\r\ninstantaneous_ops_per_sec:8\r\ntotal_net_input_bytes:2360798619\r\ntotal_net_output_bytes:35346192\r\ninstantaneous_input_kbps:2.82\r\ninstantaneous_output_kbps:0.03\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:5607\r\nevicted_keys:0\r\nkeyspace_hits:22957\r\nkeyspace_misses:3032442\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:1022\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:559.14\r\nused_cpu_user:480.30\r\nused_cpu_sys_children:5.15\r\nused_cpu_user_children:0.89\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=2,expires=1,avg_ttl=9556088\r\ndb1:keys=2,expires=2,avg_ttl=42998\r\n\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.20.1\r\nDate: Wed, 24 Nov 2021 06:54:54 GMT\r\nContent-Type: text/html\r\nContent-Length: 7109\r\nLast-Modified: Tue, 16 Nov 2021 03:58:48 GMT\r\nConnection: close\r\nETag: \"61932c78-1bc5\"\r\nAccept-Ranges: bytes","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 531\r\n\r\n{\n  \"name\" : \"node-1\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"gWEeGfnyQuiDnr1iET6oiw\",\n  \"version\" : {\n    \"number\" : \"7.7.1\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"ad56dce891c901a492bb1ee393f12dfff473a423\",\n    \"build_date\" : \"2020-05-28T16:30:01.040088Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"8.5.1\",\n    \"minimum_wire_compatibility_version\" : \"6.8.0\",\n    \"minimum_index_compatibility_version\" : \"6.0.0-beta1\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.35 \r\nJ\\x00\\x00\\x00\n5.7.35\\x00\\xfe\\xbd\\x00\\x007\\x05:!e%f\\x18\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00L6Q\tdLSkh\\x04L)\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":7077,"banner":"HTTP/1.1 400 Bad Request\r\nContent-Type: text/plain; charset=utf-8\r\nConnection: close\r\n\r\n400 Bad Request","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[7077],"title":"jQuery"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n/_cat/ml/anomaly_detectors\n/_cat/ml/anomaly_detectors/{job_id}\n/_cat/ml/trained_models\n/_cat/ml/trained_models/{model_id}\n/_cat/ml/datafeeds\n/_cat/ml/datafeeds/{datafeed_id}\n/_cat/ml/data_frame/analytics\n/_cat/ml/data_frame/analytics/{id}\n/_cat/transforms\n/_cat/transforms/{transform_id}\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"2b3bad7dae0cd5c54559afbf7425874f","_score":1,"_source":{"hosts":null,"ip":"************","port":8080,"name":"System Dashboard - BMH-Jira","common_title":"Atlassian Jira用户名枚举","mac":"00:0c:29:7c:d9:0c","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ATLASSIAN-JIRA","Oracle-JSP","Oracle-JAVA","OpenSSH","Oracle-MySQL","jQuery","Struts2"],"cat_tags":["其他企业应用","脚本语言","其他支撑系统","数据库系统","中间件","开发框架"],"company_tags":["Atlassian","Oracle Corporation","其他","The jQuery Foundation.","Apache Software Foundation."],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://************:8080","vulfile":"Jira_unauth_ViewUserHover_infoleak.json","url":"http://************:8080/secure/ViewUserHover.jspa?username=admin","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"7baceb9c9770e06331b217ffe77281a9","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.1 200 \r\nX-AREQUESTID: 861x4817x1\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Security-Policy: frame-ancestors 'self'\r\nX-ASEN: SEN-L17662553\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_4f9f5deecbf9690db021ef4b717b1b53d9101883_lout; Path=/\r\nX-AUSERNAME: anonymous\r\nSet-Cookie: JSESSIONID=7613BEFC2D4481ED80BFE4885A16A496; Path=/; HttpOnly\r\nX-Accel-Buffering: no\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:21:25 GMT\r\nConnection: close","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.34 \r\nJ\\x00\\x00\\x00\n5.7.34\\x00\\x0e\\xaf\\x03\\x00<x.w\u000b?\\x13\\x1e\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00#M\\x1aN(XD\\9KO:\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8090,"banner":"HTTP/1.1 503 \r\nSet-Cookie: JSESSIONID=167829123ECFC204D469471DB44CCD0F; Path=/; HttpOnly\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:42:04 GMT\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":10,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"Atlassian","ports":[8080],"title":"ATLASSIAN-JIRA"},{"belong_level":0,"rule_id":270,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JSP"},{"belong_level":0,"rule_id":22569,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JAVA"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8080,8090],"title":"jQuery"},{"belong_level":0,"rule_id":674,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Apache Software Foundation.","ports":[8080,8090],"title":"Struts2"}],"common_description":"Atlassian Jira是澳大利亚Atlassian公司的一套缺陷跟踪管理系统。该系统主要用于对工作中各类问题、缺陷进行跟踪管理。受影响的Atlassian Jira Server和Data Center版本允许未经身份验证的用户通过/ViewUserHover.jspa端点中的信息泄露漏洞枚举用户。受影响的版本是7.13.6之前的版本，8.5.7之前的8.0.0版本和8.12.0之前的8.6.0版本。Jira 存在信息泄漏漏洞，攻击者可利用该漏洞枚举用户信息。 ","common_impact":"<p><span style=\"font-size: medium;\">Atlassian Jira是澳大利亚Atlassian公司的一套缺陷跟踪管理系统。该系统主要用于对工作中各类问题、缺陷进行跟踪管理。</span></p><p><span style=\"font-size: medium;\">受影响的Atlassian Jira Server和Data Center版本允许未经身份验证的用户通过/ViewUserHover.jspa端点中的信息泄露漏洞枚举用户。受影响的版本是7.13.6之前的版本，8.5.7之前的8.0.0版本和8.12.0之前的8.6.0版本。Jira 存在信息泄漏漏洞，<span style=\"color: rgb(23, 43, 77);\">受影响的Atlassian Jira Server和Data Center版本允许未经身份验证的用户通过/ViewUserHover.jspa端点中的信息泄露漏洞枚举用户。</span></span></p>","recommandation":"<p style=\"text-align: start;\">1、目前厂商已发布升级补丁以修复漏洞，补丁获取链接：</p><p style=\"text-align: start;\"><a target=\"_Blank\" href=\"https://jira.atlassian.com/browse/JRASERVER-71560\">https://jira.atlassian.com/browse/JRASERVER-71560</a></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2020-14181","vulType":"信息泄露","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 \r\nTransfer-Encoding: chunked\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Security-Policy: frame-ancestors 'self'\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:45:00 GMT\r\nExpires: Thu, 01 Jan 1970 00:00:00 GMT\r\nPragma: no-cache\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_ea66b0240a137d3df5849069093eb25af60d4a34_lout; Path=/\r\nSet-Cookie: JSESSIONID=35BB543809F53DF60983C8CE94FC369F; Path=/; HttpOnly\r\nVary: User-Agent\r\nX-Arequestid: 885x4826x1\r\nX-Asen: SEN-L17662553\r\nX-Ausername: anonymous\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\n\r\n\n\n\n\n\n\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    \n\n\n\n\n\n\n\n\n\n<meta charset=\"utf-8\">\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\"/>\n<title> - BMH-Jira</title>\n<meta name=\"application-name\" content=\"JIRA\" data-name=\"jira\" data-version=\"8.0.2\"><meta name=\"ajs-server-scheme\" content=\"http\">\n<meta name=\"ajs-server-port\" content=\"8080\">\n<meta name=\"ajs-server-name\" content=\"************\">\n<meta name=\"ajs-behind-proxy\" content=\"null\">\n<meta name=\"ajs-base-url\" content=\"http://************:8080\">\n<meta name=\"ajs-dev-mode\" content=\"false\">\n<meta name=\"ajs-context-path\" content=\"\">\n<meta name=\"ajs-version-number\" content=\"8.0.2\">\n<meta name=\"ajs-build-number\" content=\"800010\">\n<meta name=\"ajs-is-beta\" content=\"false\">\n<meta name=\"ajs-is-rc\" content=\"false\">\n<meta name=\"ajs-is-snapshot\" content=\"false\">\n<meta name=\"ajs-is-milestone\" content=\"false\">\n<meta name=\"ajs-remote-user\" content=\"\">\n<meta name=\"ajs-remote-user-fullname\" content=\"\">\n<meta name=\"ajs-user-locale\" content=\"zh_CN\">\n<meta name=\"ajs-user-locale-group-separator\" content=\",\">\n<meta name=\"ajs-app-title\" content=\"BMH-Jira\">\n<meta name=\"ajs-keyboard-shortcuts-enabled\" content=\"true\">\n<meta name=\"ajs-keyboard-accesskey-modifier\" content=\"Ctrl+Alt\">\n<meta name=\"ajs-enabled-dark-features\" content=\"[&quot;com.atlassian.jira.agile.darkfeature.editable.detailsview&quot;,&quot;nps.survey.inline.dialog&quot;,&quot;com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled&quot;,&quot;jira.plugin.devstatus.phasetwo&quot;,&quot;jira.frother.reporter.field&quot;,&quot;atlassian.rest.xsrf.legacy.enabled&quot;,&quot;jira.issue.status.lozenge&quot;,&quot;com.atlassian.jira.config.BIG_PIPE&quot;,&quot;com.atlassian.jira.projects.issuenavigator&quot;,&quot;com.atlassian.jira.config.PDL&quot;,&quot;jira.plugin.devstatus.phasetwo.enabled&quot;,&quot;atlassian.aui.raphael.disabled&quot;,&quot;app-switcher.new&quot;,&quot;frother.assignee.field&quot;,&quot;com.atlassian.jira.projects.************************.Switch&quot;,&quot;jira.onboarding.cyoa&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.enabled&quot;,&quot;com.atlassian.jira.config.ProjectConfig.MENU&quot;,&quot;com.atlassian.jira.projects.sidebar.DEFER_RESOURCES&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled&quot;,&quot;com.atlassian.jira.agile.darkfeature.sprint.goal.enabled&quot;,&quot;jira.zdu.admin-updates-ui&quot;,&quot;jira.zdu.jmx-monitoring&quot;,&quot;sd.new.settings.sidebar.location.disabled&quot;,&quot;jira.zdu.cluster-upgrade-state&quot;,&quot;com.atlassian.jira.agile.darkfeature.splitissue&quot;,&quot;com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED&quot;,&quot;jira.export.csv.enabled&quot;]\">\n<meta name=\"ajs-in-admin-mode\" content=\"false\">\n<meta name=\"ajs-is-sysadmin\" content=\"false\">\n<meta name=\"ajs-is-admin\" content=\"false\">\n<meta name=\"ajs-outgoing-mail-enabled\" content=\"true\">\n<meta name=\"ajs-archiving-enabled\" content=\"true\">\n<meta name=\"ajs-date-relativize\" content=\"true\">\n<meta name=\"ajs-date-time\" content=\"h:mm a\">\n<meta name=\"ajs-date-day\" content=\"EEEE h:mm a\">\n<meta name=\"ajs-date-dmy\" content=\"dd/MMM/yy\">\n<meta name=\"ajs-date-complete\" content=\"dd/MMM/yy h:mm a\">\n<script type=\"text/javascript\">var AJS=AJS||{};AJS.debug=true;</script>\n\n\n    \n<meta id=\"atlassian-token\" name=\"atlassian-token\" content=\"BLLI-K16G-EL6P-NY5F_ea66b0240a137d3df5849069093eb25af60d4a34_lout\">\n\n\n\n<link rel=\"shortcut icon\" href=\"/s/-nco3pw/800010/6411e0087192541a09d88223fb51a6a0/_/images/fav-jsw.png\">\n<link rel=\"search\" type=\"application/opensearchdescription+xml\" href=\"/osd.jsp\" title=\" - BMH-Jira\"/>\n\n    \n\n\n\n<!--[if IE]><![endif]-->\n<script type=\"text/javascript\">\n    (function() {\n        var contextPath = '';\n        var eventBuffer = [];\n\n        function printDeprecatedMsg() {\n            if (console && console.warn) {\n                console.warn('DEPRECATED JS - contextPath global variable has been deprecated since 7.4.0. Use `wrm/context-path` module instead.');\n            }\n        }\n\n        function sendEvent(analytics, postfix) {\n            analytics.send({\n                name: 'js.globals.contextPath.' + postfix\n            });\n        }\n\n        function sendDeprecatedEvent(postfix) {\n            try {\n                var analytics = require('jira/analytics');\n                if (eventBuffer.length) {\n                    eventBuffer.forEach(function(value) {\n                        sendEvent(analytics, value);\n                    });\n                    eventBuffer = [];\n                }\n\n                if (postfix) {\n                    sendEvent(analytics, postfix);\n                }\n            } catch(ex) {\n                eventBuffer.push(postfix);\n                setTimeout(sendDeprecatedEvent, 1000);\n            }\n        }\n\n        Object.defineProperty(window, 'contextPath', {\n            get: function() {\n                printDeprecatedMsg();\n                sendDeprecatedEvent('get');\n                return contextPath;\n            },\n            set: function(value) {\n                printDeprecatedMsg();\n                sendDeprecatedEvent('set');\n                contextPath = value;\n            }\n        });\n    })();\n\n</script>\n<script>\nwindow.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};\nWRM._unparsedData[\"com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path\"]=\"\\\"\\\"\";\nWRM._unparsedData[\"jira.core:feature-flags-data.feature-flag-data\"]=\"{\\\"enabled-feature-keys\\\":[\\\"com.atlassian.jira.agile.darkfeature.editable.detailsview\\\",\\\"nps.survey.inline.dialog\\\",\\\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled\\\",\\\"jira.plugin.devstatus.phasetwo\\\",\\\"jira.frother.reporter.field\\\",\\\"atlassian.rest.xsrf.legacy.enabled\\\",\\\"jira.issue.status.lozenge\\\",\\\"com.atlassian.jira.config.BIG_PIPE\\\",\\\"com.atlassian.jira.projects.issuenavigator\\\",\\\"com.atlassian.jira.config.PDL\\\",\\\"jira.plugin.devstatus.phasetwo.enabled\\\",\\\"atlassian.aui.raphael.disabled\\\",\\\"app-switcher.new\\\",\\\"frother.assignee.field\\\",\\\"com.atlassian.jira.projects.************************.Switch\\\",\\\"jira.onboarding.cyoa\\\",\\\"com.atlassian.jira.agile.darkfeature.kanplan.enabled\\\",\\\"com.atlassian.jira.config.ProjectConfig.MENU\\\",\\\"com.atlassian.jira.projects.sidebar.DEFER_RESOURCES\\\",\\\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled\\\",\\\"com.atlassian.jira.agile.darkfeature.sprint.goal.enabled\\\",\\\"jira.zdu.admin-updates-ui\\\",\\\"jira.zdu.jmx-monitoring\\\",\\\"sd.new.settings.sidebar.location.disabled\\\",\\\"jira.zdu.cluster-upgrade-state\\\",\\\"com.atlassian.jira.agile.darkfeature.splitissue\\\",\\\"com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED\\\",\\\"jira.export.csv.enabled\\\"],\\\"feature-flag-states\\\":{\\\"mail.batching.override.core\\\":true,\\\"jira.spectrum.m1\\\":true,\\\"jira.spectrum.m2\\\":false,\\\"com.atlassian.jira.issuetable.draggable\\\":true,\\\"mail.batching\\\":false,\\\"com.atlassian.jira.agile.darkfeature.kanban.hide.old.done.issues\\\":true,\\\"jira.jql.suggestrecentfields\\\":false,\\\"com.atlassian.jira.agile.darkfeature.backlog.showmore\\\":true,\\\"com.atlassian.jira.agile.darkfeature.optimistic.transitions\\\":true,\\\"com.atlassian.jira.issuetable.move.links.hidden\\\":true,\\\"jira.renderer.consider.variable.format\\\":true,\\\"com.atlassian.jira.agile.darkfeature.kanplan\\\":false,\\\"jira.priorities.per.project.jsd\\\":true,\\\"jira.instrumentation.laas\\\":false,\\\"com.atlassian.jira.agile.darkfeature.rapid.boards.bands\\\":true,\\\"com.atlassian.jira.sharedEntityEditRights\\\":true,\\\"jira.customfields.paginated.ui\\\":true,\\\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint\\\":false,\\\"jira.create.linked.issue\\\":true,\\\"mail.batching.user.notification\\\":true,\\\"jira.spectrum.m1b\\\":true,\\\"com.atlassian.jira.agile.darkfeature.sprint.goal\\\":false,\\\"com.atlassian.jira.agile.darkfeature.dataonpageload\\\":true,\\\"com.atlassian.jira.agile.darkfeature.sidebar.boards.list\\\":true,\\\"jira.sal.host.connect.accessor.existing.transaction.will.create.transactions\\\":true,\\\"com.atlassian.jira.custom.csv.escaper\\\":true,\\\"com.atlassian.jira.plugin.issuenavigator.filtersUxImprovment\\\":true,\\\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions\\\":false,\\\"jira.quick.search\\\":true,\\\"jira.jql.smartautoselectfirst\\\":false,\\\"com.atlassian.jira.projects.per.project.permission.query\\\":true,\\\"com.atlassian.jira.issues.archiving\\\":false,\\\"com.atlassian.jira.projects.archiving\\\":true,\\\"index.use.snappy\\\":true,\\\"jira.priorities.per.project\\\":true,\\\"com.atlassian.jira.upgrade.startup.fix.index\\\":true}}\";\nWRM._unparsedData[\"jira.core:default-comment-security-level-data.DefaultCommentSecurityLevelHelpLink\"]=\"{\\\"extraClasses\\\":\\\"default-comment-level-help\\\",\\\"title\\\":\\\"Commenting on an Issue\\\",\\\"url\\\":\\\"https://docs.atlassian.com/jira/jcore-docs-080/Editing+and+collaborating+on+issues#Editingandcollaboratingonissues-restrictacomment\\\",\\\"isLocal\\\":false}\";\nWRM._unparsedData[\"com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider\"]=\"false\";\nWRM._unparsedData[\"com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider\"]=\"false\";\nWRM._unparsedData[\"jira.core:dateFormatProvider.allFormats\"]=\"{\\\"dateFormats\\\":{\\\"meridiem\\\":[\\\"\\u4e0a\\u5348\\\",\\\"\\u4e0b\\u5348\\\"],\\\"eras\\\":[\\\"\\u516c\\u51","has_response":1,"createtime":"2021-11-24 15:15:34","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:34"}}
{"_index":"fofaee_threats","_type":"threats","_id":"440a90878be108543e3af620e47524a8","_score":1,"_source":{"hosts":null,"ip":"************","port":6379,"name":"登录-互联网暴露面风险自查平台","common_title":"Redis未授权访问漏洞","mac":"00:0c:29:c0:7d:06","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","redis","Linux-操作系统","NGINX","php","OpenSSH","elastic-Elasticsearch","jQuery","Bootstrap","layer.js"],"cat_tags":["数据库系统","操作系统","服务","脚本语言","其他支撑系统","中间件","开发框架"],"company_tags":["Oracle Corporation","Redis Labs","其他","PHP Group","Elastic, Inc.","The jQuery Foundation.","twitter"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:6379","vulfile":"redis_unauthorized_access.json","url":"************:6379","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"fcc689df2516e1741a90e9a1bc8946b0","scan_engine":4,"port_list":[{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.52-MariaDB \r\nR\\x00\\x00\\x00\n5.5.52-MariaDB\\x00\\x99.\\x00\\x00U(eFlnd+\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00F`2tuoMs{1~L\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\nContent-Type: application/json; charset=UTF-8\r\nContent-Length: 317\r\n\r\n{\n  \"name\" : \"Aqueduct\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"version\" : {\n    \"number\" : \"2.4.0\",\n    \"build_hash\" : \"ce9f0c7394dee074091dd1bc4e9469251181fc55\",\n    \"build_timestamp\" : \"2016-08-29T09:14:17Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"5.5.2\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 302 Moved Temporarily\r\nServer: nginx/1.14.0\r\nDate: Wed, 24 Nov 2021 07:01:22 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Powered-By: PHP/5.4.16\r\nCache-control: no-cache,must-revalidate\r\nLocation: /Home/account/login","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Moved Temporarily\r\nServer: nginx/1.14.0\r\nDate: Wed, 24 Nov 2021 07:07:46 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Powered-By: PHP/5.4.16\r\nCache-control: no-cache,must-revalidate\r\nLocation: /Home/account/login","certs":{"not_after":"2029-03-08 11:35:08","subject_cn":"cii.gov110.cn","issuer_cn":"cii.gov110.cn","subject_org":["FRI"],"issuer_org":["FRI"],"not_before":"2019-03-11 11:35:08","v":"v1","sn":"10791619571119056400","sig_alth":"SHA256-RSA"}},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2250\r\n# Server\r\nredis_version:3.2.3\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:672aed6eb816ad6c\r\nredis_mode:standalone\r\nos:Linux 3.10.0-327.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1190\r\nrun_id:6d025824403824521460fed893d6ae055fc054d6\r\ntcp_port:6379\r\nuptime_in_seconds:503688\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:********\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:108\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:74\r\n\r\n# Memory\r\nused_memory:8295416\r\nused_memory_human:7.91M\r\nused_memory_rss:12361728\r\nused_memory_rss_human:11.79M\r\nused_memory_peak:10212104\r\nused_memory_peak_human:9.74M\r\ntotal_system_memory:12431106048\r\ntotal_system_memory_human:11.58G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:1.49\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:4224\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637737549\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:220\r\ntotal_commands_processed:34692982\r\ninstantaneous_ops_per_sec:74\r\ntotal_net_input_bytes:1867781895\r\ntotal_net_output_bytes:441374296\r\ninstantaneous_input_kbps:4.13\r\ninstantaneous_output_kbps:0.64\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:1\r\nevicted_keys:0\r\nkeyspace_hits:553822\r\nkeyspace_misses:107438\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:227\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:1280.34\r\nused_cpu_user:703.15\r\nused_cpu_sys_children:24.34\r\nused_cpu_user_children:100.94\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=697,expires=11,avg_ttl=56197\r\ndb1:keys=1,expires=0,avg_ttl=0\r\n\r\n","certs":null},{"protocol":"telnet","port":23,"banner":"\\xff\\xfd\\x18\\xff\\xfd \\xff\\xfd#\\xff\\xfd'\\xff\\xfb\\x03\\xff\\xfd\\x01\\xff\\xfd\\x1f\\xff\\xfb\\x05\\xff\\xfd!\\xff\\xfe\\x01\\xff\\xfb\\x01\r\nKernel 3.10.0-327.el7.x86_64 on an x86_64\r\n\r\nKernel 3.10.0-327.el7.x86_64 on an x86_64\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":266,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"PHP Group","ports":[443,80],"title":"php"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443,80],"title":"jQuery"},{"belong_level":0,"rule_id":139,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"twitter","ports":[443,80],"title":"Bootstrap"},{"belong_level":0,"rule_id":20771,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443,80],"title":"layer.js"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$2250\r\n# Server\r\nredis_version:3.2.3\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:672aed6eb816ad6c\r\nredis_mode:standalone\r\nos:Linux 3.10.0-327.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1190\r\nrun_id:6d025824403824521460fed893d6ae055fc054d6\r\ntcp_port:6379\r\nuptime_in_seconds:504313\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348461\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:108\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:74\r\n\r\n# Memory\r\nused_memory:8405920\r\nused_memory_human:8.02M\r\nused_memory_rss:12361728\r\nused_memory_rss_human:11.79M\r\nused_memory_peak:10212104\r\nused_memory_peak_human:9.74M\r\ntotal_system_memory:12431106048\r\ntotal_system_memory_human:11.58G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:1.47\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_la","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"7b3ea793112931482b246da94a9e3a91","_score":1,"_source":{"hosts":null,"ip":"************","port":6379,"name":"网络空间资产管理系统 - 网络空间测绘管理系统","common_title":"Redis未授权访问漏洞","mac":"00:0c:29:c0:bc:4f","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","redis","Linux-操作系统","OpenSSH","Ruby","layer.js"],"cat_tags":["数据库系统","操作系统","其他支撑系统","脚本语言","开发框架"],"company_tags":["Oracle Corporation","Redis Labs","其他"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:6379","vulfile":"redis_unauthorized_access.json","url":"************:6379","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"925a70902bdf336a780568d8d3661df0","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2239\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 3.10.0-693.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1055\r\nrun_id:21108958748af5ef43246e32d9a94b683bf5ca13\r\ntcp_port:6379\r\nuptime_in_seconds:504264\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10346972\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:31\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:16\r\n\r\n# Memory\r\nused_memory:2079816\r\nused_memory_human:1.98M\r\nused_memory_rss:7335936\r\nused_memory_rss_human:7.00M\r\nused_memory_peak:2194104\r\nused_memory_peak_human:2.09M\r\ntotal_system_memory:16657932288\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:3.53\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:768\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637736806\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:67\r\ntotal_commands_processed:9547705\r\ninstantaneous_ops_per_sec:43\r\ntotal_net_input_bytes:590344752\r\ntotal_net_output_bytes:83246362\r\ninstantaneous_input_kbps:2.73\r\ninstantaneous_output_kbps:0.43\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:4\r\nevicted_keys:0\r\nkeyspace_hits:16\r\nkeyspace_misses:133942\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:1053\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:812.89\r\nused_cpu_user:478.79\r\nused_cpu_sys_children:5.74\r\nused_cpu_user_children:6.34\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb5:keys=638,expires=2,avg_ttl=56007\r\ndb6:keys=546,expires=2,avg_ttl=56252\r\n\r\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":3000,"banner":"HTTP/1.0 200 OK\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nContent-Type: text/html; charset=utf-8\r\nETag: W/\"cef6b85642d0fe83e5585fcaae4e0765\"\r\nCache-Control: max-age=0, private, must-revalidate\r\nSet-Cookie: _fofapro_session=S05QR0tOT1JIVnRRenljYXBScnlUck51NGc3Y0xNb2VtYytXSlhYOUdBRHZkSFk5T2VaTi9WMGYrZm5xM2pFUHcrNEJ4WFhTRDU5VHRlb0hxNWg1QzdrZFF6eVdidktHUHVIRWVMWWllWFozRHl5OW1nTUo2SzlzYzBxNXQvYlFZWjJXbzBHWTZjeEorQlVyWlpFUk5LQ3FQWHVjem55bDJXNVU0azh6K1Jvd0dPeDl2bjh5M1BGa1BkYU84di94LS0xN09ES0daNEt4VlVTMFpjTDJHaUNBPT0%3D--48410a2ff90fc5e8d45db52c5f5797994f4ccbc8; path=/; HttpOnly\r\nX-Request-Id: a6be324c-813c-43a7-9b0e-deb2a690c87d\r\nX-Runtime: 0.024386\r\nContent-Length: 32717","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.32 \r\nJ\\x00\\x00\\x00\n5.7.32\\x00'\\x01\\x00\\x00r\\x0fv\\x17F\\x048T\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\rX\r\\x1a\\x1eMziTu\\x19>\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":4000,"banner":"HTTP/1.0 200 OK\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nContent-Type: text/html; charset=utf-8\r\nETag: W/\"37f56f455a3f3da8e41b3661928d1d30\"\r\nCache-Control: max-age=0, private, must-revalidate\r\nSet-Cookie: _fofapro_session=alVteWJPNFg1em90aWN2Z29NN0Y1ZXB4MDYrcDlYWEFNanRmM09obFRNN2FvU05EYlAzeEx6TkQ0R0d5TklsOFdNZmFQYW1CY003UnUydEZhelpWTnhJUDZKa3RHQi8vcnNEOXRtRjZOTnBPekpDeDVWQURRWlJPelJERE00b1o1ZktoajVHM1Z0VmErK3h1R3p1UXFJZGl4UUxJVHgvQ3VYTlovQ2FpRW01dHUvQzNIRC92Uy9EaVYxbWd1N2lLLS1PektLbVp1Y3ZuREp3c3g0MWdFUWZ3PT0%3D--859aefe944bd9043b333758dffc10a6a6af7d857; path=/; HttpOnly\r\nX-Request-Id: b68cfeb2-ea40-486c-8497-ca1423dbd1f0\r\nX-Runtime: 0.022930\r\nContent-Length: 33262","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[4000,3000],"title":"Ruby"},{"belong_level":0,"rule_id":20771,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[4000,3000],"title":"layer.js"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$2238\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 3.10.0-693.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1055\r\nrun_id:21108958748af5ef43246e32d9a94b683bf5ca13\r\ntcp_port:6379\r\nuptime_in_seconds:505641\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348349\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:31\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:16\r\n\r\n# Memory\r\nused_memory:1932448\r\nused_memory_human:1.84M\r\nused_memory_rss:7335936\r\nused_memory_rss_human:7.00M\r\nused_memory_peak:2194104\r\nused_memory_peak_human:2.09M\r\ntotal_system_memory:16657932288\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:3.80\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_s","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"4c21b0e610f41be74ece354f0a6dc79a","_score":1,"_source":{"hosts":null,"ip":"************","port":2181,"name":null,"common_title":"zookeeper四字命令任意执行","mac":"24:1c:04:75:20:d0","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","OpenSSH","Oracle-MySQL","APACHE-ZooKeeper","php"],"cat_tags":["服务","其他支撑系统","数据库系统","大数据处理","脚本语言"],"company_tags":["其他","Oracle Corporation","Apache Software Foundation.","PHP Group"],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://************:2181","vulfile":"zookeeper_disclouser.json","url":"************:2181","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"28da84eb0f5fececcf88fbb8ad50a839","scan_engine":4,"port_list":[{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.52-MariaDB \r\nR\\x00\\x00\\x00\n5.5.52-MariaDB\\x009e\\x00\\x00g:,kXX2.\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00/@4$Vf~hlf<7\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8888,"banner":"HTTP/1.0 404 Not Found\r\nHost: ************:8888\r\nDate: Wed, 24 Nov 2021 06:56:09 GMT\r\nConnection: close\r\nX-Powered-By: PHP/7.4.20\r\nCache-Control: no-cache, private\r\ndate: Wed, 24 Nov 2021 06:56:09 GMT\r\nContent-type: text/html; charset=UTF-8","certs":null},{"protocol":"kafka","port":9092,"banner":"Kafka\nAddr: ************:9092\nTopics: __consumer_offsets test001","certs":null},{"protocol":"zookeeper","port":2181,"banner":"Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:48916[0](queued=0,recved=1,sent=0)\n /*.*.*.*:43530[1](queued=0,recved=75039,sent=75040)\n\nLatency min/avg/max: 0/0/214\nReceived: 75156\nSent: 75156\nConnections: 2\nOutstanding: 0\nZxid: 0x340\nMode: standalone\nNode count: 144\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.18.0\r\nDate: Wed, 24 Nov 2021 07:05:07 GMT\r\nContent-Type: text/html\r\nContent-Length: 4118\r\nLast-Modified: Mon, 22 Nov 2021 01:37:17 GMT\r\nConnection: close\r\nETag: \"619af44d-1016\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx/1.18.0\r\nDate: Wed, 24 Nov 2021 07:07:40 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":21157,"second_cat_tag":"大数据处理","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Apache Software Foundation.","ports":[2181],"title":"APACHE-ZooKeeper"},{"belong_level":0,"rule_id":266,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"PHP Group","ports":[8888],"title":"php"}],"common_description":"ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。","common_impact":"<p>ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。</p>","recommandation":"<p>1、3.4.10以上版本可禁止四字命令，在安装目录下conf/zoo.cfg文件中加一条配置，如下：</p><blockquote><p>4lw.commands.whitelist=</p></blockquote><p>可能影响业务，请确认后再操作。</p><p>2、通过防火墙等安全设备设置访问策略，设置白名单访问。</p><p>3、禁止把Zookeeper直接暴露在公网。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":" /************:36388[0](queued=0,recved=1,sent=0)\n /127.0.0.1:43530[1](queued=0,recved=75198,sent=75199,sid=0x10000011d040001,lop=PING,est=1637290922102,to=18000,lcxid=0x6c,lzxid=0x340,lresp=*********,llat=0,minlat=0,avglat=0,maxlat=214)\n\n","has_response":1,"createtime":"2021-11-24 15:18:35","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:18:35"}}
{"_index":"fofaee_threats","_type":"threats","_id":"4ed6b864a7a12b91923f99c55625ab14","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","OpenSSH","NGINX","elastic-Elasticsearch"],"cat_tags":["数据库系统","其他支撑系统","服务"],"company_tags":["Oracle Corporation","其他","Elastic, Inc."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"f568d6ed7774eee40859a7034880453a","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.0 404 Not Found\r\nContent-Type: text/plain; charset=utf-8\r\nX-Content-Type-Options: nosniff\r\nDate: Wed, 24 Nov 2021 06:50:00 GMT\r\nContent-Length: 19","certs":null},{"protocol":"mysql","port":3306,"banner":"G\\x00\\x00\\x00\\xffj\\x04Host '************' is not allowed to connect to this MariaDB server","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 493\r\n\r\n{\n  \"name\" : \"UVmIfn8\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"hbFzdwLlR3OfhTViYu7Zzw\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":8000,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:52:21 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:55:23 GMT\r\nContent-Type: text/html\r\nContent-Length: 6354\r\nLast-Modified: Wed, 24 Nov 2021 06:17:27 GMT\r\nConnection: close\r\nETag: \"619dd8f7-18d2\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":{"not_after":"2020-06-15 23:59:59","subject_cn":"focii.cn","issuer_cn":"Sectigo RSA Domain Validation Secure Server CA","issuer_org":["Sectigo Limited"],"not_before":"2019-05-13 00:00:00","v":"v3","domain":"focii.cn","sn":"182191660422272240828351337638536332243","sig_alth":"SHA256-RSA"}},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 07:08:26 GMT\r\nContent-Type: text/html\r\nContent-Length: 185\r\nConnection: close\r\nLocation: https://************:80/","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443,8000],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"ec5ea36f248072fa7ab6cca8028e2ba3","_score":1,"_source":{"hosts":null,"ip":"***********","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":"00:0c:29:e8:2c:a2","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Oracle-MySQL","redis","Linux-操作系统","elastic-Elasticsearch","OpenSSH"],"cat_tags":["服务","数据库系统","操作系统","其他支撑系统"],"company_tags":["其他","Oracle Corporation","Redis Labs","Elastic, Inc."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://***********:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://***********:9200/_cat","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"931708c9f49d4f0b101f3c14d1bb27f7","scan_engine":4,"port_list":[{"protocol":"http","port":8000,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:49:06 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:50:01 GMT\r\nContent-Type: text/html\r\nContent-Length: 6354\r\nLast-Modified: Wed, 24 Nov 2021 03:52:16 GMT\r\nConnection: close\r\nETag: \"619db6f0-18d2\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.68-MariaDB \r\nR\\x00\\x00\\x00\n5.5.68-MariaDB\\x00\\x84I\\x00\\x00ggP:n?/m\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00XOc%*F^+VS};\\x00mysql_native_password\\x00","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 07:08:28 GMT\r\nContent-Type: text/html\r\nContent-Length: 6354\r\nLast-Modified: Wed, 24 Nov 2021 03:52:16 GMT\r\nConnection: close\r\nETag: \"619db6f0-18d2\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":{"not_after":"2020-06-15 23:59:59","subject_cn":"focii.cn","issuer_cn":"Sectigo RSA Domain Validation Secure Server CA","issuer_org":["Sectigo Limited"],"not_before":"2019-05-13 00:00:00","v":"v3","domain":"focii.cn","sn":"182191660422272240828351337638536332243","sig_alth":"SHA256-RSA"}},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 493\r\n\r\n{\n  \"name\" : \"NryzzqS\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"rLkxJSu6RZ6DOAjsIiRwwA\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2210\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 5.9.11-1.el7.elrepo.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6977\r\nrun_id:82c55bba1b758f46c7a65fd1cf939dcb619f8015\r\ntcp_port:6379\r\nuptime_in_seconds:504581\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348084\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:15\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:6\r\n\r\n# Memory\r\nused_memory:1178432\r\nused_memory_human:1.12M\r\nused_memory_rss:7319552\r\nused_memory_rss_human:6.98M\r\nused_memory_peak:1579280\r\nused_memory_peak_human:1.51M\r\ntotal_system_memory:8350412800\r\ntotal_system_memory_human:7.78G\r\nused_memory_lua:41984\r\nused_memory_lua_human:41.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:6.21\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:37\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637737785\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:46850\r\ntotal_commands_processed:7156762\r\ninstantaneous_ops_per_sec:19\r\ntotal_net_input_bytes:444713744\r\ntotal_net_output_bytes:42747430\r\ninstantaneous_input_kbps:1.18\r\ninstantaneous_output_kbps:0.11\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:2669\r\nevicted_keys:0\r\nkeyspace_hits:76617\r\nkeyspace_misses:1124934\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:443\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:933.50\r\nused_cpu_user:439.22\r\nused_cpu_sys_children:1.85\r\nused_cpu_user_children:0.73\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=40,expires=2,avg_ttl=45483\r\n\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443,8000],"title":"NGINX"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"23b7176ab2607deffe841f09039b2f6a","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"Cland Beta","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:2d:d5:93","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["OpenSSH","elastic-Elasticsearch","vmware-Spring-Framework"],"cat_tags":["其他支撑系统","数据库系统","开发框架"],"company_tags":["其他","Elastic, Inc.","Pivotal Software, Inc."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"02d1fbf7dbd49013a4a40b025fd82fce","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"HzRiFAS\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"YH9nXVDmQdyLCRBY1bLbhw\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 404 \r\nContent-Type: application/json\r\nDate: Wed, 24 Nov 2021 06:50:35 GMT\r\nConnection: close","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_x.x\r\n","certs":null},{"protocol":"openvpn-udp","port":1194,"banner":"@\\xc1\\x1f9Na\\xf9\\xf5\\x1e\\x00\\x00\\x00\\x00\\x00","certs":null},{"protocol":"http","port":12345,"banner":"HTTP/1.0 307 Temporary Redirect\r\nContent-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; object-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self'; media-src 'self'; frame-src 'self'; font-src 'self' data:; connect-src 'self'\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /cland/\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\nDate: Wed, 24 Nov 2021 07:04:33 GMT\r\nContent-Length: 43","certs":null},{"protocol":"java-rmi","port":1099,"banner":"N\\x00\f*.*.*.*\\x00\\x00\\xe8L","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":8772,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Pivotal Software, Inc.","ports":[8080],"title":"vmware-Spring-Framework"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"fefcf122cf75b3858dae0ed78ee7f4db","_score":1,"_source":{"hosts":null,"ip":"**********9","port":0,"name":"Sign in · GitLab","common_title":"GitLab信息泄露漏洞","mac":"00:0c:29:67:ab:44","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Ruby","GitLab","RAILS-Framework"],"cat_tags":["服务","脚本语言","其他企业应用","开发框架"],"company_tags":["其他","GitLab Inc"],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"https://**********9","vulfile":"GitLab_graphql_infoleak.json","url":"https://**********9/api/graphql","obj_type":1,"object":"**********9","intranet_ip":1,"addition":null,"merge_md5":"187f0b3dac192fabb6f210e9155a56ee","scan_engine":4,"port_list":[{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:07:57 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 99\r\nConnection: close\r\nCache-Control: no-cache\r\nLocation: https://**********9/users/sign_in\r\nSet-Cookie: experimentation_subject_id=eyJfcmFpbHMiOnsibWVzc2FnZSI6IklqUXdOVFkxWXpoaExUSmlabVF0TkdRd01pMWhOV1ppTFdKbVpEWTBOamMzWXpjMllpST0iLCJleHAiOm51bGwsInB1ciI6ImNvb2tpZS5leHBlcmltZW50YXRpb25fc3ViamVjdF9pZCJ9fQ%3D%3D--49a2567a0c6fb05b05696e87e0933a6df2fbe2d1; path=/; expires=Sun, 24 Nov 2041 07:07:57 GMT; secure; HttpOnly; SameSite=None\r\nX-Content-Type-Options: nosniff\r\nX-Download-Options: noopen\r\nX-Frame-Options: DENY\r\nX-Gitlab-Feature-Category: projects\r\nX-Permitted-Cross-Domain-Policies: none\r\nX-Request-Id: JN4XuuHglG\r\nX-Runtime: 0.018909\r\nX-Ua-Compatible: IE=edge\r\nX-Xss-Protection: 1; mode=block\r\nStrict-Transport-Security: max-age=31536000\r\nReferrer-Policy: strict-origin-when-cross-origin","certs":{"not_after":"2027-02-11 14:03:44","subject_cn":"*.baimaohui.net","issuer_cn":"*.baimaohui.net","subject_org":["Baimaohui"],"issuer_org":["Baimaohui"],"not_before":"2017-02-13 14:03:44","v":"v3","sn":"12132677475066784369","sig_alth":"SHA256-RSA"}},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111), 100024 v1 UDP(45931), 100024 v1 TCP(39237)","certs":null},{"protocol":"http","port":8060,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.18.0\r\nDate: Wed, 24 Nov 2021 07:14:07 GMT\r\nContent-Type: text/html\r\nContent-Length: 153\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,8060],"title":"NGINX"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":469,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"GitLab Inc","ports":[443],"title":"GitLab"},{"belong_level":0,"rule_id":9473,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"RAILS-Framework"}],"common_description":"GitLab是美国GitLab公司的一款使用Ruby on Rails开发的、自托管的、Git（版本控制系统）项目仓库应用程序。该程序可用于查阅项目的文件内容、提交历史、Bug列表等。Git是一套免费、开源的分布式版本控制系统。\nGitLab CE/EE affecting all versions starting from 13.4 before 13.6.2存在信息泄露漏洞，该漏洞源于通过GraphQL公开信息会导致用户的电子邮件可见。目前没有详细的漏洞细节提供。","common_impact":"<p>GitLab是美国GitLab公司的一款使用Ruby on Rails开发的、自托管的、Git（版本控制系统）项目仓库应用程序。该程序可用于查阅项目的文件内容、提交历史、Bug列表等。Git是一套免费、开源的分布式版本控制系统。</p><p>GitLab CE/EE affecting all versions starting from 13.4 before 13.6.2存在信息泄露漏洞，该漏洞源于通过GraphQL公开信息会导致用户的电子邮件可见。目前没有详细的漏洞细节提供。</p>","recommandation":"<p>厂商尚未提供漏洞修复方案，请关注厂商主页更新：</p><p><a href=\"https://gitlab.com/gitlab-org/cves/-/blob/master/2020/CVE-2020-26413.json\" style=\"\">https://gitlab.com/gitlab-org/cves/-/blob/master/2020/CVE-2020-26413.json</a></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"信息泄露","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nTransfer-Encoding: chunked\r\nCache-Control: max-age=0, private, must-revalidate\r\nConnection: keep-alive\r\nContent-Type: application/json; charset=utf-8\r\nDate: Wed, 24 Nov 2021 07:15:53 GMT\r\nEtag: W/\"c5023d28ffc261abb8246a1ce682cc7d\"\r\nReferrer-Policy: strict-origin-when-cross-origin\r\nServer: nginx\r\nStrict-Transport-Security: max-age=31536000\r\nVary: Accept-Encoding\r\nVary: Origin\r\nX-Content-Type-Options: nosniff\r\nX-Download-Options: noopen\r\nX-Frame-Options: DENY\r\nX-Gitlab-Feature-Category: not_owned\r\nX-Permitted-Cross-Domain-Policies: none\r\nX-Request-Id: mHIP8hlLgi\r\nX-Runtime: 0.252448\r\nX-Ua-Compatible: IE=edge\r\nX-Xss-Protection: 1; mode=block\r\n\r\n{\"data\":{\"users\":{\"edges\":[{\"node\":{\"username\":\"zhaochenglong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gaojie\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"shiyalei\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/245/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"bilibin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"caiwanqiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"kangzejian\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhujiahao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lijialiang\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/240/avatar.png\",\"status\":{\"emoji\":\"soccer\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"wangyongqiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"majun\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xuehaichao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"qinjinlei\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liuqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xuhongzhou\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_615_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhangyanxu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhangyuwei\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"yanzeliang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangsen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wuyanbing\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"liuyuhan\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chenpf\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wumingyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"futianyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"caosiqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chentongcong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"fangzheng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_520_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"fuyuhang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"haochaoyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_545_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_581_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_548_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wufan\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wufang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"dengchengjie\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"huliankuo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"majingni\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"hewenchao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liangjing\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"douxianqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"licunyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangjianyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"xuzhiyi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lirui\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"sunjiaqi\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liusong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_519_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhayubo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wangpeibin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gongdaomeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"liuyifan\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/195/avatar.png\",\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"sleeeeeping\",\"messageHtml\":\"sleeeeeping\"}}},{\"node\":{\"username\":\"liuyifang\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"sleeping\",\"messageHtml\":\"sleeping\"}}},{\"node\":{\"username\":\"gaolumeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wengfangchen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wenhaijiang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"duanenjian\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"duy\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"anxianglang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"zhanfeiyang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lizhe\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"yuanmengting\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"qiaoshubo\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lizhuang\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"xihuichao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lin3er\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lijiandong\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"jinwenyan\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/179/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"shengguangsheng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/178/avatar.png\",\"status\":{\"emoji\":\"rosette\",\"message\":\"happy\",\"messageHtml\":\"happy\"}}},{\"node\":{\"username\":\"wangtianyang\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/177/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"rongjiale\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/176/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"chenhaiming\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"lanjinkai\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"likai\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"project_447_bot\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wushaohua\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/171/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"yangqianru\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/170/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"zhangzhen\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"maojunpeng\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"alert-bot\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/167/alert-bot.png\",\"status\":null}},{\"node\":{\"username\":\"chaidakun\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"guoguangxing\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"gaopeng2\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"support-bot\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/163/support-bot.png\",\"status\":null}},{\"node\":{\"username\":\"rentianyun\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"speech_balloon\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"shiyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"guanshanqiu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"chenyingao\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"dengxinke\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"baolei\",\"email\":\"\",\"avatarUrl\":null,\"status\":{\"emoji\":\"bicyclist\",\"message\":\"Making wheels...\",\"messageHtml\":\"Making wheels...\"}}},{\"node\":{\"username\":\"zhangqingcheng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/156/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"wangli\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/155/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"changxiaoyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"wanghaima\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"shizhenyu\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"limingcheng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/151/avatar.png\",\"status\":null}},{\"node\":{\"username\":\"libing\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}},{\"node\":{\"username\":\"limingze\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/149/avatar.png\",\"status\":{\"emoji\":\"rocket\",\"message\":\"\",\"messageHtml\":\"\"}}},{\"node\":{\"username\":\"lihaifeng\",\"email\":\"\",\"avatarUrl\":\"/uploads/-/system/user/avatar/148/avatar.png\",\"status\":{\"emoji\":\"bicyclist_tone1\",\"message\":\"Crazyyy coding..\",\"messageHtml\":\"Crazyyy coding..\"}}},{\"node\":{\"username\":\"wangchuanlin\",\"email\":\"\",\"avatarUrl\":null,\"status\":null}}]}}}","has_response":1,"createtime":"2021-11-24 15:15:09","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:09"}}
{"_index":"fofaee_threats","_type":"threats","_id":"461af251d70e2c6eae2e98eeeed9ac4f","_score":1,"_source":{"hosts":null,"ip":"************","port":8080,"name":"System Dashboard - BMH-Jira","common_title":"JIRA缺陷跟踪管理系统 META-INF目录信息泄露","mac":"00:0c:29:7c:d9:0c","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ATLASSIAN-JIRA","Oracle-JSP","Oracle-JAVA","OpenSSH","Oracle-MySQL","jQuery","Struts2"],"cat_tags":["其他企业应用","脚本语言","其他支撑系统","数据库系统","中间件","开发框架"],"company_tags":["Atlassian","Oracle Corporation","其他","The jQuery Foundation.","Apache Software Foundation."],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://************:8080","vulfile":"Jira_META_INF_CVE_2019_8442_UnauthorizedAccess.json","url":"http://************:8080/s/thiscanbeanythingyouwant/_/META-INF/maven/com.atlassian.jira/atlassian-jira-webapp/pom.xml","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"e9cc336a640dd78bc40fb0ae605d5be7","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.1 200 \r\nX-AREQUESTID: 861x4817x1\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Security-Policy: frame-ancestors 'self'\r\nX-ASEN: SEN-L17662553\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_4f9f5deecbf9690db021ef4b717b1b53d9101883_lout; Path=/\r\nX-AUSERNAME: anonymous\r\nSet-Cookie: JSESSIONID=7613BEFC2D4481ED80BFE4885A16A496; Path=/; HttpOnly\r\nX-Accel-Buffering: no\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:21:25 GMT\r\nConnection: close","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.34 \r\nJ\\x00\\x00\\x00\n5.7.34\\x00\\x0e\\xaf\\x03\\x00<x.w\u000b?\\x13\\x1e\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00#M\\x1aN(XD\\9KO:\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8090,"banner":"HTTP/1.1 503 \r\nSet-Cookie: JSESSIONID=167829123ECFC204D469471DB44CCD0F; Path=/; HttpOnly\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:42:04 GMT\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":10,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"Atlassian","ports":[8080],"title":"ATLASSIAN-JIRA"},{"belong_level":0,"rule_id":270,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JSP"},{"belong_level":0,"rule_id":22569,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JAVA"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8080,8090],"title":"jQuery"},{"belong_level":0,"rule_id":674,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Apache Software Foundation.","ports":[8080,8090],"title":"Struts2"}],"common_description":"Atlassian Jira是澳大利亚Atlassian公司的一套缺陷跟踪管理系统。该系统主要用于对工作中各类问题、缺陷进行跟踪管理。Atlassian Jira 7.13.4之前版本、8.0.4之前版本和8.1.1之前版本中的CachingResourceDownloadRewriteRule类存在安全漏洞。远程攻击者可利用该漏洞访问Jira webroot中的文件。","common_impact":"<p>Atlassian Jira是澳大利亚Atlassian公司的一套缺陷跟踪管理系统。该系统主要用于对工作中各类问题、缺陷进行跟踪管理。</p><p>Atlassian Jira 7.13.4之前版本、8.0.4之前版本和8.1.1之前版本存在信息泄露漏洞。该漏洞源于CachingResourceDownloadRewriteRule类允许远程攻击者访问META-INF目录下的Jira Webroot中的文件，</span>攻击者利用泄漏的敏感信息，获取网站服务器web路径，为进一步攻击提供帮助。</p>","recommandation":"<p>1、官方已修复该漏洞，请用户联系厂商升级固件至最新版本：<a href=\"https://jira.atlassian.com/browse/JRASERVER-69241\">https://jira.atlassian.com/browse/JRASERVER-69241</a></p><p>2、如非必要，禁止公网访问该系统。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2019-8442","vulType":"信息泄露","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 \r\nConnection: close\r\nContent-Length: 32156\r\nAccept-Ranges: bytes\r\nCache-Control: max-age=31536000\r\nCache-Control: public\r\nContent-Encoding: gzip\r\nContent-Security-Policy: frame-ancestors 'self'\r\nContent-Type: application/xml;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:44:55 GMT\r\nEtag: W/\"32156-1551856054000\"\r\nExpires: Thu, 24 Nov 2022 06:44:55 GMT\r\nLast-Modified: Wed, 06 Mar 2019 07:07:34 GMT\r\nVary: User-Agent\r\nX-Arequestid: 884x4821x1\r\nX-Asen: SEN-L17662553\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\n\r\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd\">\n  <modelVersion>4.0.0</modelVersion>\n  <parent>\n    <groupId>com.atlassian.jira</groupId>\n    <artifactId>jira-components</artifactId>\n    <version>8.0.2</version>\n  </parent>\n  <artifactId>atlassian-jira-webapp</artifactId>\n  <packaging>war</packaging>\n  <name>Atlassian JIRA - Webapp</name>\n  <dependencies>\n    <dependency>\n      <!-- this ensures that commons-logging doesn't make it into WEB-INF/lib even if transitively depended on -->\n      <groupId>commons-logging</groupId>\n      <artifactId>commons-logging</artifactId>\n      <scope>provided</scope>\n    </dependency>\n    <dependency>\n      <!-- SLF4j's implementation of the commons-logging API -->\n      <groupId>org.slf4j</groupId>\n      <artifactId>jcl-over-slf4j</artifactId>\n      <scope>runtime</scope>\n    </dependency>\n      <dependency>\n          <groupId>javax.servlet</groupId>\n          <artifactId>jsp-api</artifactId>\n          <version>2.0</version>\n          <scope>provided</scope>\n      </dependency>\n    <dependency>\n      <groupId>com.atlassian.jira</groupId>\n      <artifactId>jira-core</artifactId>\n      <version>${project.version}</version>\n    </dependency>\n    <dependency>\n      <groupId>com.atlassian.plugins</groupId>\n      <artifactId>atlassian-plugins-framework-bundles</artifactId>\n      <type>zip</type>\n      <classifier>${plugins.spring.classifier}</classifier>\n      <scope>provided</scope>\n    </dependency>\n    <dependency>\n        <groupId>org.codehaus.jackson</groupId>\n        <artifactId>jackson-core-asl</artifactId>\n        <scope>runtime</scope>\n    </dependency>\n  </dependencies>\n  <build>\n    <plugins>\n        <plugin>\n            <artifactId>maven-dependency-plugin</artifactId>\n            <version>2.10</version>\n            <executions>\n                <execution>\n                    <id>copy-spring-velocity-support-bundle</id>\n                    <phase>prepare-package</phase>\n                    <goals>\n                        <goal>copy</goal>\n                    </goals>\n                    <configuration>\n                        <artifactItems>\n                            <artifactItem>\n                                <groupId>com.atlassian.platform</groupId>\n                                <artifactId>spring-velocity-support</artifactId>\n                                <type>jar</type>\n                                <overWrite>true</overWrite>\n                                <version>0.0.6</version>\n                                <outputDirectory>${project.build.directory}/jira/WEB-INF/osgi-framework-bundles</outputDirectory>\n                                <destFileName>spring-velocity-support.jar</destFileName>\n                            </artifactItem>\n                        </artifactItems>\n                    </configuration>\n                </execution>\n            </executions>\n        </plugin>\n      <plugin>\n        <artifactId>maven-antrun-plugin</artifactId>\n        <executions>\n            <execution>\n                <id>ensure the output directory exists</id>\n                <!-- Because if we don't, Gulp and other tasks will fail -->\n                <phase>generate-resources</phase>\n                <goals>\n                    <goal>run</goal>\n                </goals>\n                <configuration>\n                    <tasks>\n                        <mkdir dir=\"${jira.webapp.directory}\" />\n                        <mkdir dir=\"${jira.compiled.ui.directory}\" />\n                    </tasks>\n                </configuration>\n            </execution>\n        </executions>\n      </plugin>\n      <plugin>\n        <groupId>org.apache.maven.plugins</groupId>\n        <artifactId>maven-war-plugin</artifactId>\n        <configuration>\n          <packagingExcludes>WEB-INF/lib/jira-core*.jar,WEB-INF/lib/*applet*</packagingExcludes>\n          <webResources>\n            <resource>\n              <filtering>true</filtering>\n              <directory>src/main/webapp/WEB-INF/classes</directory>\n              <includes>\n                <include>product.properties</include>\n              </includes>\n              <targetPath>WEB-INF/classes</targetPath>\n            </resource>\n            <!-- JSEV-1338: pushing in files that are compiled via gulp + frontend-maven-plugin pipeline,\n              because the maven-war-plugin likes to copy-paste the sources from src/main/webapp over the\n              top of the compiled files. So, we're going to copy over them *again*. (facepalm) -->\n            <resource>\n              <directory>target/${jira.compiled.ui.subdir}</directory>\n            </resource>\n          </webResources>\n          <overlays>\n            <overlay>\n              <groupId>com.atlassian.jira</groupId>\n              <artifactId>jira-core</artifactId>\n              <type>jar</type>\n              <targetPath>WEB-INF/classes</targetPath>\n              <excludes>\n                <exclude>com/atlassian/jira/screenshot/applet/**</exclude>\n                <exclude>**/package.html</exclude>\n              </excludes>\n            </overlay>\n            <overlay>\n              <groupId>com.atlassian.plugins</groupId>\n              <artifactId>atlassian-plugins-framework-bundles</artifactId>\n              <type>zip</type>\n              <classifier>${plugins.spring.classifier}</classifier>\n              <targetPath>WEB-INF/osgi-framework-bundles</targetPath>\n            </overlay>\n          </overlays>\n        </configuration>\n      </plugin>\n       <plugin>\n        <groupId>org.apache.maven.plugins</groupId>\n        <artifactId>maven-enforcer-plugin</artifactId>\n        <executions>\n          <execution>\n            <id>enforce-guava</id>\n            <goals>\n             <goal>enforce</goal>\n            </goals>\n            <phase>validate</phase>\n            <configuration>\n              <rules>\n                <bannedDependencies>\n                  <searchTransitive>true</searchTransitive>\n                  <message>Something is depending on google-collections (perhaps transitively), but we use guava</message>\n                    <excludes>\n                     <exclude>com.google.collections:google-collections</exclude>\n                    </excludes>\n                </bannedDependencies>\n              </rules>\n              <fail>true</fail>\n            </configuration>\n            </execution>\n            <execution>\n                <id>enforce-javamail</id>\n                <goals>\n                    <goal>enforce</goal>\n                </goals>\n                <phase>validate</phase>\n                <configuration>\n                    <rules>\n                        <bannedDependencies>\n                            <searchTransitive>true</searchTransitive>\n                            <message>Something is depending on javax.mail:mail (perhaps transitively), but we use javax.mail:javax.mail-api and com.sun.mail:javax.mail</message>\n                            <excludes>\n                                <exclude>javax.mail:mail</exclude>\n                            </excludes>\n                        </bannedDependencies>\n                    </rules>\n                    <fail>true</fail>\n                </configuration>\n            </execution>\n        </executions>\n        </plugin>\n        <plugin>\n            <groupId>com.github.eirslett</groupId>\n            <artifactId>frontend-maven-plugin</artifactId>\n            <configuration>\n              <installDirectory>${project.parent.parent.build.directory}</installDirectory>\n              <workingDirectory>${project.parent.parent.basedir}</workingDirectory>\n            </configuration>\n            <executions>\n                <execution>\n                    <id>install-dependencies-for-johnson</id>\n                    <goals>\n                        <goal>yarn</goal>\n                    </goals>\n                    <configuration>\n                        <arguments>${frontend.install.cmd}</arguments>\n                        <workingDirectory>${johnsonErrorPage.workingDirectory}</workingDirectory>\n                    </configuration>\n                    <phase>generate-resources</phase>\n                </execution>\n                <execution>\n                    <id>run-build-for-johnson</id>\n                    <goals>\n                        <goal>yarn</goal>\n                    </goals>\n                    <configuration>\n                        <arguments>build</arguments>\n                        <workingDirectory>${johnsonErrorPage.workingDirectory}</workingDirectory>\n                    </configuration>\n                    <phase>generate-resources</phase>\n                </execution>\n                <execution>\n                    <id>gulp process-resources</id>\n                    <goals>\n                        <goal>yarn</goal>\n                    </goals>\n                    <configuration>\n                        <arguments>run process-resources --module jira-webapp ${frontend.process.resources.args}</arguments>\n                    </configuration>\n                    <phase>process-resources</phase>\n                </execution>\n            </executions>\n        </plugin>\n        <plugin>\n            <artifactId>maven-clean-plugin</artifactId>\n            <configuration>\n                <filesets>\n      ","has_response":1,"createtime":"2021-11-24 15:15:29","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:29"}}
{"_index":"fofaee_threats","_type":"threats","_id":"715a4f6e8e19a8152136a913f08961d8","_score":1,"_source":{"hosts":null,"ip":"***********","port":6379,"name":"通信网络安全防护管理系统","common_title":"Redis未授权访问漏洞","mac":"00:0c:29:c0:09:23","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Oracle-MySQL","ECLIPSE-jetty","Oracle-JSP","Jenkins","Oracle-JAVA","redis","Linux-操作系统","Oracle-数据库","OpenSSH","Struts2"],"cat_tags":["服务","数据库系统","脚本语言","其他支撑系统","操作系统","开发框架"],"company_tags":["其他","Oracle Corporation","Eclipse Foundation, Inc.","Redis Labs","Apache Software Foundation."],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://***********:6379","vulfile":"redis_unauthorized_access.json","url":"***********:6379","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"5a947dad0a663343ba3e111bb42046ea","scan_engine":4,"port_list":[{"protocol":"http","port":8089,"banner":"HTTP/1.1 502 Bad Gateway\r\nServer: nginx/1.21.0\r\nDate: Wed, 24 Nov 2021 06:57:28 GMT\r\nContent-Type: text/html\r\nContent-Length: 494\r\nConnection: close\r\nETag: \"60acf71f-1ee\"","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.21.0\r\nDate: Wed, 24 Nov 2021 07:01:49 GMT\r\nContent-Type: text/html\r\nContent-Length: 12753\r\nLast-Modified: Mon, 11 Oct 2021 01:32:24 GMT\r\nConnection: close\r\nETag: \"61639428-31d1\"\r\nAccept-Ranges: bytes","certs":null},{"protocol":"oracle","port":1521,"banner":"Version:********.0\n\"\\x00\\x00Y(DESCRIPTION=(TMP=)(VSNNUM=186646784)(ERR=1189)(ERROR_STACK=(ERROR=(CODE=1189)(EMFI=4))))","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n$4164\r\n# Server\r\nredis_version:6.2.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:1822cf71ea870c30\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:4.8.5\r\nprocess_id:6699\r\nprocess_supervised:no\r\nrun_id:e2394e94d6323e9ef109b36a9604e827ceb4ef87\r\ntcp_port:6379\r\nserver_time_usec:1637737939907064\r\nuptime_in_seconds:504820\r\nuptime_in_days:5\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10347987\r\nexecutable:/usr/local/redis/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\nio_threads_active:0\r\n\r\n# Clients\r\nconnected_clients:1\r\ncluster_connections:0\r\nmaxclients:10000\r\nclient_recent_max_input_buffer:0\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\ntracking_clients:0\r\nclients_in_timeout_table:0\r\n\r\n# Memory\r\nused_memory:883120\r\nused_memory_human:862.42K\r\nused_memory_rss:10338304\r\nused_memory_rss_human:9.86M\r\nused_memory_peak:902400\r\nused_memory_peak_human:881.25K\r\nused_memory_peak_perc:97.86%\r\nused_memory_overhead:801080\r\nused_memory_startup:800800\r\nused_memory_dataset:82040\r\nused_memory_dataset_perc:99.66%\r\nallocator_allocated:893592\r\nallocator_active:1265664\r\nallocator_resident:3784704\r\ntotal_system_memory:16657199104\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nused_memory_scripts:0\r\nused_memory_scripts_human:0B\r\nnumber_of_cached_scripts:0\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nallocator_frag_ratio:1.42\r\nallocator_frag_bytes:372072\r\nallocator_rss_ratio:2.99\r\nallocator_rss_bytes:2519040\r\nrss_overhead_ratio:2.73\r\nrss_overhead_bytes:6553600\r\nmem_fragmentation_ratio:12.62\r\nmem_fragmentation_bytes:9518896\r\nmem_not_counted_for_evict:0\r\nmem_replication_backlog:0\r\nmem_clients_slaves:0\r\nmem_clients_normal:0\r\nmem_aof_buffer:0\r\nmem_allocator:jemalloc-5.1.0\r\nactive_defrag_running:0\r\nlazyfree_pending_objects:0\r\nlazyfreed_objects:0\r\n\r\n# Persistence\r\nloading:0\r\ncurrent_cow_size:0\r\ncurrent_cow_size_age:0\r\ncurrent_fork_perc:0.00\r\ncurrent_save_keys_processed:0\r\ncurrent_save_keys_total:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637233119\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:-1\r\nrdb_current_bgsave_time_sec:-1\r\nrdb_last_cow_size:0\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\naof_last_cow_size:0\r\nmodule_fork_in_progress:0\r\nmodule_fork_last_cow_size:0\r\n\r\n# Stats\r\ntotal_connections_received:57\r\ntotal_commands_processed:66\r\ninstantaneous_ops_per_sec:0\r\ntotal_net_input_bytes:1530\r\ntotal_net_output_bytes:210555\r\ninstantaneous_input_kbps:0.00\r\ninstantaneous_output_kbps:0.00\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:0\r\nexpired_stale_perc:0.00\r\nexpired_time_cap_reached_count:0\r\nexpire_cycle_cpu_milliseconds:5998\r\nevicted_keys:0\r\nkeyspace_hits:0\r\nkeyspace_misses:0\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:0\r\ntotal_forks:0\r\nmigrate_cached_sockets:0\r\nslave_expires_tracked_keys:0\r\nactive_defrag_hits:0\r\nactive_defrag_misses:0\r\nactive_defrag_key_hits:0\r\nactive_defrag_key_misses:0\r\ntracking_total_keys:0\r\ntracking_total_items:0\r\ntracking_total_prefixes:0\r\nunexpected_error_replies:0\r\ntotal_error_replies:41\r\ndump_payload_sanitizations:0\r\ntotal_reads_processed:157\r\ntotal_writes_processed:99\r\nio_threaded_reads_processed:0\r\nio_threaded_writes_processed:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_failover_state:no-failover\r\nmaster_replid:aaae93e861273db9094a40156d2fae519a2aa771\r\nmaster_replid2:****************************************\r\nmaster_repl_offset:0\r\nsecond_repl_offset:-1\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:380.659061\r\nused_cpu_user:468.212153\r\nused_cpu_sys_children:0.000000\r\nused_cpu_user_children:0.000000\r\nused_cpu_sys_main_thread:380.632857\r\nused_cpu_user_main_thread:468.198997\r\n\r\n# Modules\r\n\r\n# Errorstats\r\nerrorstat_ERR:count=41\r\n\r\n#","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.34 \r\nJ\\x00\\x00\\x00\n5.7.34\\x00A\\x00\\x00\\x00\\x05B\noD\rG\\x1f\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x12CI z_m'\\x0fz9^\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8888,"banner":"HTTP/1.1 403 Forbidden\r\nDate: Wed, 24 Nov 2021 07:13:22 GMT\r\nX-Content-Type-Options: nosniff\r\nSet-Cookie: JSESSIONID.6cc53390=node0cdqhoh9hf58ud0w9tm97gfsi278.node0; Path=/; HttpOnly\r\nExpires: Thu, 01 Jan 1970 00:00:00 GMT\r\nContent-Type: text/html;charset=utf-8\r\nX-Hudson: 1.395\r\nX-Jenkins: 2.303.1\r\nX-Jenkins-Session: b1b89399\r\nContent-Length: 541\r\nServer: Jetty(9.4.42.v20210604)","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,8089],"title":"NGINX"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":223,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"Eclipse Foundation, Inc.","ports":[8888],"title":"ECLIPSE-jetty"},{"belong_level":0,"rule_id":270,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8888],"title":"Oracle-JSP"},{"belong_level":0,"rule_id":431,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"其他","ports":[8888],"title":"Jenkins"},{"belong_level":0,"rule_id":22569,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8888],"title":"Oracle-JAVA"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":7216,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[1521],"title":"Oracle-数据库"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":674,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Apache Software Foundation.","ports":[8888],"title":"Struts2"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$4165\r\n# Server\r\nredis_version:6.2.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:1822cf71ea870c30\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:4.8.5\r\nprocess_id:6699\r\nprocess_supervised:no\r\nrun_id:e2394e94d6323e9ef109b36a9604e827ceb4ef87\r\ntcp_port:6379\r\nserver_time_usec:1637738301749807\r\nuptime_in_seconds:505182\r\nuptime_in_days:5\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10348349\r\nexecutable:/usr/local/redis/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\nio_threads_active:0\r\n\r\n# Clients\r\nconnected_clients:1\r\ncluster_connections:0\r\nmaxclients:10000\r\nclient_recent_max_input_buffer:0\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\ntracking_clients:0\r\nclients_in_timeout_table:0\r\n\r\n# Memory\r\nused_memory:883120\r\nused_memory_human:862.42K\r\nused_memory_rss:10338304\r\nused_memory_rss_human:9.86M\r\nused_memory_peak:902400\r\nused_memory_peak_human:881.25K\r\nused_memory_peak_perc:97.86%\r\nused_memory_overhead:801","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"e5e058f0cfd729f360c697a66695c4bb","_score":1,"_source":{"hosts":null,"ip":"***********","port":6379,"name":"通信网络安全防护管理系统","common_title":"Redis未授权访问漏洞","mac":"00:0c:29:0d:f2:a7","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["redis","Linux-操作系统","NGINX","elastic-Elasticsearch","Oracle-MySQL","OpenSSH","jQuery"],"cat_tags":["数据库系统","操作系统","服务","其他支撑系统","中间件"],"company_tags":["Redis Labs","其他","Elastic, Inc.","Oracle Corporation","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://***********:6379","vulfile":"redis_unauthorized_access.json","url":"***********:6379","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"44719d86a373ad01dc776301d123c7fe","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2245\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6363\r\nrun_id:f120b8c1236351870c37adcec0f5fc025502e359\r\ntcp_port:6379\r\nuptime_in_seconds:502521\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10346492\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:13\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:1213440\r\nused_memory_human:1.16M\r\nused_memory_rss:7249920\r\nused_memory_rss_human:6.91M\r\nused_memory_peak:1694112\r\nused_memory_peak_human:1.62M\r\ntotal_system_memory:16657199104\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:49152\r\nused_memory_lua_human:48.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:5.97\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:31\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637736292\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:20102\r\ntotal_commands_processed:7221311\r\ninstantaneous_ops_per_sec:8\r\ntotal_net_input_bytes:2360798619\r\ntotal_net_output_bytes:35346192\r\ninstantaneous_input_kbps:2.82\r\ninstantaneous_output_kbps:0.03\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:5607\r\nevicted_keys:0\r\nkeyspace_hits:22957\r\nkeyspace_misses:3032442\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:1022\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:559.14\r\nused_cpu_user:480.30\r\nused_cpu_sys_children:5.15\r\nused_cpu_user_children:0.89\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=2,expires=1,avg_ttl=9556088\r\ndb1:keys=2,expires=2,avg_ttl=42998\r\n\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.20.1\r\nDate: Wed, 24 Nov 2021 06:54:54 GMT\r\nContent-Type: text/html\r\nContent-Length: 7109\r\nLast-Modified: Tue, 16 Nov 2021 03:58:48 GMT\r\nConnection: close\r\nETag: \"61932c78-1bc5\"\r\nAccept-Ranges: bytes","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 531\r\n\r\n{\n  \"name\" : \"node-1\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"gWEeGfnyQuiDnr1iET6oiw\",\n  \"version\" : {\n    \"number\" : \"7.7.1\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"ad56dce891c901a492bb1ee393f12dfff473a423\",\n    \"build_date\" : \"2020-05-28T16:30:01.040088Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"8.5.1\",\n    \"minimum_wire_compatibility_version\" : \"6.8.0\",\n    \"minimum_index_compatibility_version\" : \"6.0.0-beta1\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.35 \r\nJ\\x00\\x00\\x00\n5.7.35\\x00\\xfe\\xbd\\x00\\x007\\x05:!e%f\\x18\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00L6Q\tdLSkh\\x04L)\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":7077,"banner":"HTTP/1.1 400 Bad Request\r\nContent-Type: text/plain; charset=utf-8\r\nConnection: close\r\n\r\n400 Bad Request","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[7077],"title":"jQuery"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$2246\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6363\r\nrun_id:f120b8c1236351870c37adcec0f5fc025502e359\r\ntcp_port:6379\r\nuptime_in_seconds:504028\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10347999\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:13\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:1066016\r\nused_memory_human:1.02M\r\nused_memory_rss:7254016\r\nused_memory_rss_human:6.92M\r\nused_memory_peak:1694112\r\nused_memory_peak_human:1.62M\r\ntotal_system_memory:16657199104\r\ntotal_system_memory_human:15.51G\r\nused_memory_lua:41984\r\nused_memory_lua_human:41.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:6.80\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_sa","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"90133fe76260ad3760a97212b20b71de","_score":1,"_source":{"hosts":null,"ip":"***********","port":6379,"name":"发现者一号后台登陆","common_title":"Redis未授权访问漏洞","mac":"00:0c:29:c0:85:d4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ubuntu-系统","OpenSSH","Oracle-MySQL","APACHE-ZooKeeper","NGINX","redis","Linux-操作系统","jQuery"],"cat_tags":["操作系统","其他支撑系统","数据库系统","大数据处理","服务","中间件"],"company_tags":["Canonical Ltd.","其他","Oracle Corporation","Apache Software Foundation.","Redis Labs","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://***********:6379","vulfile":"redis_unauthorized_access.json","url":"***********:6379","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"5d56ad4b5b74a582ca0168f91e16d739","scan_engine":4,"port_list":[{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1p1 Ubuntu-2ubuntu2.13\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 302 \r\nServer: nginx/1.4.6 (Ubuntu)\r\nDate: Wed, 24 Nov 2021 06:56:21 GMT\r\nContent-Length: 0\r\nConnection: close\r\nLocation: http://***********/login.html","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.23 \r\nJ\\x00\\x00\\x00\n5.7.23\\x00\\x07\\x02\\x00\\x00yKJWPXY|\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00<0[?\\x10+5X^qI\\x1d\\x00mysql_native_password\\x00","certs":null},{"protocol":"mdns","port":5353,"banner":"\\x00\\x00\\x84\\x00\\x00\\x01\\x00\\x01\\x00\\x00\\x00\\x00\t_services\\x07_dns-sd\\x04_udp\\x05local\\x00\\x00\f\\x00\\x01\\xc0\f\\x00\f\\x00\\x01\\x00\\x00\\x00\n\\x00\\x14\f_workstation\\x04_tcp\\xc0#","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 302 \r\nLocation: http://***********:8080/login.html\r\nContent-Length: 0\r\nDate: Wed, 24 Nov 2021 07:05:19 GMT\r\nConnection: close","certs":null},{"protocol":"epmd","port":4369,"banner":"name rabbit at port 40310\n","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$1710\r\n# Server\r\nredis_version:2.8.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:645b61b5aa39f6b1\r\nredis_mode:standalone\r\nos:Linux 4.4.0-148-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.4\r\nprocess_id:2375\r\nrun_id:7ca0770c0684114cc66ca73e21467c1e54a79131\r\ntcp_port:6379\r\nuptime_in_seconds:502799\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:195920\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:501208\r\nused_memory_human:489.46K\r\nused_memory_rss:2351104\r\nused_memory_peak:500280\r\nused_memory_peak_human:488.55K\r\nused_memory_lua:33792\r\nmem_fragmentation_ratio:4.69\r\nmem_allocator:jemalloc-3.5.1\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637234969\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:-1\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:62\r\ntotal_commands_processed:78\r\ninstantaneous_ops_per_sec:0\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:0\r\nevicted_keys:0\r\nkeyspace_hits:0\r\nkeyspace_misses:0\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:66.11\r\nused_cpu_user:87.64\r\nused_cpu_sys_children:0.00\r\nused_cpu_user_children:0.00\r\n\r\n# Keyspace\r\n\r\n","certs":null},{"protocol":"pptp","port":1723,"banner":"Firmware: 1\nHostname: local\nVendor: linux\n","certs":null},{"protocol":"zookeeper","port":2181,"banner":"Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:52730[0](queued=0,recved=1,sent=0)\n\nLatency min/avg/max: 0/0/0\nReceived: 59\nSent: 58\nConnections: 1\nOutstanding: 0\nZxid: 0x32\nMode: standalone\nNode count: 4\n","certs":null},{"protocol":"amqp","port":5672,"banner":"AMQP\\x00\\x00\t\\x01","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":4528,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Canonical Ltd.","ports":[22,80],"title":"ubuntu-系统"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":21157,"second_cat_tag":"大数据处理","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Apache Software Foundation.","ports":[2181],"title":"APACHE-ZooKeeper"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80],"title":"NGINX"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379,1723],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[80,8080],"title":"jQuery"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$1710\r\n# Server\r\nredis_version:2.8.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:645b61b5aa39f6b1\r\nredis_mode:standalone\r\nos:Linux 4.4.0-148-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.4\r\nprocess_id:2375\r\nrun_id:7ca0770c0684114cc66ca73e21467c1e54a79131\r\ntcp_port:6379\r\nuptime_in_seconds:503328\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:195973\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:501208\r\nused_memory_human:489.46K\r\nused_memory_rss:2351104\r\nused_memory_peak:500280\r\nused_memory_peak_human:488.55K\r\nused_memory_lua:33792\r\nmem_fragmentation_ratio:4.69\r\nmem_allocator:jemalloc-3.5.1\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637234969\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:-1\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"16170b0361925db795a5608a71d9fbaf","_score":1,"_source":{"hosts":null,"ip":"************","port":6379,"name":"elasticsearch-head","common_title":"Redis未授权访问漏洞","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["vmware-认证服务","Microsoft-Windows","redis","Linux-操作系统","elastic-Elasticsearch","Oracle-MySQL"],"cat_tags":["其他企业应用","操作系统","数据库系统"],"company_tags":["VMware, Inc.","Microsoft Corporation","Redis Labs","其他","Elastic, Inc.","Oracle Corporation"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:6379","vulfile":"redis_unauthorized_access.json","url":"************:6379","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"3971af922231a68ee90d5197116310a3","scan_engine":4,"port_list":[{"protocol":"unknown","port":9000,"banner":"","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.31 \r\nJ\\x00\\x00\\x00\n5.7.31\\x00\\x04\\x00\\x00\\x00\\x1c\\=\\x075C\tq\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00.\\x08\\x7f.+Vys1\\x19z\\x0e\\x00mysql_native_password\\x00","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n$3777\r\n# Server\r\nredis_version:6.0.6\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:19d4277f1e8a2fed\r\nredis_mode:standalone\r\nos:Linux 4.15.0-30deepin-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:8.3.0\r\nprocess_id:1\r\nrun_id:0bf2006cfd468a1fa38d81cdb6c460644b1753ed\r\ntcp_port:6379\r\nuptime_in_seconds:19234\r\nuptime_in_days:0\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10346718\r\nexecutable:/data/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:14\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:10\r\ntracking_clients:0\r\nclients_in_timeout_table:10\r\n\r\n# Memory\r\nused_memory:1152200\r\nused_memory_human:1.10M\r\nused_memory_rss:7774208\r\nused_memory_rss_human:7.41M\r\nused_memory_peak:1621016\r\nused_memory_peak_human:1.55M\r\nused_memory_peak_perc:71.08%\r\nused_memory_overhead:1031796\r\nused_memory_startup:803032\r\nused_memory_dataset:120404\r\nused_memory_dataset_perc:34.48%\r\nallocator_allocated:1191032\r\nallocator_active:1593344\r\nallocator_resident:4325376\r\ntotal_system_memory:16760500224\r\ntotal_system_memory_human:15.61G\r\nused_memory_lua:41984\r\nused_memory_lua_human:41.00K\r\nused_memory_scripts:3120\r\nused_memory_scripts_human:3.05K\r\nnumber_of_cached_scripts:2\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nallocator_frag_ratio:1.34\r\nallocator_frag_bytes:402312\r\nallocator_rss_ratio:2.71\r\nallocator_rss_bytes:2732032\r\nrss_overhead_ratio:1.80\r\nrss_overhead_bytes:3448832\r\nmem_fragmentation_ratio:7.14\r\nmem_fragmentation_bytes:6685504\r\nmem_not_counted_for_evict:3890\r\nmem_replication_backlog:0\r\nmem_clients_slaves:0\r\nmem_clients_normal:220818\r\nmem_aof_buffer:3890\r\nmem_allocator:jemalloc-5.1.0\r\nactive_defrag_running:0\r\nlazyfree_pending_objects:0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:1689\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637736546\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\nrdb_last_cow_size:540672\r\naof_enabled:1\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\naof_last_cow_size:0\r\nmodule_fork_in_progress:0\r\nmodule_fork_last_cow_size:0\r\naof_current_size:58838096\r\naof_base_size:40236837\r\naof_pending_rewrite:0\r\naof_buffer_length:0\r\naof_rewrite_buffer_length:0\r\naof_pending_bio_fsync:0\r\naof_delayed_fsync:0\r\n\r\n# Stats\r\ntotal_connections_received:14\r\ntotal_commands_processed:917666\r\ninstantaneous_ops_per_sec:54\r\ntotal_net_input_bytes:25129193\r\ntotal_net_output_bytes:4114871\r\ninstantaneous_input_kbps:2.17\r\ninstantaneous_output_kbps:0.31\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:2\r\nexpired_stale_perc:0.00\r\nexpired_time_cap_reached_count:0\r\nexpire_cycle_cpu_milliseconds:339\r\nevicted_keys:0\r\nkeyspace_hits:242731\r\nkeyspace_misses:352223\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:234\r\nmigrate_cached_sockets:0\r\nslave_expires_tracked_keys:0\r\nactive_defrag_hits:0\r\nactive_defrag_misses:0\r\nactive_defrag_key_hits:0\r\nactive_defrag_key_misses:0\r\ntracking_total_keys:0\r\ntracking_total_items:0\r\ntracking_total_prefixes:0\r\nunexpected_error_replies:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_replid:68591902d0e3c24a430f50225540c608835aa34b\r\nmaster_replid2:****************************************\r\nmaster_repl_offset:0\r\nsecond_repl_offset:-1\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:24.099503\r\nused_cpu_user:23.991491\r\nused_cpu_sys_children:0.031348\r\nused_cpu_user_children:0.048337\r\n\r\n# Modules\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb2:keys=15,expires=6,avg_ttl=105093373784\r\n\r\n","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 437\r\n\r\n{\n  \"name\" : \"y5E0ml4\",\n  \"cluster_name\" : \"elastic-cluster\",\n  \"cluster_uuid\" : \"6dty8mzbRc-20bgiEdxQSw\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":9100,"banner":"HTTP/1.1 400 Bad Request\r\n\r\n","certs":null},{"protocol":"netbios","port":137,"banner":"NetBIOS Response\nMAC:\nHostname:HAIMA-PC<0>\nHAIMA-PC<3>\nHAIMA-PC<20>\n\\x01\\x02__MSBROWSE__\\x02<1>\nWORKGROUP<0>\nWORKGROUP<1d>\nWORKGROUP<1e>\n","certs":null},{"protocol":"ikev2","port":500,"banner":"Initiator SPI: 8e7779464044673e\nResponder SPI: 402ba7d8f258a632\nNext Payload: 41\nVersion: 2.0\nExchange Type: 34\nMessage ID: 0\nLength: 36\n","certs":null},{"protocol":"smb","port":445,"banner":"Version: 6.1Build 0\nTarget Name : HAIMA-PC\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 403 Forbidden\r\nDate: Wed, 24 Nov 2021 07:00:44 GMT\r\nConnection: close\r\nContent-Security-Policy: block-all-mixed-content\r\nContent-Type: text/plain; charset=utf-8\r\nStrict-Transport-Security: max-age=31536000\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: DENY\r\nX-XSS-Protection: 1\r\nContent-Length: 0","certs":{"not_after":"2022-11-07 10:12:44","subject_cn":"VMware","issuer_cn":"VMware","not_before":"2021-11-07 10:12:44","v":"v3","sn":"14045361892086469562","sig_alth":"SHA256-RSA"}},{"protocol":"vmware_authentication_daemon","port":902,"banner":"220 VMware Authentication Daemon Version 1.10: SSL Required, ServerDaemonProtocol:SOAP, MKSDisplayProtocol:VNC , VMXARGS supported, NFCSSL supported/t\r\n","certs":null},{"protocol":"netbios-ssn","port":139,"banner":"Version: 6.1Build 0\nTarget Name : HAIMA-PC\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7206,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"VMware, Inc.","ports":[902],"title":"vmware-认证服务"},{"belong_level":0,"rule_id":7220,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Microsoft Corporation","ports":[445],"title":"Microsoft-Windows"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$3778\r\n# Server\r\nredis_version:6.0.6\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:19d4277f1e8a2fed\r\nredis_mode:standalone\r\nos:Linux 4.15.0-30deepin-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:8.3.0\r\nprocess_id:1\r\nrun_id:0bf2006cfd468a1fa38d81cdb6c460644b1753ed\r\ntcp_port:6379\r\nuptime_in_seconds:20865\r\nuptime_in_days:0\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10348349\r\nexecutable:/data/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:14\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:10\r\ntracking_clients:0\r\nclients_in_timeout_table:10\r\n\r\n# Memory\r\nused_memory:1152152\r\nused_memory_human:1.10M\r\nused_memory_rss:7876608\r\nused_memory_rss_human:7.51M\r\nused_memory_peak:1621016\r\nused_memory_peak_human:1.55M\r\nused_memory_peak_perc:71.08%\r\nused_memory_overhead:1031796\r\nused_memory_startup:803032\r\nused_memory_dataset:120356\r\nused_memory_dataset_perc:34.47%\r\nallocator_allocated:1247224\r\nallocat","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"69486dbad27203bb5353bc2577201804","_score":1,"_source":{"hosts":null,"ip":"************","port":27017,"name":"308 Permanent Redirect","common_title":"MongoDB未授权访问漏洞","mac":"00:0c:29:eb:b3:a8","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["MongoDB-数据库","NGINX","OpenSSH","elastic-Elasticsearch","redis","HARBOR"],"cat_tags":["数据库系统","服务","其他支撑系统","虚拟化"],"company_tags":["MongoDB, Inc","其他","Elastic, Inc.","Redis Labs"],"task_ids":[0,2],"state":1,"level":2,"hostinfo":"http://************:27017","vulfile":"mongodb_unauthorized_access.json","url":"http://************:27017","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"5c4b81eeaeb327bfbe4b715e0fa5087c","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n-NOAUTH Authentication required.\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:52:59 GMT\r\nContent-Type: text/html\r\nContent-Length: 856\r\nConnection: close\r\nLast-Modified: Wed, 16 Sep 2020 02:45:15 GMT\r\nETag: \"5f617c3b-358\"\r\nCache-Control: no-store, no-cache, must-revalidate\r\nAccept-Ranges: bytes\r\nStrict-Transport-Security: max-age=31536000; includeSubdomains; preload\r\nX-Frame-Options: DENY\r\nContent-Security-Policy: frame-ancestors 'none'","certs":{"not_after":"2021-10-21 11:00:23","subject_cn":"fofa.so","issuer_cn":"fofa.so","subject_org":["fofa.so"],"issuer_org":["fofa.so"],"not_before":"2020-10-21 11:00:23","v":"v3","sn":"12359437977138707854","sig_alth":"SHA256-RSA"}},{"protocol":"mongodb","port":27017,"banner":"{\n \"asserts\": {\n  \"msg\": 0,\n  \"regular\": 0,\n  \"rollovers\": 0,\n  \"user\": 0,\n  \"warning\": 0\n },\n \"connections\": {\n  \"active\": 1,\n  \"available\": 838858,\n  \"awaitingTopologyChanges\": 0,\n  \"current\": 2,\n  \"exhaustIsMaster\": 0,\n  \"totalCreated\": 88\n },\n \"electionMetrics\": {\n  \"averageCatchUpOps\": 0,\n  \"catchUpTakeover\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"electionTimeout\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"freezeTimeout\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"numCatchUps\": 0,\n  \"numCatchUpsAlreadyCaughtUp\": 0,\n  \"numCatchUpsFailedWithError\": 0,\n  \"numCatchUpsFailedWithNewTerm\": 0,\n  \"numCatchUpsFailedWithReplSetAbortPrimaryCatchUpCmd\": 0,\n  \"numCatchUpsSkipped\": 0,\n  \"numCatchUpsSucceeded\": 0,\n  \"numCatchUpsTimedOut\": 0,\n  \"numStepDownsCausedByHigherTerm\": 0,\n  \"priorityTakeover\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"stepUpCmd\": {\n   \"called\": 0,\n   \"successful\": 0\n  }\n },\n \"extra_info\": {\n  \"input_blocks\": 167920,\n  \"involuntary_context_switches\": 630,\n  \"maximum_resident_set_kb\": 114044,\n  \"note\": \"fields vary by platform\",\n  \"output_blocks\": 2848448,\n  \"page_faults\": 67,\n  \"page_reclaims\": 25583,\n  \"system_time_us\": 909666138,\n  \"user_time_us\": 886928887,\n  \"voluntary_context_switches\": 17682986\n },\n \"flowControl\": {\n  \"enabled\": true,\n  \"isLagged\": false,\n  \"isLaggedCount\": 0,\n  \"isLaggedTimeMicros\": 0,\n  \"locksPerKiloOp\": 0,\n  \"sustainerRate\": 0,\n  \"targetRateLimit\": 1********0,\n  \"timeAcquiringMicros\": 10909\n },\n \"freeMonitoring\": {\n  \"state\": \"undecided\"\n },\n \"globalLock\": {\n  \"activeClients\": {\n   \"readers\": 0,\n   \"total\": 0,\n   \"writers\": 0\n  },\n  \"currentQueue\": {\n   \"readers\": 0,\n   \"total\": 0,\n   \"writers\": 0\n  },\n  \"totalTime\": 502142381000\n },\n \"host\": \"a35ae3d41154\",\n \"localTime\": \"2021-11-24T14:58:43.974+08:00\",\n \"locks\": {\n  \"Collection\": {\n   \"acquireCount\": {\n    \"W\": 2,\n    \"r\": 519343,\n    \"w\": 8368\n   }\n  },\n  \"Database\": {\n   \"acquireCount\": {\n    \"W\": 4,\n    \"r\": 515907,\n    \"w\": 8368\n   }\n  },\n  \"Global\": {\n   \"acquireCount\": {\n    \"W\": 4,\n    \"r\": 1520236,\n    \"w\": 8372\n   }\n  },\n  \"Mutex\": {\n   \"acquireCount\": {\n    \"r\": 524278\n   }\n  },\n  \"ParallelBatchWriterMode\": {\n   \"acquireCount\": {\n    \"r\": 17232\n   }\n  },\n  \"ReplicationStateTransition\": {\n   \"acquireCount\": {\n    \"w\": 1528612\n   }\n  },\n  \"oplog\": {\n   \"acquireCount\": {\n    \"r\": 502119\n   }\n  }\n },\n \"logicalSessionRecordCache\": {\n  \"activeSessionsCount\": 0,\n  \"lastSessionsCollectionJobCursorsClosed\": 0,\n  \"lastSessionsCollectionJobDurationMillis\": 0,\n  \"lastSessionsCollectionJobEntriesEnded\": 0,\n  \"lastSessionsCollectionJobEntriesRefreshed\": 0,\n  \"lastSessionsCollectionJobTimestamp\": \"2021-11-24T14:54:44.668+08:00\",\n  \"lastTransactionReaperJobDurationMillis\": 0,\n  \"lastTransactionReaperJobEntriesCleanedUp\": 0,\n  \"lastTransactionReaperJobTimestamp\": \"2021-11-24T14:54:44.67+08:00\",\n  \"sessionCatalogSize\": 0,\n  \"sessionsCollectionJobCount\": 1674,\n  \"transactionReaperJobCount\": 1674\n },\n \"mem\": {\n  \"bits\": 64,\n  \"resident\": 110,\n  \"supported\": true,\n  \"virtual\": 1555\n },\n \"metrics\": {\n  \"aggStageCounters\": {\n   \"$_internalInhibitOptimization\": 0,\n   \"$_internalSplitPipeline\": 0,\n   \"$addFields\": 0,\n   \"$bucket\": 0,\n   \"$bucketAuto\": 0,\n   \"$changeStream\": 0,\n   \"$collStats\": 0,\n   \"$count\": 0,\n   \"$currentOp\": 0,\n   \"$facet\": 0,\n   \"$geoNear\": 0,\n   \"$graphLookup\": 0,\n   \"$group\": 0,\n   \"$indexStats\": 0,\n   \"$limit\": 0,\n   \"$listLocalSessions\": 0,\n   \"$listSessions\": 0,\n   \"$lookup\": 0,\n   \"$match\": 0,\n   \"$merge\": 0,\n   \"$mergeCursors\": 0,\n   \"$out\": 0,\n   \"$planCacheStats\": 0,\n   \"$project\": 0,\n   \"$redact\": 0,\n   \"$replaceRoot\": 0,\n   \"$replaceWith\": 0,\n   \"$sample\": 0,\n   \"$set\": 0,\n   \"$skip\": 0,\n   \"$sort\": 0,\n   \"$sortByCount\": 0,\n   \"$unionWith\": 0,\n   \"$unset\": 0,\n   \"$unwind\": 0\n  },\n  \"commands\": {\n   \"\\u003cUNKNOWN\\u003e\": 0,\n   \"_addShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_cloneCollectionOptionsFromPrimaryShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrAddShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrAddShardToZone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerCollectionStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStop\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrClearJumboFlag\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkMerge\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkMigration\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkSplit\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCreateCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCreateDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrDropCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrDropDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrEnableSharding\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrEnsureChunkVersionIsGreaterThan\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrMoveChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRefineCollectionShardKey\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRemoveShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRemoveShardFromZone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrShardCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrUpdateZoneKeyRange\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_flushDatabaseCacheUpdates\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_flushRoutingTableCacheUpdates\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_getNextSessionMods\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_getUserCacheGeneration\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_isSelf\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_killOperations\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_mergeAuthzCollections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_migrateClone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkAbort\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkCommit\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrCloneCatalogData\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrShardCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_transferMods\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"abortTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"aggregate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"appendOplogNote\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"applyOps\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"authenticate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"availableQueryOptions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"buildInfo\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"checkShardingIndex\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"cleanupOrphaned\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"cloneCollectionAsCapped\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"collMod\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"collStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"commitTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"compact\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connPoolStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connPoolSync\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connectionStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"convertToCapped\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"coordinateCommitTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"count\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"create\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createIndexes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"currentOp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dataSize\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dbHash\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dbStats\": {\n    \"failed\": 0,\n    \"total\": 104\n   },\n   \"delete\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"distinct\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"driverOIDTest\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"drop\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropAllRolesFromDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropAllUsersFromDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropConnections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropIndexes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"endSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"explain\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"features\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"filemd5\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"find\": {\n    \"failed\": 0,\n    \"total\": 1675\n   },\n   \"findAndModify\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"flushRouterConfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"fsync\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"fsyncUnlock\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"geoSearch\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getCmdLineOpts\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDatabaseVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDefaultRWConcern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDiagnosticData\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getFreeMonitoringStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getLastError\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getLog\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getMore\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getParameter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getShardMap\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getShardVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getnonce\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"grantPrivilegesToRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"grantRolesToRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"grantRolesToUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"hostInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"insert\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"internalRenameIfOptionsAndIndexesMatch\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"invalidateUserCache\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"isMaster\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"killAllSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killAllSessionsByPattern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killCursors\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killOp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listCollections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listCommands\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listDatabases\": {\n    \"failed\": 0,\n    \"total\": 71\n   },\n   \"listIndexes\": {\n    \"failed\": 0,\n    \"total\": 3348\n   },\n   \"lockInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"logRotate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"logout\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"mapReduce\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"mapreduce\": {\n    \"shardedfinish\": {\n     \"failed\": 0,\n     \"total\": 0\n    }\n   },\n   \"mergeChunks\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"moveChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"ping\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"planCacheClear\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheClearFilters\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheListFilters\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheSetFilter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"prepareTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"profile\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"reIndex\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"refreshSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"renameCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"repairDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetAbortPrimaryCatchUp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetFreeze\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetConfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetRBID\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetHeartbeat\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetInitiate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetMaintenance\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetReconfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetRequestVotes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetResizeOplog\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepDown\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepDownWithForce\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepUp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetSyncFrom\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetUpdatePosition\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"resetError\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokePrivilegesFromRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokeRolesFromRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokeRolesFromUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"rolesInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"saslContinue\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"saslStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"serverStatus\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"setDefaultRWConcern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setFeatureCompatibilityVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setFreeMonitoring\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setIndexCommitQuorum\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setParameter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setShardVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shardConnPoolStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shardingState\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shutdown\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"splitChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"splitVector\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"startRecordingTraffic\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"startSession\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"stopRecordingTraffic\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"top\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"unsetSharding\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"update\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"updateRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"updateUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"usersInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"validate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"voteCommitIndexBuild\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"waitForFailPoint\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"whatsmyuri\": {\n    \"failed\": 0,\n    \"total\": 0\n   }\n  },\n  \"cursor\": {\n   \"open\": {\n    \"noTimeout\": 0,\n    \"pinned\": 0,\n    \"total\": 0\n   },\n   \"timedOut\": 0\n  },\n  \"document\": {\n   \"deleted\": 0,\n   \"inserted\": 0,\n   \"returned\": 0,\n   \"updated\": 0\n  },\n  \"getLastError\": {\n   \"default\": {\n    \"unsatisfiable\": 0,\n    \"wtimeouts\": 0\n   },\n   \"wtime\": {\n    \"num\": 0,\n    \"totalMillis\": 0\n   },\n   \"wtimeouts\": 0\n  },\n  \"operation\": {\n   \"scanAndOrder\": 0,\n   \"writeConflicts\": 0\n  },\n  \"query\": {\n   \"planCacheTotalSizeEstimateBytes\": 0,\n   \"updateOneOpStyleBroadcastWithExactIDCount\": 0\n  },\n  \"queryExecutor\": {\n   \"collectionScans\": {\n    \"nonTailable\": 0,\n    \"total\": 0\n   },\n   \"scanned\": 0,\n   \"scannedObjects\": 0\n  },\n  \"record\": {\n   \"moves\": 0\n  },\n  \"repl\": {\n   \"apply\": {\n    \"attemptsToBecomeSecondary\": 0,\n    \"batchSize\": 0,\n    \"batches\": {\n     \"num\": 0,\n     \"totalMillis\": 0\n    },\n    \"ops\": 0\n   },\n   \"buffer\": {\n    \"count\": 0,\n    \"maxSizeBytes\": 0,\n    \"sizeBytes\": 0\n   },\n   \"executor\": {\n    \"networkInterface\": \"DEPRECATED: getDiagnosticString is deprecated in NetworkInterfaceTL\",\n    \"pool\": {\n     \"inProgressCount\": 0\n    },\n    \"queues\": {\n     \"networkInProgress\": 0,\n     \"sleepers\": 0\n    },\n    \"shuttingDown\": false,\n    \"unsignaledEvents\": 0\n   },\n   \"initialSync\": {\n    \"completed\": 0,\n    \"failedAttempts\": 0,\n    \"failures\": 0\n   },\n   \"network\": {\n    \"bytes\": 0,\n    \"getmores\": {\n     \"num\": 0,\n     \"numEmptyBatches\": 0,\n     \"totalMillis\": 0\n    },\n    \"notMasterLegacyUnacknowledgedWrites\": 0,\n    \"notMasterUnacknowledgedWrites\": 0,\n    \"oplogGetMoresProcessed\": {\n     \"num\": 0,\n     \"totalMillis\": 0\n    },\n    \"ops\": 0,\n    \"readersCreated\": 0,\n    \"replSetUpdatePosition\": {\n     \"num\": 0\n    }\n   },\n   \"stateTransition\": {\n    \"lastStateTransition\": \"\",\n    \"userOperationsKilled\": 0,\n    \"userOperationsRunning\": 0\n   },\n   \"syncSource\": {\n    \"numSelections\": 0,\n    \"numTimesChoseDifferent\": 0,\n    \"numTimesChoseSame\": 0,\n    \"numTimesCouldNotFind\": 0\n   }\n  },\n  \"ttl\": {\n   \"deletedDocuments\": 0,\n   \"passes\": 8368\n  }\n },\n \"network\": {\n  \"bytesIn\": 18312,\n  \"bytesOut\": 1203604,\n  \"compression\": {\n   \"snappy\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   },\n   \"zlib\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   },\n   \"zstd\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   }\n  },\n  \"numRequests\": 310,\n  \"numSlowDNSOperations\": 0,\n  \"numSlowSSLOperations\": 0,\n  \"physicalBytesIn\": 18312,\n  \"physicalBytesOut\": 1203604,\n  \"serviceExecutorTaskStats\": {\n   \"executor\": \"passthrough\",\n   \"threadsRunning\": 2\n  },\n  \"tcpFastOpen\": {\n   \"accepted\": 0,\n   \"clientSupported\": false,\n   \"kernelSetting\": 0,\n   \"serverSupported\": true\n  }\n },\n \"ok\": 1,\n \"opLatencies\": {\n  \"commands\": {\n   \"latency\": 165783,\n   \"ops\": 309\n  },\n  \"reads\": {\n   \"latency\": 0,\n   \"ops\": 0\n  },\n  \"transactions\": {\n   \"latency\": 0,\n   \"ops\": 0\n  },\n  \"writes\": {\n   \"latency\": 0,\n   \"ops\": 0\n  }\n },\n \"opReadConcernCounters\": {\n  \"available\": 0,\n  \"linearizable\": 0,\n  \"local\": 0,\n  \"majority\": 0,\n  \"none\": 1675,\n  \"snapshot\": 0\n },\n \"opcounters\": {\n  \"command\": 3658,\n  \"delete\": 0,\n  \"getmore\": 0,\n  \"insert\": 0,\n  \"query\": 1675,\n  \"update\": 0\n },\n \"opcountersRepl\": {\n  \"command\": 0,\n  \"delete\": 0,\n  \"getmore\": 0,\n  \"insert\": 0,\n  \"query\": 0,\n  \"update\": 0\n },\n \"pid\": 1,\n \"process\": \"mongod\",\n \"security\": {\n  \"authentication\": {\n   \"mechanisms\": {\n    \"MONGODB-X509\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    },\n    \"SCRAM-SHA-1\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    },\n    \"SCRAM-SHA-256\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    }\n   }\n  }\n },\n \"storageEngine\": {\n  \"backupCursorOpen\": false,\n  \"dropPendingIdents\": 0,\n  \"name\": \"wiredTiger\",\n  \"oldestRequiredTimestampForCrashRecovery\": 0,\n  \"persistent\": true,\n  \"readOnly\": false,\n  \"supportsCommittedReads\": true,\n  \"supportsPendingDrops\": true,\n  \"supportsSnapshotReadConcern\": true,\n  \"supportsTwoPhaseIndexBuild\": true\n },\n \"tcmalloc\": {\n  \"generic\": {\n   \"current_allocated_bytes\": 82341944,\n   \"heap_size\": 95567872\n  },\n  \"tcmalloc\": {\n   \"aggressive_memory_decommit\": 0,\n   \"central_cache_free_bytes\": 281984,\n   \"current_total_thread_cache_bytes\": 1031240,\n   \"formattedString\": \"------------------------------------------------\\nMALLOC:       82342520 (   78.5 MiB) Bytes in use by application\\nMALLOC: +      4366336 (    4.2 MiB) Bytes in page heap freelist\\nMALLOC: +       281984 (    0.3 MiB) Bytes in central cache freelist\\nMALLOC: +       378368 (    0.4 MiB) Bytes in transfer cache freelist\\nMALLOC: +      1030664 (    1.0 MiB) Bytes in thread cache freelists\\nMALLOC: +      2752512 (    2.6 MiB) Bytes in malloc metadata\\nMALLOC:   ------------\\nMALLOC: =     91152384 (   86.9 MiB) Actual memory used (physical + swap)\\nMALLOC: +      7168000 (    6.8 MiB) Bytes released to OS (aka unmapped)\\nMALLOC:   ------------\\nMALLOC: =     98320384 (   93.8 MiB) Virtual address space used\\nMALLOC:\\nMALLOC:            770              Spans in use\\nMALLOC:             32              Thread heaps in use\\nMALLOC:           4096              Tcmalloc page size\\n------------------------------------------------\\nCall ReleaseFreeMemory() to release freelist memory to the OS (via madvise()).\\nBytes released to the OS take up virtual address space but no physical memory.\\n\",\n   \"max_total_thread_cache_bytes\": 980418560,\n   \"pageheap_commit_count\": 1130,\n   \"pageheap_committed_bytes\": 88399872,\n   \"pageheap_decommit_count\": 566,\n   \"pageheap_free_bytes\": 4366336,\n   \"pageheap_reserve_count\": 57,\n   \"pageheap_scavenge_count\": 566,\n   \"pageheap_total_commit_bytes\": 381353984,\n   \"pageheap_total_decommit_bytes\": 292954112,\n   \"pageheap_total_reserve_bytes\": 95567872,\n   \"pageheap_unmapped_bytes\": 7168000,\n   \"release_rate\": 1,\n   \"spinlock_total_delay_ns\": 22796,\n   \"thread_cache_free_bytes\": 1031240,\n   \"total_free_bytes\": 1691592,\n   \"transfer_cache_free_bytes\": 378368\n  }\n },\n \"trafficRecording\": {\n  \"running\": false\n },\n \"transactions\": {\n  \"currentActive\": 0,\n  \"currentInactive\": 0,\n  \"currentOpen\": 0,\n  \"currentPrepared\": 0,\n  \"retriedCommandsCount\": 0,\n  \"retriedStatementsCount\": 0,\n  \"totalAborted\": 0,\n  \"totalCommitted\": 0,\n  \"totalPrepared\": 0,\n  \"totalPreparedThenAborted\": 0,\n  \"totalPreparedThenCommitted\": 0,\n  \"totalStarted\": 0,\n  \"transactionsCollectionWriteCount\": 0\n },\n \"transportSecurity\": {\n  \"1.0\": 0,\n  \"1.1\": 0,\n  \"1.2\": 0,\n  \"1.3\": 0,\n  \"unknown\": 0\n },\n \"twoPhaseCommitCoordinator\": {\n  \"currentInSteps\": {\n   \"deletingCoordinatorDoc\": 0,\n   \"waitingForDecisionAcks\": 0,\n   \"waitingForVotes\": 0,\n   \"writingDecision\": 0,\n   \"writingParticipantList\": 0\n  },\n  \"totalAbortedTwoPhaseCommit\": 0,\n  \"totalCommittedTwoPhaseCommit\": 0,\n  \"totalCreated\": 0,\n  \"totalStartedTwoPhaseCommit\": 0\n },\n \"uptime\": 502142,\n \"uptimeEstimate\": 502142,\n \"uptimeMillis\": 502142433,\n \"version\": \"4.4.1\",\n \"wiredTiger\": {\n  \"async\": {\n   \"current work queue length\": 0,\n   \"maximum work queue length\": 0,\n   \"number of allocation state races\": 0,\n   \"number of flush calls\": 0,\n   \"number of operation slots viewed for allocation\": 0,\n   \"number of times operation allocation failed\": 0,\n   \"number of times worker found no work\": 0,\n   \"total allocations\": 0,\n   \"total compact calls\": 0,\n   \"total insert calls\": 0,\n   \"total remove calls\": 0,\n   \"total search calls\": 0,\n   \"total update calls\": 0\n  },\n  \"block-manager\": {\n   \"blocks pre-loaded\": 10,\n   \"blocks read\": 8371,\n   \"blocks written\": 41728,\n   \"bytes read\": 34390016,\n   \"bytes read via memory map API\": 0,\n   \"bytes read via system call API\": 0,\n   \"bytes written\": 376033280,\n   \"bytes written for checkpoint\": 376033280,\n   \"bytes written via memory map API\": 0,\n   \"bytes written via system call API\": 0,\n   \"mapped blocks read\": 0,\n   \"mapped bytes read\": 0,\n   \"number of times the file was remapped because it changed size via fallocate or truncate\": 0,\n   \"number of times the region was remapped via write\": 0\n  },\n  \"cache\": {\n   \"application threads page read from disk to cache count\": 6,\n   \"application threads page read from disk to cache time (usecs)\": 8567,\n   \"application threads page write from cache to disk count\": 16692,\n   \"application threads page write from cache to disk time (usecs)\": 855489,\n   \"bytes allocated for updates\": 14936,\n   \"bytes belonging to page images in the cache\": 168647,\n   \"bytes belonging to the history store table in the cache\": 173,\n   \"bytes currently in the cache\": 190350,\n   \"bytes dirty in the cache cumulative\": 309133181,\n   \"bytes not belonging to page images in the cache\": 21702,\n   \"bytes read into cache\": 156155,\n   \"bytes written from cache\": 236052223,\n   \"cache overflow score\": 0,\n   \"checkpoint blocked page eviction\": 0,\n   \"eviction calls to get a page\": 43870,\n   \"eviction calls to get a page found queue empty\": 34914,\n   \"eviction calls to get a page found queue empty after locking\": 390,\n   \"eviction currently operating in aggressive mode\": 0,\n   \"eviction empty score\": 0,\n   \"eviction passes of a file\": 0,\n   \"eviction server candidate queue empty when topping up\": 0,\n   \"eviction server candidate queue not empty when topping up\": 0,\n   \"eviction server evicting pages\": 0,\n   \"eviction server slept, because we did not make progress with eviction\": 7984,\n   \"eviction server unable to reach eviction goal\": 0,\n   \"eviction server waiting for a leaf page\": 0,\n   \"eviction state\": 64,\n   \"eviction walk target pages histogram - 0-9\": 0,\n   \"eviction walk target pages histogram - 10-31\": 0,\n   \"eviction walk target pages histogram - 128 and higher\": 0,\n   \"eviction walk target pages histogram - 32-63\": 0,\n   \"eviction walk target pages histogram - 64-128\": 0,\n   \"eviction walk target strategy both clean and dirty pages\": 0,\n   \"eviction walk target strategy only clean pages\": 0,\n   \"eviction walk target strategy only dirty pages\": 0,\n   \"eviction walks abandoned\": 0,\n   \"eviction walks gave up because they restarted their walk twice\": 0,\n   \"eviction walks gave up because they saw too many pages and found no candidates\": 0,\n   \"eviction walks gave up because they saw too many pages and found too few candidates\": 0,\n   \"eviction walks reached end of tree\": 0,\n   \"eviction walks started from root of tree\": 0,\n   \"eviction walks started from saved location in tree\": 0,\n   \"eviction worker thread active\": 4,\n   \"eviction worker thread created\": 0,\n   \"eviction worker thread evicting pages\": 8341,\n   \"eviction worker thread removed\": 0,\n   \"eviction worker thread stable number\": 0,\n   \"files with active eviction walks\": 0,\n   \"files with new eviction walks started\": 0,\n   \"force re-tuning of eviction workers once in a while\": 0,\n   \"forced eviction - history store pages failed to evict while session has history store cursor open\": 0,\n   \"forced eviction - history store pages selected while session has history store cursor open\": 0,\n   \"forced eviction - history store pages successfully evicted while session has history store cursor open\": 0,\n   \"forced eviction - pages evicted that were clean count\": 0,\n   \"forced eviction - pages evicted that were clean time (usecs)\": 0,\n   \"forced eviction - pages evicted that were dirty count\": 0,\n   \"forced eviction - pages evicted that were dirty time (usecs)\": 0,\n   \"forced eviction - pages selected because of too many deleted items count\": 0,\n   \"forced eviction - pages selected count\": 0,\n   \"forced eviction - pages selected unable to be evicted count\": 0,\n   \"forced eviction - pages selected unable to be evicted time\": 0,\n   \"forced eviction - session returned rollback error while force evicting due to being oldest\": 0,\n   \"hazard pointer blocked page eviction\": 0,\n   \"hazard pointer check calls\": 8341,\n   \"hazard pointer check entries walked\": 14,\n   \"hazard pointer maximum array length\": 1,\n   \"history store score\": 0,\n   \"history store table insert calls\": 0,\n   \"history store table insert calls that returned restart\": 0,\n   \"history store table max on-disk size\": 0,\n   \"history store table on-disk size\": 0,\n   \"history store table out-of-order resolved updates that lose their durable timestamp\": 0,\n   \"history store table out-of-order updates that were fixed up by moving existing records\": 0,\n   \"history store table out-of-order updates that were fixed up during insertion\": 0,\n   \"history store table reads\": 0,\n   \"history store table reads missed\": 0,\n   \"history store table reads requiring squashed modifies\": 0,\n   \"history store table truncation by rollback to stable to remove an unstable update\": 0,\n   \"history store table truncation by rollback to stable to remove an update\": 0,\n   \"history store table truncation due to mixed timestamps that returned restart\": 0,\n   \"history store table truncation to remove an update\": 0,\n   \"history store table truncation to remove range of updates due to key being removed from the data page during reconciliation\": 0,\n   \"history store table truncation to remove range of updates due to mixed timestamps\": 0,\n   \"history store table writes requiring squashed modifies\": 0,\n   \"in-memory page passed criteria to be split\": 0,\n   \"in-memory page splits\": 0,\n   \"internal pages evicted\": 0,\n   \"internal pages queued for eviction\": 0,\n   \"internal pages seen by eviction walk\": 0,\n   \"internal pages seen by eviction walk that are already queued\": 0,\n   \"internal pages split during eviction\": 0,\n   \"leaf pages split during eviction\": 0,\n   \"maximum bytes configured\": 3386900480,\n   \"maximum page size at eviction\": 352,\n   \"modified pages evicted\": 8341,\n   \"modified pages evicted by application threads\": 0,\n   \"operations timed out waiting for space in cache\": 0,\n   \"overflow pages read into cache\": 0,\n   \"page split during eviction deepened the tree\": 0,\n   \"page written requiring history store records\": 0,\n   \"pages currently held in the cache\": 19,\n   \"pages evicted by application threads\": 0,\n   \"pages queued for eviction\": 0,\n   \"pages queued for eviction post lru sorting\": 0,\n ","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 542\r\n\r\n{\n  \"name\" : \"24abfaf805c9\",\n  \"cluster_name\" : \"docker-cluster\",\n  \"cluster_uuid\" : \"WXp-kL7TTJuzp4gXkiBIMA\",\n  \"version\" : {\n    \"number\" : \"7.10.1\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"docker\",\n    \"build_hash\" : \"1c34507e66d7db1211f66f3513706fdf548736aa\",\n    \"build_date\" : \"2020-12-05T01:00:33.671820Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"8.7.0\",\n    \"minimum_wire_compatibility_version\" : \"6.8.0\",\n    \"minimum_index_compatibility_version\" : \"6.0.0-beta1\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 308 Permanent Redirect\r\nServer: nginx/1.16.1\r\nDate: Wed, 24 Nov 2021 07:03:36 GMT\r\nContent-Type: text/html\r\nContent-Length: 171\r\nConnection: close\r\nLocation: https://************:443/","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":5,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"MongoDB, Inc","ports":[27017],"title":"MongoDB-数据库"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":8111,"second_cat_tag":"虚拟化","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"其他","ports":[443],"title":"HARBOR"}],"common_description":"MongoDB 是一个基于分布式文件存储的数据库。开启MongoDB服务时不添加任何参数，默认是没有权限验证的，攻击者可获取Mongodb数据库中的数据，导致敏感数据的泄露。","common_impact":"<p>攻击者可以通过默认端口，无需密码就可以对数据库进行任意操作，而且可以远程访问数据库，导致数据库信息泄露。</p>","recommandation":"<p>1、创建管理员账户；</p><p>mongodb没有默认管理员账号，所以要先添加管理员账号，在开启权限认证。<br></p><p>（1）添加管理员账号</p><p><code>[root@localhost]#&nbsp;mongo</code></p><p><code>MongoDB shell version: 2.4.6</code></p><p><code>connecting to: tank</code></p><p><code>&gt; use admin&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; //切换到admin数据库</code></p><p><code>switched to db admin</code></p><p><code>&gt; show collections;</code></p><p><code>system.indexes</code></p><p><code>system.users&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;//用户表</code></p><p><code>&gt; db.system.users.find();&nbsp; &nbsp; &nbsp; //用户表没有数据</code></p><p><code>&gt; db.addUser('tank','test');&nbsp; &nbsp;//添加一个管理员账号</code></p><p><code>{</code></p><p><code>&nbsp; &nbsp; \"user\" : \"tank\",</code></p><p><code>&nbsp; &nbsp; \"readOnly\" : false,</code></p><p><code>&nbsp; &nbsp; \"pwd\" : \"988432606980d0695e4f668f6bbc643a\",</code></p><p><code>&nbsp; &nbsp; \"_id\" : ObjectId(\"529e5d543b6a4608ac833429\")</code></p><p><code>}</code></p><p></p><p></p><p>（2）开启动用户权限认证</p><p><code>[root@localhost&nbsp;zhangy]#&nbsp;vim&nbsp;/etc/mongodb.conf&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;//将auth=true前面的注释拿掉[root@localhost&nbsp;zhangy]#&nbsp;/etc/init.d/mongod&nbsp;restart&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//重启生效</code></p><p>2、如非必要，禁止外网访问该数据库。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"","has_response":0,"createtime":"2021-11-24 15:17:27","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:27"}}
{"_index":"fofaee_threats","_type":"threats","_id":"9f56d10ce7cef72ac96bbec4eccfb536","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"elasticsearch-head","common_title":"Elasticsearch未授权访问","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["vmware-认证服务","Microsoft-Windows","redis","Linux-操作系统","elastic-Elasticsearch","Oracle-MySQL"],"cat_tags":["其他企业应用","操作系统","数据库系统"],"company_tags":["VMware, Inc.","Microsoft Corporation","Redis Labs","其他","Elastic, Inc.","Oracle Corporation"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"f4a978591d8783c978beafa1c425f9e7","scan_engine":4,"port_list":[{"protocol":"unknown","port":9000,"banner":"","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.31 \r\nJ\\x00\\x00\\x00\n5.7.31\\x00\\x04\\x00\\x00\\x00\\x1c\\=\\x075C\tq\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00.\\x08\\x7f.+Vys1\\x19z\\x0e\\x00mysql_native_password\\x00","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n$3777\r\n# Server\r\nredis_version:6.0.6\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:19d4277f1e8a2fed\r\nredis_mode:standalone\r\nos:Linux 4.15.0-30deepin-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:8.3.0\r\nprocess_id:1\r\nrun_id:0bf2006cfd468a1fa38d81cdb6c460644b1753ed\r\ntcp_port:6379\r\nuptime_in_seconds:19234\r\nuptime_in_days:0\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10346718\r\nexecutable:/data/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:14\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:10\r\ntracking_clients:0\r\nclients_in_timeout_table:10\r\n\r\n# Memory\r\nused_memory:1152200\r\nused_memory_human:1.10M\r\nused_memory_rss:7774208\r\nused_memory_rss_human:7.41M\r\nused_memory_peak:1621016\r\nused_memory_peak_human:1.55M\r\nused_memory_peak_perc:71.08%\r\nused_memory_overhead:1031796\r\nused_memory_startup:803032\r\nused_memory_dataset:120404\r\nused_memory_dataset_perc:34.48%\r\nallocator_allocated:1191032\r\nallocator_active:1593344\r\nallocator_resident:4325376\r\ntotal_system_memory:16760500224\r\ntotal_system_memory_human:15.61G\r\nused_memory_lua:41984\r\nused_memory_lua_human:41.00K\r\nused_memory_scripts:3120\r\nused_memory_scripts_human:3.05K\r\nnumber_of_cached_scripts:2\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nallocator_frag_ratio:1.34\r\nallocator_frag_bytes:402312\r\nallocator_rss_ratio:2.71\r\nallocator_rss_bytes:2732032\r\nrss_overhead_ratio:1.80\r\nrss_overhead_bytes:3448832\r\nmem_fragmentation_ratio:7.14\r\nmem_fragmentation_bytes:6685504\r\nmem_not_counted_for_evict:3890\r\nmem_replication_backlog:0\r\nmem_clients_slaves:0\r\nmem_clients_normal:220818\r\nmem_aof_buffer:3890\r\nmem_allocator:jemalloc-5.1.0\r\nactive_defrag_running:0\r\nlazyfree_pending_objects:0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:1689\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637736546\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\nrdb_last_cow_size:540672\r\naof_enabled:1\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\naof_last_cow_size:0\r\nmodule_fork_in_progress:0\r\nmodule_fork_last_cow_size:0\r\naof_current_size:58838096\r\naof_base_size:40236837\r\naof_pending_rewrite:0\r\naof_buffer_length:0\r\naof_rewrite_buffer_length:0\r\naof_pending_bio_fsync:0\r\naof_delayed_fsync:0\r\n\r\n# Stats\r\ntotal_connections_received:14\r\ntotal_commands_processed:917666\r\ninstantaneous_ops_per_sec:54\r\ntotal_net_input_bytes:25129193\r\ntotal_net_output_bytes:4114871\r\ninstantaneous_input_kbps:2.17\r\ninstantaneous_output_kbps:0.31\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:2\r\nexpired_stale_perc:0.00\r\nexpired_time_cap_reached_count:0\r\nexpire_cycle_cpu_milliseconds:339\r\nevicted_keys:0\r\nkeyspace_hits:242731\r\nkeyspace_misses:352223\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:234\r\nmigrate_cached_sockets:0\r\nslave_expires_tracked_keys:0\r\nactive_defrag_hits:0\r\nactive_defrag_misses:0\r\nactive_defrag_key_hits:0\r\nactive_defrag_key_misses:0\r\ntracking_total_keys:0\r\ntracking_total_items:0\r\ntracking_total_prefixes:0\r\nunexpected_error_replies:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_replid:68591902d0e3c24a430f50225540c608835aa34b\r\nmaster_replid2:****************************************\r\nmaster_repl_offset:0\r\nsecond_repl_offset:-1\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:24.099503\r\nused_cpu_user:23.991491\r\nused_cpu_sys_children:0.031348\r\nused_cpu_user_children:0.048337\r\n\r\n# Modules\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb2:keys=15,expires=6,avg_ttl=105093373784\r\n\r\n","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 437\r\n\r\n{\n  \"name\" : \"y5E0ml4\",\n  \"cluster_name\" : \"elastic-cluster\",\n  \"cluster_uuid\" : \"6dty8mzbRc-20bgiEdxQSw\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":9100,"banner":"HTTP/1.1 400 Bad Request\r\n\r\n","certs":null},{"protocol":"netbios","port":137,"banner":"NetBIOS Response\nMAC:\nHostname:HAIMA-PC<0>\nHAIMA-PC<3>\nHAIMA-PC<20>\n\\x01\\x02__MSBROWSE__\\x02<1>\nWORKGROUP<0>\nWORKGROUP<1d>\nWORKGROUP<1e>\n","certs":null},{"protocol":"ikev2","port":500,"banner":"Initiator SPI: 8e7779464044673e\nResponder SPI: 402ba7d8f258a632\nNext Payload: 41\nVersion: 2.0\nExchange Type: 34\nMessage ID: 0\nLength: 36\n","certs":null},{"protocol":"smb","port":445,"banner":"Version: 6.1Build 0\nTarget Name : HAIMA-PC\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 403 Forbidden\r\nDate: Wed, 24 Nov 2021 07:00:44 GMT\r\nConnection: close\r\nContent-Security-Policy: block-all-mixed-content\r\nContent-Type: text/plain; charset=utf-8\r\nStrict-Transport-Security: max-age=31536000\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: DENY\r\nX-XSS-Protection: 1\r\nContent-Length: 0","certs":{"not_after":"2022-11-07 10:12:44","subject_cn":"VMware","issuer_cn":"VMware","not_before":"2021-11-07 10:12:44","v":"v3","sn":"14045361892086469562","sig_alth":"SHA256-RSA"}},{"protocol":"vmware_authentication_daemon","port":902,"banner":"220 VMware Authentication Daemon Version 1.10: SSL Required, ServerDaemonProtocol:SOAP, MKSDisplayProtocol:VNC , VMXARGS supported, NFCSSL supported/t\r\n","certs":null},{"protocol":"netbios-ssn","port":139,"banner":"Version: 6.1Build 0\nTarget Name : HAIMA-PC\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7206,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"VMware, Inc.","ports":[902],"title":"vmware-认证服务"},{"belong_level":0,"rule_id":7220,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Microsoft Corporation","ports":[445],"title":"Microsoft-Windows"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"7eff1fc2e3ff3df384e61a16f0b17530","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"网络资产测绘及风险分析系统","common_title":"Elasticsearch未授权访问","mac":"9c:69:b4:60:45:4a","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","elastic-Elasticsearch","OpenSSH","vmware-Spring-Framework","jQuery","Ruby","Layui","华顺信安-FOEYE"],"cat_tags":["服务","数据库系统","其他支撑系统","开发框架","中间件","脚本语言","组件","威胁分析与管理"],"company_tags":["其他","Elastic, Inc.","Pivotal Software, Inc.","The jQuery Foundation.","北京华顺信安科技有限公司"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"bcec5b6a637bdb1117ddf0d20a95033b","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"wUc51H7\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"zTFoPVf2QiK6JzXDUR-7nA\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":8081,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.21.3\r\nDate: Wed, 24 Nov 2021 06:55:52 GMT\r\nContent-Type: text/html; charset=UTF-8\r\nContent-Length: 1495\r\nLast-Modified: Tue, 23 Nov 2021 02:23:00 GMT\r\nConnection: close\r\nETag: \"619c5084-5d7\"\r\nX-Frame-Options: SAMEORIGIN\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Headers: X-Request-With\r\nAccess-Control-Allow-Methods: GET,POST,OPTIONS\r\nAccept-Ranges: bytes","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_x.x\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:58:38 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nLocation: https://************/auth/login\r\nCache-Control: no-cache\r\nSet-Cookie: _fofa_EE_session=554d288704c1170b8f986be271a3d620; path=/;httponly;secure;; HttpOnly\r\nX-Request-Id: 6cde97af-f028-4d83-8806-eacc90f2c9c8\r\nX-Runtime: 0.083837","certs":{"not_after":"2030-05-11 07:52:02","subject_cn":"************","issuer_cn":"************","subject_org":["HSXA"],"issuer_org":["HSXA"],"not_before":"2020-05-13 07:52:02","v":"v1","sn":"12078017394137921867","sig_alth":"SHA256-RSA"}},{"protocol":"java-rmi","port":1099,"banner":"N\\x00\f*.*.*.*\\x00\\x00\\xd70","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 404 \r\nContent-Type: application/json\r\nDate: Wed, 24 Nov 2021 07:06:52 GMT\r\nConnection: close","certs":null},{"protocol":"http","port":8000,"banner":"HTTP/1.0 200 OK\r\nAccess-Control-Allow-Credentials: true\r\nAccess-Control-Allow-Headers: Content-Type,Content-Length,Accept,Accept-Encoding,Accept-Language,Referer,Connection,X-Access-Token,Authorization,Origin,Cache-Control,X-Requested-With,X-Check-Result,Content-Disposition,Host\r\nAccess-Control-Allow-Methods: GET,HEAD,POST,PUT,PATCH,DELETE,CONNECT,OPTIONS,TRACE\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Expose-Headers: Content-Type,Content-Length,Accept,Accept-Encoding,Accept-Language,Referer,Connection,X-Access-Token,Authorization,Origin,Cache-Control,X-Requested-With,X-Check-Result,Content-Disposition,Host\r\nContent-Type: application/json; charset=utf-8\r\nDate: Wed, 24 Nov 2021 07:07:02 GMT\r\nContent-Length: 88","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:10:03 GMT\r\nContent-Type: text/html\r\nContent-Length: 178\r\nConnection: close\r\nLocation: https://************/","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,8081,443],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":8772,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Pivotal Software, Inc.","ports":[8080],"title":"vmware-Spring-Framework"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443],"title":"jQuery"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":20777,"second_cat_tag":"组件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Layui"},{"belong_level":0,"rule_id":532625,"second_cat_tag":"威胁分析与管理","soft_hard_code":1,"first_cat_tag":"安全产品","level_code":1,"company":"北京华顺信安科技有限公司","ports":[443],"title":"华顺信安-FOEYE"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"d41736f32ce423812d8287d3687e60ca","_score":1,"_source":{"hosts":null,"ip":"***********","port":6379,"name":null,"common_title":"Redis未授权访问漏洞","mac":"00:0c:29:4f:3f:c4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["redis","Linux-操作系统","OpenSSH"],"cat_tags":["数据库系统","操作系统","其他支撑系统"],"company_tags":["Redis Labs","其他"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://***********:6379","vulfile":"redis_unauthorized_access.json","url":"***********:6379","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"ccfcf9f7a8b53485043c7173dac66eec","scan_engine":4,"port_list":[{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2143\r\n# Server\r\nredis_version:3.2.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:ad6ec0fc2feacc08\r\nredis_mode:standalone\r\nos:Linux 3.10.0-327.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1431\r\nrun_id:e0b8718c651e8cbf190ade7e0c01898e97bfbae4\r\ntcp_port:6379\r\nuptime_in_seconds:505045\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348269\r\nexecutable:/usr/local/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:821320\r\nused_memory_human:802.07K\r\nused_memory_rss:7704576\r\nused_memory_rss_human:7.35M\r\nused_memory_peak:822344\r\nused_memory_peak_human:803.07K\r\ntotal_system_memory:3975643136\r\ntotal_system_memory_human:3.70G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:9.38\r\nmem_allocator:jemalloc-4.0.3\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637233176\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:-1\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:61\r\ntotal_commands_processed:71\r\ninstantaneous_ops_per_sec:0\r\ntotal_net_input_bytes:868\r\ntotal_net_output_bytes:130366\r\ninstantaneous_input_kbps:0.00\r\ninstantaneous_output_kbps:0.00\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:0\r\nevicted_keys:0\r\nkeyspace_hits:0\r\nkeyspace_misses:0\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:0\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:342.04\r\nused_cpu_user:151.23\r\nused_cpu_sys_children:0.00\r\nused_cpu_user_children:0.00\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\n\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$2143\r\n# Server\r\nredis_version:3.2.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:ad6ec0fc2feacc08\r\nredis_mode:standalone\r\nos:Linux 3.10.0-327.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:1431\r\nrun_id:e0b8718c651e8cbf190ade7e0c01898e97bfbae4\r\ntcp_port:6379\r\nuptime_in_seconds:505477\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348701\r\nexecutable:/usr/local/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:821312\r\nused_memory_human:802.06K\r\nused_memory_rss:7704576\r\nused_memory_rss_human:7.35M\r\nused_memory_peak:822344\r\nused_memory_peak_human:803.07K\r\ntotal_system_memory:3975643136\r\ntotal_system_memory_human:3.70G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:9.38\r\nmem_allocator:jemalloc-4.0.3\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_la","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"ed437983aed6dbed923ae8cf8e045de0","_score":1,"_source":{"hosts":null,"ip":"**********","port":2181,"name":"WPS 开放平台","common_title":"zookeeper四字命令任意执行","mac":"b4:96:91:98:4b:f8","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","OpenSSH","Oracle-MySQL","APACHE-ZooKeeper","Prometheus-Time-Series-Collection-and-Processing-Server","Prometheus-Alertmanager","jQuery","Bootstrap","jQuery_UI","七牛云-七牛CDN"],"cat_tags":["服务","其他支撑系统","数据库系统","大数据处理","中间件","开发框架"],"company_tags":["其他","Oracle Corporation","Apache Software Foundation.","The Linux Foundation.","The jQuery Foundation.","twitter","上海七牛信息技术有限公司"],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://**********:2181","vulfile":"zookeeper_disclouser.json","url":"**********:2181","obj_type":1,"object":"**********","intranet_ip":1,"addition":null,"merge_md5":"70057763807c0f23e69e08c0709a7315","scan_engine":4,"port_list":[{"protocol":"unknown","port":9100,"banner":"","certs":null},{"protocol":"zookeeper","port":2181,"banner":"Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:51222[0](queued=0,recved=1,sent=0)\n /*.*.*.*:49382[1](queued=0,recved=37794,sent=37795)\n\nLatency min/avg/max: 0/0/59\nReceived: 88067\nSent: 88067\nConnections: 2\nOutstanding: 0\nZxid: 0x2d9\nMode: standalone\nNode count: 166\n","certs":null},{"protocol":"http","port":9093,"banner":"HTTP/1.0 200 OK\r\nAccept-Ranges: bytes\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Length: 1314\r\nContent-Type: text/html; charset=utf-8\r\nExpires: 0\r\nLast-Modified: Thu, 01 Jan 1970 00:00:01 GMT\r\nPragma: no-cache\r\nDate: Wed, 24 Nov 2021 06:54:27 GMT","certs":null},{"protocol":"http","port":9200,"banner":"HTTP/1.0 401 Unauthorized\r\nWWW-Authenticate: Basic realm=\"security\" charset=\"UTF-8\"\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 381\r\n\r\n{\"error\":{\"root_cause\":[{\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}}],\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}},\"status\":401}","certs":null},{"protocol":"kafka","port":9092,"banner":"Kafka\nAddr: **********:9092\nTopics: DOCS_CPS_TASK __consumer_offsets DOCS_STAT_APP_ACTIVITY","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.33-log \r\nN\\x00\\x00\\x00\n5.7.33-log\\x007[\\x00\\x00?]a\\x15Gp\\x16/\\x00\\xff\\xff\\xe0\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x1c4%\\x17{|6Y\u000b1-Y\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":5000,"banner":"HTTP/1.0 200 OK\r\nCache-Control: no-cache\r\nDate: Wed, 24 Nov 2021 07:07:06 GMT\r\nContent-Length: 0","certs":null},{"protocol":"etcd","port":2379,"banner":"Name: etcd1\nId: 80286fd956d980e5\nStartTime: 2021-11-18T11:28:14.475600154Z\nVersion: 3.2.0+git\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 7975\r\nConnection: close\r\nVary: Accept-Encoding\r\nServer: APISIX/2.0\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:08:59 GMT\r\nVary: Accept-Encoding\r\nLast-Modified: Tue, 26 Oct 2021 15:52:59 GMT\r\nVary: Accept-Encoding\r\nETag: \"6178245b-1f27\"\r\nAccept-Ranges: bytes\r\nx-origin: http://**********\r\nEncryption: 0","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"https","port":8081,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:11:39 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 323\r\nConnection: close","certs":{"not_after":"2119-04-16 15:24:08","subject_cn":"**********","issuer_cn":"WPS","not_before":"2021-10-26 15:24:08","v":"v3","domain":"localhost","sn":"11157282297511052792","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":9090,"banner":"HTTP/1.0 302 Found\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /prometheus\r\nDate: Wed, 24 Nov 2021 07:13:22 GMT\r\nContent-Length: 34","certs":null},{"protocol":"http","port":8888,"banner":"HTTP/1.0 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Length: 6586\r\nX-Content-Type-Options: nosniff\r\nReferrer-Policy: same-origin","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,8081],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":21157,"second_cat_tag":"大数据处理","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Apache Software Foundation.","ports":[2181],"title":"APACHE-ZooKeeper"},{"belong_level":0,"rule_id":338397,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"The Linux Foundation.","ports":[9090],"title":"Prometheus-Time-Series-Collection-and-Processing-Server"},{"belong_level":0,"rule_id":338447,"second_cat_tag":"其他支撑系统","soft_hard_code":0,"first_cat_tag":"支撑系统","level_code":5,"company":"The Linux Foundation.","ports":[9093],"title":"Prometheus-Alertmanager"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8888],"title":"jQuery"},{"belong_level":0,"rule_id":139,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"twitter","ports":[8888],"title":"Bootstrap"},{"belong_level":0,"rule_id":255,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8888],"title":"jQuery_UI"},{"belong_level":0,"rule_id":4605,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"上海七牛信息技术有限公司","ports":[8888],"title":"七牛云-七牛CDN"}],"common_description":"ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。","common_impact":"<p>ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。</p>","recommandation":"<p>1、3.4.10以上版本可禁止四字命令，在安装目录下conf/zoo.cfg文件中加一条配置，如下：</p><blockquote><p>4lw.commands.whitelist=</p></blockquote><p>可能影响业务，请确认后再操作。</p><p>2、通过防火墙等安全设备设置访问策略，设置白名单访问。</p><p>3、禁止把Zookeeper直接暴露在公网。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":" /**********:49382[1](queued=0,recved=37918,sent=37919,sid=0x100000542870002,lop=PING,est=1637234985953,to=40000,lcxid=0xbf,lzxid=0x2d9,lresp=503773984,llat=0,minlat=0,avglat=0,maxlat=29)\n /************:42636[0](queued=0,recved=1,sent=0)\n\n","has_response":1,"createtime":"2021-11-24 15:18:35","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:18:35"}}
{"_index":"fofaee_threats","_type":"threats","_id":"360dfec436a60870414f5b5f2fba2762","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":"00:0c:29:34:b7:e4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Oracle-MySQL","OpenSSH","elastic-Elasticsearch"],"cat_tags":["服务","数据库系统","其他支撑系统"],"company_tags":["其他","Oracle Corporation","Elastic, Inc."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"9dbfd9b56771bc31a5ceaa943cc5ba22","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 493\r\n\r\n{\n  \"name\" : \"SguABt6\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"-QzWbcg_R1GcPUX7JkmHGg\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:24:07 GMT\r\nContent-Type: text/html\r\nContent-Length: 6192\r\nLast-Modified: Thu, 11 Nov 2021 07:35:41 GMT\r\nConnection: close\r\nETag: \"618cc7cd-1830\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"http","port":8000,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:33:46 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:34:07 GMT\r\nContent-Type: text/html\r\nContent-Length: 6192\r\nLast-Modified: Thu, 11 Nov 2021 07:35:41 GMT\r\nConnection: close\r\nETag: \"618cc7cd-1830\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":{"not_after":"2020-06-15 23:59:59","subject_cn":"focii.cn","issuer_cn":"Sectigo RSA Domain Validation Secure Server CA","issuer_org":["Sectigo Limited"],"not_before":"2019-05-13 00:00:00","v":"v3","domain":"focii.cn","sn":"182191660422272240828351337638536332243","sig_alth":"SHA256-RSA"}},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.68-MariaDB \r\nR\\x00\\x00\\x00\n5.5.68-MariaDB\\x00\\xda~\\x01\\x00`uL\\gq&c\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00B^5,{I9!:AuK\\x00mysql_native_password\\x00","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443,8000],"title":"NGINX"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"2cb4b6b2030b2bd165b4cc2494008851","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"网络资产与安全风险综合管控平台","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:40:da:9c","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["elastic-Elasticsearch","Oracle-MySQL","NGINX","OpenSSH"],"cat_tags":["数据库系统","服务","其他支撑系统"],"company_tags":["Elastic, Inc.","Oracle Corporation","其他"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"9ff0f7e30f5596ec1ab1e8006e708846","scan_engine":4,"port_list":[{"protocol":"mysql","port":3306,"banner":"G\\x00\\x00\\x00\\xffj\\x04Host '************' is not allowed to connect to this MariaDB server","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.16.1\r\nDate: Wed, 24 Nov 2021 07:05:37 GMT\r\nContent-Type: text/html\r\nContent-Length: 4266\r\nLast-Modified: Tue, 03 Nov 2020 05:50:39 GMT\r\nConnection: close\r\nETag: \"5fa0efaf-10aa\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx/1.16.1\r\nDate: Wed, 24 Nov 2021 07:06:35 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"B9HtWYa\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"rPtszVVCQwOik_lKjnHuWg\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"dca186d4fb87381a446cd41b357b95ce","_score":1,"_source":{"hosts":null,"ip":"***********","port":2181,"name":"发现者一号后台登陆","common_title":"zookeeper四字命令任意执行","mac":"00:0c:29:c0:85:d4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ubuntu-系统","OpenSSH","Oracle-MySQL","APACHE-ZooKeeper","NGINX","redis","Linux-操作系统","jQuery"],"cat_tags":["操作系统","其他支撑系统","数据库系统","大数据处理","服务","中间件"],"company_tags":["Canonical Ltd.","其他","Oracle Corporation","Apache Software Foundation.","Redis Labs","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://***********:2181","vulfile":"zookeeper_disclouser.json","url":"***********:2181","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"224b6294afe6a7325c61f096ab6ca368","scan_engine":4,"port_list":[{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1p1 Ubuntu-2ubuntu2.13\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 302 \r\nServer: nginx/1.4.6 (Ubuntu)\r\nDate: Wed, 24 Nov 2021 06:56:21 GMT\r\nContent-Length: 0\r\nConnection: close\r\nLocation: http://***********/login.html","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.23 \r\nJ\\x00\\x00\\x00\n5.7.23\\x00\\x07\\x02\\x00\\x00yKJWPXY|\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00<0[?\\x10+5X^qI\\x1d\\x00mysql_native_password\\x00","certs":null},{"protocol":"mdns","port":5353,"banner":"\\x00\\x00\\x84\\x00\\x00\\x01\\x00\\x01\\x00\\x00\\x00\\x00\t_services\\x07_dns-sd\\x04_udp\\x05local\\x00\\x00\f\\x00\\x01\\xc0\f\\x00\f\\x00\\x01\\x00\\x00\\x00\n\\x00\\x14\f_workstation\\x04_tcp\\xc0#","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 302 \r\nLocation: http://***********:8080/login.html\r\nContent-Length: 0\r\nDate: Wed, 24 Nov 2021 07:05:19 GMT\r\nConnection: close","certs":null},{"protocol":"epmd","port":4369,"banner":"name rabbit at port 40310\n","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$1710\r\n# Server\r\nredis_version:2.8.4\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:645b61b5aa39f6b1\r\nredis_mode:standalone\r\nos:Linux 4.4.0-148-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.4\r\nprocess_id:2375\r\nrun_id:7ca0770c0684114cc66ca73e21467c1e54a79131\r\ntcp_port:6379\r\nuptime_in_seconds:502799\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:195920\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:501208\r\nused_memory_human:489.46K\r\nused_memory_rss:2351104\r\nused_memory_peak:500280\r\nused_memory_peak_human:488.55K\r\nused_memory_lua:33792\r\nmem_fragmentation_ratio:4.69\r\nmem_allocator:jemalloc-3.5.1\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637234969\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:-1\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:62\r\ntotal_commands_processed:78\r\ninstantaneous_ops_per_sec:0\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:0\r\nevicted_keys:0\r\nkeyspace_hits:0\r\nkeyspace_misses:0\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:66.11\r\nused_cpu_user:87.64\r\nused_cpu_sys_children:0.00\r\nused_cpu_user_children:0.00\r\n\r\n# Keyspace\r\n\r\n","certs":null},{"protocol":"pptp","port":1723,"banner":"Firmware: 1\nHostname: local\nVendor: linux\n","certs":null},{"protocol":"zookeeper","port":2181,"banner":"Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:52730[0](queued=0,recved=1,sent=0)\n\nLatency min/avg/max: 0/0/0\nReceived: 59\nSent: 58\nConnections: 1\nOutstanding: 0\nZxid: 0x32\nMode: standalone\nNode count: 4\n","certs":null},{"protocol":"amqp","port":5672,"banner":"AMQP\\x00\\x00\t\\x01","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":4528,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Canonical Ltd.","ports":[22,80],"title":"ubuntu-系统"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":21157,"second_cat_tag":"大数据处理","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"Apache Software Foundation.","ports":[2181],"title":"APACHE-ZooKeeper"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80],"title":"NGINX"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379,1723],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[80,8080],"title":"jQuery"}],"common_description":"ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。","common_impact":"<p>ZooKeeper是一个分布式的，开放源码的分布式应用程序协调服务，是Hadoop和Hbase的重要组件。zookeeper的四字命令可被任何人执行，泄露敏感信息，如SessionID等。</p>","recommandation":"<p>1、3.4.10以上版本可禁止四字命令，在安装目录下conf/zoo.cfg文件中加一条配置，如下：</p><blockquote><p>4lw.commands.whitelist=</p></blockquote><p>可能影响业务，请确认后再操作。</p><p>2、通过防火墙等安全设备设置访问策略，设置白名单访问。</p><p>3、禁止把Zookeeper直接暴露在公网。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":" /************:36964[0](queued=0,recved=1,sent=0)\n\n","has_response":1,"createtime":"2021-11-24 15:18:35","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:18:35"}}
{"_index":"fofaee_threats","_type":"threats","_id":"3a0873cf1fa63f1e2864787651385d9a","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"资产识别系统","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:9f:64:2a","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["elastic-Elasticsearch","OpenSSH","NGINX"],"cat_tags":["数据库系统","其他支撑系统","服务"],"company_tags":["Elastic, Inc.","其他"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"53b7c6bff0138eb940b87b137daacd83","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"B9HtWYa\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"rPtszVVCQwOik_lKjnHuWg\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:28:13 GMT\r\nContent-Type: text/html\r\nContent-Length: 162\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:31:04 GMT\r\nContent-Type: text/html\r\nContent-Length: 5692\r\nLast-Modified: Tue, 23 Nov 2021 08:30:28 GMT\r\nConnection: close\r\nETag: \"619ca6a4-163c\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"0fc38e735b33289be4e4acbb217e4ad5","_score":1,"_source":{"hosts":null,"ip":"************","port":445,"name":null,"common_title":"MS17-010 SMB  远程溢出","mac":"d0:94:66:66:04:1e","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ubuntu-系统","OpenSSH","vmware-认证服务","Oracle-MySQL","Microsoft-Windows"],"cat_tags":["操作系统","其他支撑系统","其他企业应用","数据库系统"],"company_tags":["Canonical Ltd.","其他","VMware, Inc.","Oracle Corporation","Microsoft Corporation"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:445","vulfile":"smb_ms17_010_rce.json","url":"http://************:445","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"6ceb07396b193d86e28c0bb217941960","scan_engine":4,"port_list":[{"protocol":"unknown","port":8020,"banner":"","certs":null},{"protocol":"unknown","port":7001,"banner":"","certs":null},{"protocol":"unknown","port":8087,"banner":"","certs":null},{"protocol":"unknown","port":8200,"banner":"","certs":null},{"protocol":"unknown","port":49152,"banner":"","certs":null},{"protocol":"mysql","port":3306,"banner":"E\\x00\\x00\\x00\\xffj\\x04Host '************' is not allowed to connect to this MySQL server","certs":null},{"protocol":"unknown","port":8090,"banner":"","certs":null},{"protocol":"unknown","port":49153,"banner":"","certs":null},{"protocol":"netbios","port":137,"banner":"NetBIOS Response\nMAC:d0:94:66:66:04:1e\nHostname:WIN-JMSQ2I9FNS3<0>\nWORKGROUP<0>\nWIN-JMSQ2I9FNS3<20>\n","certs":null},{"protocol":"unknown","port":27017,"banner":"","certs":null},{"protocol":"unknown","port":8086,"banner":"","certs":null},{"protocol":"unknown","port":9200,"banner":"","certs":null},{"protocol":"unknown","port":8500,"banner":"","certs":null},{"protocol":"unknown","port":1433,"banner":"","certs":null},{"protocol":"netbios-ssn","port":139,"banner":"\\x83\\x00\\x00\\x01\\x8f","certs":null},{"protocol":"unknown","port":49154,"banner":"","certs":null},{"protocol":"pop3","port":110,"banner":"+OK TDpop3Server 1.0 POP3 Server ready.\r\n","certs":null},{"protocol":"decrpc","port":135,"banner":"\\x05\\x00\r\\x03\\x10\\x00\\x00\\x00\\x18\\x00\\x00\\x00\\x01\\x00\\x00\\x00\\x04\\x00\\x01\\x05\\x00\\x00\\x00\\x00","certs":null},{"protocol":"smb","port":445,"banner":"Version: 6.1Build 7601\nTarget Name : WIN-JMSQ2I9FNS3\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.6p1 Ubuntu-4ubuntu0.5\r\n","certs":null},{"protocol":"unknown","port":873,"banner":"","certs":null},{"protocol":"unknown","port":80,"banner":"HTTP/1.1 400 Bad Request\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:06:44 GMT\r\nContent-Type: text/html\r\nContent-Length: 166\r\nConnection: close\r\n\r\n<html>\r\n<head><title>400 Bad Request</title></head>\r\n<body bgcolor=\"white\">\r\n<center><h1>400 Bad Request</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","certs":null},{"protocol":"unknown","port":443,"banner":"","certs":null},{"protocol":"unknown","port":1344,"banner":"","certs":null},{"protocol":"unknown","port":11211,"banner":"","certs":null},{"protocol":"ldap","port":389,"banner":"0\f\\x02\\x01\\x01a\\x07\n\\x01\\x00\\x04\\x00\\x04\\x00","certs":null},{"protocol":"unknown","port":6379,"banner":"","certs":null},{"protocol":"unknown","port":2001,"banner":"","certs":null},{"protocol":"unknown","port":8080,"banner":"","certs":null},{"protocol":"unknown","port":3389,"banner":"","certs":null},{"protocol":"vmware_authentication_daemon","port":902,"banner":"220 VMware Authentication Daemon Version 1.10: SSL Required, ServerDaemonProtocol:SOAP, MKSDisplayProtocol:VNC , , NFCSSL supported/t\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":4528,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Canonical Ltd.","ports":[22],"title":"ubuntu-系统"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7206,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"VMware, Inc.","ports":[902],"title":"vmware-认证服务"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7220,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Microsoft Corporation","ports":[445],"title":"Microsoft-Windows"}],"common_description":"NSA组织爆出的黑客武器。如果攻击者向 Windows SMBv1 服务器发送特殊设计的消息，可造成远程执行代码。变种蠕虫病毒永恒之蓝对全球造成大规模的感染。","common_impact":"<p>攻击者可以利用该漏洞在目标系统上执行任意代码，可能会导致服务器被控制。即使攻击失败，也会导致服务器拒绝服务，对业务系统造成影响。</p>","recommandation":"<p>1、优先措施：个人电脑开启防火墙拦截外部访问本机TCP445端口，服务器开启安全策略限制指定IP访问本机TCP445端口。</p><p>2、补丁更新：可以通过系统自带的更新功能打补丁，也可以单独安装具体的补丁，对应版本参考如下微软更新指南：</p><p><a href=\"https://portal.msrc.microsoft.com/zh-cn/security-guidance/advisory/CVE-2017-11780\" target=\"_blank\">https://portal.msrc.microsoft.com/zh-cn/security-guidance/advisory/CVE-2017-11780</a></p><p>找到对应的系统版本，点击“Security Update”即可下载单独的补丁。</p><p>如果想启动windowsupdate 更新，可在命令提示符下，键入wuauclt.exe /detectnow。也可以在Windows服务列表中启动Windows update服务，将该服务设为启动。重启即可。</p><p>3、安全配置：如果某些特殊环境下的系统不方便打补丁，可以参考如下安全配置进行变通处理。如何在 Windows 和 Windows Server 中启用和禁用SMBv1、SMBv2和SMBv3的指南：</p><p><a href=\"可以参考如何在 Windows 和 Windows Server 中启用和禁用SMBv1、SMBv2和SMBv3的指南：  https://support.microsoft.com/zh-cn/help/2696547/how-to-detect-enable-and-disable-smbv1-smbv2-and-smbv3-in-windows-and\" target=\"_blank\">https://support.microsoft.com/zh-cn/help/2696547/how-to-detect-enable-and-disable-smbv1-smbv2-and-smbv3-in-windows-and</a></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2017-11780","vulType":"命令执行","has_exp":0,"is_ipv6":false,"last_response":"","has_response":0,"createtime":"2021-11-24 15:17:49","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:49"}}
{"_index":"fofaee_threats","_type":"threats","_id":"dcdbf3a0c55209866c9415f9325a6379","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"308 Permanent Redirect","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:eb:b3:a8","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["MongoDB-数据库","NGINX","OpenSSH","elastic-Elasticsearch","redis","HARBOR"],"cat_tags":["数据库系统","服务","其他支撑系统","虚拟化"],"company_tags":["MongoDB, Inc","其他","Elastic, Inc.","Redis Labs"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"4a05890fed971fcdacfebfdded37706c","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n-NOAUTH Authentication required.\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:52:59 GMT\r\nContent-Type: text/html\r\nContent-Length: 856\r\nConnection: close\r\nLast-Modified: Wed, 16 Sep 2020 02:45:15 GMT\r\nETag: \"5f617c3b-358\"\r\nCache-Control: no-store, no-cache, must-revalidate\r\nAccept-Ranges: bytes\r\nStrict-Transport-Security: max-age=31536000; includeSubdomains; preload\r\nX-Frame-Options: DENY\r\nContent-Security-Policy: frame-ancestors 'none'","certs":{"not_after":"2021-10-21 11:00:23","subject_cn":"fofa.so","issuer_cn":"fofa.so","subject_org":["fofa.so"],"issuer_org":["fofa.so"],"not_before":"2020-10-21 11:00:23","v":"v3","sn":"12359437977138707854","sig_alth":"SHA256-RSA"}},{"protocol":"mongodb","port":27017,"banner":"{\n \"asserts\": {\n  \"msg\": 0,\n  \"regular\": 0,\n  \"rollovers\": 0,\n  \"user\": 0,\n  \"warning\": 0\n },\n \"connections\": {\n  \"active\": 1,\n  \"available\": 838858,\n  \"awaitingTopologyChanges\": 0,\n  \"current\": 2,\n  \"exhaustIsMaster\": 0,\n  \"totalCreated\": 88\n },\n \"electionMetrics\": {\n  \"averageCatchUpOps\": 0,\n  \"catchUpTakeover\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"electionTimeout\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"freezeTimeout\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"numCatchUps\": 0,\n  \"numCatchUpsAlreadyCaughtUp\": 0,\n  \"numCatchUpsFailedWithError\": 0,\n  \"numCatchUpsFailedWithNewTerm\": 0,\n  \"numCatchUpsFailedWithReplSetAbortPrimaryCatchUpCmd\": 0,\n  \"numCatchUpsSkipped\": 0,\n  \"numCatchUpsSucceeded\": 0,\n  \"numCatchUpsTimedOut\": 0,\n  \"numStepDownsCausedByHigherTerm\": 0,\n  \"priorityTakeover\": {\n   \"called\": 0,\n   \"successful\": 0\n  },\n  \"stepUpCmd\": {\n   \"called\": 0,\n   \"successful\": 0\n  }\n },\n \"extra_info\": {\n  \"input_blocks\": 167920,\n  \"involuntary_context_switches\": 630,\n  \"maximum_resident_set_kb\": 114044,\n  \"note\": \"fields vary by platform\",\n  \"output_blocks\": 2848448,\n  \"page_faults\": 67,\n  \"page_reclaims\": 25583,\n  \"system_time_us\": 909666138,\n  \"user_time_us\": 886928887,\n  \"voluntary_context_switches\": 17682986\n },\n \"flowControl\": {\n  \"enabled\": true,\n  \"isLagged\": false,\n  \"isLaggedCount\": 0,\n  \"isLaggedTimeMicros\": 0,\n  \"locksPerKiloOp\": 0,\n  \"sustainerRate\": 0,\n  \"targetRateLimit\": 1********0,\n  \"timeAcquiringMicros\": 10909\n },\n \"freeMonitoring\": {\n  \"state\": \"undecided\"\n },\n \"globalLock\": {\n  \"activeClients\": {\n   \"readers\": 0,\n   \"total\": 0,\n   \"writers\": 0\n  },\n  \"currentQueue\": {\n   \"readers\": 0,\n   \"total\": 0,\n   \"writers\": 0\n  },\n  \"totalTime\": 502142381000\n },\n \"host\": \"a35ae3d41154\",\n \"localTime\": \"2021-11-24T14:58:43.974+08:00\",\n \"locks\": {\n  \"Collection\": {\n   \"acquireCount\": {\n    \"W\": 2,\n    \"r\": 519343,\n    \"w\": 8368\n   }\n  },\n  \"Database\": {\n   \"acquireCount\": {\n    \"W\": 4,\n    \"r\": 515907,\n    \"w\": 8368\n   }\n  },\n  \"Global\": {\n   \"acquireCount\": {\n    \"W\": 4,\n    \"r\": 1520236,\n    \"w\": 8372\n   }\n  },\n  \"Mutex\": {\n   \"acquireCount\": {\n    \"r\": 524278\n   }\n  },\n  \"ParallelBatchWriterMode\": {\n   \"acquireCount\": {\n    \"r\": 17232\n   }\n  },\n  \"ReplicationStateTransition\": {\n   \"acquireCount\": {\n    \"w\": 1528612\n   }\n  },\n  \"oplog\": {\n   \"acquireCount\": {\n    \"r\": 502119\n   }\n  }\n },\n \"logicalSessionRecordCache\": {\n  \"activeSessionsCount\": 0,\n  \"lastSessionsCollectionJobCursorsClosed\": 0,\n  \"lastSessionsCollectionJobDurationMillis\": 0,\n  \"lastSessionsCollectionJobEntriesEnded\": 0,\n  \"lastSessionsCollectionJobEntriesRefreshed\": 0,\n  \"lastSessionsCollectionJobTimestamp\": \"2021-11-24T14:54:44.668+08:00\",\n  \"lastTransactionReaperJobDurationMillis\": 0,\n  \"lastTransactionReaperJobEntriesCleanedUp\": 0,\n  \"lastTransactionReaperJobTimestamp\": \"2021-11-24T14:54:44.67+08:00\",\n  \"sessionCatalogSize\": 0,\n  \"sessionsCollectionJobCount\": 1674,\n  \"transactionReaperJobCount\": 1674\n },\n \"mem\": {\n  \"bits\": 64,\n  \"resident\": 110,\n  \"supported\": true,\n  \"virtual\": 1555\n },\n \"metrics\": {\n  \"aggStageCounters\": {\n   \"$_internalInhibitOptimization\": 0,\n   \"$_internalSplitPipeline\": 0,\n   \"$addFields\": 0,\n   \"$bucket\": 0,\n   \"$bucketAuto\": 0,\n   \"$changeStream\": 0,\n   \"$collStats\": 0,\n   \"$count\": 0,\n   \"$currentOp\": 0,\n   \"$facet\": 0,\n   \"$geoNear\": 0,\n   \"$graphLookup\": 0,\n   \"$group\": 0,\n   \"$indexStats\": 0,\n   \"$limit\": 0,\n   \"$listLocalSessions\": 0,\n   \"$listSessions\": 0,\n   \"$lookup\": 0,\n   \"$match\": 0,\n   \"$merge\": 0,\n   \"$mergeCursors\": 0,\n   \"$out\": 0,\n   \"$planCacheStats\": 0,\n   \"$project\": 0,\n   \"$redact\": 0,\n   \"$replaceRoot\": 0,\n   \"$replaceWith\": 0,\n   \"$sample\": 0,\n   \"$set\": 0,\n   \"$skip\": 0,\n   \"$sort\": 0,\n   \"$sortByCount\": 0,\n   \"$unionWith\": 0,\n   \"$unset\": 0,\n   \"$unwind\": 0\n  },\n  \"commands\": {\n   \"\\u003cUNKNOWN\\u003e\": 0,\n   \"_addShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_cloneCollectionOptionsFromPrimaryShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrAddShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrAddShardToZone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerCollectionStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrBalancerStop\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrClearJumboFlag\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkMerge\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkMigration\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitChunkSplit\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCommitMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCreateCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrCreateDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrDropCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrDropDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrEnableSharding\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrEnsureChunkVersionIsGreaterThan\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrMoveChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRefineCollectionShardKey\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRemoveShard\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrRemoveShardFromZone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrShardCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_configsvrUpdateZoneKeyRange\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_flushDatabaseCacheUpdates\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_flushRoutingTableCacheUpdates\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_getNextSessionMods\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_getUserCacheGeneration\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_isSelf\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_killOperations\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_mergeAuthzCollections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_migrateClone\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkAbort\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkCommit\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_recvChunkStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrCloneCatalogData\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrMovePrimary\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_shardsvrShardCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"_transferMods\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"abortTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"aggregate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"appendOplogNote\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"applyOps\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"authenticate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"availableQueryOptions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"buildInfo\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"checkShardingIndex\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"cleanupOrphaned\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"cloneCollectionAsCapped\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"collMod\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"collStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"commitTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"compact\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connPoolStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connPoolSync\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"connectionStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"convertToCapped\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"coordinateCommitTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"count\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"create\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createIndexes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"createUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"currentOp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dataSize\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dbHash\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dbStats\": {\n    \"failed\": 0,\n    \"total\": 104\n   },\n   \"delete\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"distinct\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"driverOIDTest\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"drop\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropAllRolesFromDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropAllUsersFromDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropConnections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropIndexes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"dropUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"endSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"explain\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"features\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"filemd5\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"find\": {\n    \"failed\": 0,\n    \"total\": 1675\n   },\n   \"findAndModify\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"flushRouterConfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"fsync\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"fsyncUnlock\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"geoSearch\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getCmdLineOpts\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDatabaseVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDefaultRWConcern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getDiagnosticData\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getFreeMonitoringStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getLastError\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getLog\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getMore\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getParameter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getShardMap\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getShardVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"getnonce\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"grantPrivilegesToRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"grantRolesToRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"grantRolesToUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"hostInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"insert\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"internalRenameIfOptionsAndIndexesMatch\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"invalidateUserCache\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"isMaster\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"killAllSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killAllSessionsByPattern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killCursors\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killOp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"killSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listCollections\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listCommands\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"listDatabases\": {\n    \"failed\": 0,\n    \"total\": 71\n   },\n   \"listIndexes\": {\n    \"failed\": 0,\n    \"total\": 3348\n   },\n   \"lockInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"logRotate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"logout\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"mapReduce\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"mapreduce\": {\n    \"shardedfinish\": {\n     \"failed\": 0,\n     \"total\": 0\n    }\n   },\n   \"mergeChunks\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"moveChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"ping\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"planCacheClear\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheClearFilters\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheListFilters\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"planCacheSetFilter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"prepareTransaction\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"profile\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"reIndex\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"refreshSessions\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"renameCollection\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"repairDatabase\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetAbortPrimaryCatchUp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetFreeze\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetConfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetRBID\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetGetStatus\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetHeartbeat\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetInitiate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetMaintenance\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetReconfig\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetRequestVotes\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetResizeOplog\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepDown\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepDownWithForce\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetStepUp\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetSyncFrom\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"replSetUpdatePosition\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"resetError\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokePrivilegesFromRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokeRolesFromRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"revokeRolesFromUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"rolesInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"saslContinue\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"saslStart\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"serverStatus\": {\n    \"failed\": 0,\n    \"total\": 27\n   },\n   \"setDefaultRWConcern\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setFeatureCompatibilityVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setFreeMonitoring\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setIndexCommitQuorum\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setParameter\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"setShardVersion\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shardConnPoolStats\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shardingState\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"shutdown\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"splitChunk\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"splitVector\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"startRecordingTraffic\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"startSession\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"stopRecordingTraffic\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"top\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"unsetSharding\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"update\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"updateRole\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"updateUser\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"usersInfo\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"validate\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"voteCommitIndexBuild\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"waitForFailPoint\": {\n    \"failed\": 0,\n    \"total\": 0\n   },\n   \"whatsmyuri\": {\n    \"failed\": 0,\n    \"total\": 0\n   }\n  },\n  \"cursor\": {\n   \"open\": {\n    \"noTimeout\": 0,\n    \"pinned\": 0,\n    \"total\": 0\n   },\n   \"timedOut\": 0\n  },\n  \"document\": {\n   \"deleted\": 0,\n   \"inserted\": 0,\n   \"returned\": 0,\n   \"updated\": 0\n  },\n  \"getLastError\": {\n   \"default\": {\n    \"unsatisfiable\": 0,\n    \"wtimeouts\": 0\n   },\n   \"wtime\": {\n    \"num\": 0,\n    \"totalMillis\": 0\n   },\n   \"wtimeouts\": 0\n  },\n  \"operation\": {\n   \"scanAndOrder\": 0,\n   \"writeConflicts\": 0\n  },\n  \"query\": {\n   \"planCacheTotalSizeEstimateBytes\": 0,\n   \"updateOneOpStyleBroadcastWithExactIDCount\": 0\n  },\n  \"queryExecutor\": {\n   \"collectionScans\": {\n    \"nonTailable\": 0,\n    \"total\": 0\n   },\n   \"scanned\": 0,\n   \"scannedObjects\": 0\n  },\n  \"record\": {\n   \"moves\": 0\n  },\n  \"repl\": {\n   \"apply\": {\n    \"attemptsToBecomeSecondary\": 0,\n    \"batchSize\": 0,\n    \"batches\": {\n     \"num\": 0,\n     \"totalMillis\": 0\n    },\n    \"ops\": 0\n   },\n   \"buffer\": {\n    \"count\": 0,\n    \"maxSizeBytes\": 0,\n    \"sizeBytes\": 0\n   },\n   \"executor\": {\n    \"networkInterface\": \"DEPRECATED: getDiagnosticString is deprecated in NetworkInterfaceTL\",\n    \"pool\": {\n     \"inProgressCount\": 0\n    },\n    \"queues\": {\n     \"networkInProgress\": 0,\n     \"sleepers\": 0\n    },\n    \"shuttingDown\": false,\n    \"unsignaledEvents\": 0\n   },\n   \"initialSync\": {\n    \"completed\": 0,\n    \"failedAttempts\": 0,\n    \"failures\": 0\n   },\n   \"network\": {\n    \"bytes\": 0,\n    \"getmores\": {\n     \"num\": 0,\n     \"numEmptyBatches\": 0,\n     \"totalMillis\": 0\n    },\n    \"notMasterLegacyUnacknowledgedWrites\": 0,\n    \"notMasterUnacknowledgedWrites\": 0,\n    \"oplogGetMoresProcessed\": {\n     \"num\": 0,\n     \"totalMillis\": 0\n    },\n    \"ops\": 0,\n    \"readersCreated\": 0,\n    \"replSetUpdatePosition\": {\n     \"num\": 0\n    }\n   },\n   \"stateTransition\": {\n    \"lastStateTransition\": \"\",\n    \"userOperationsKilled\": 0,\n    \"userOperationsRunning\": 0\n   },\n   \"syncSource\": {\n    \"numSelections\": 0,\n    \"numTimesChoseDifferent\": 0,\n    \"numTimesChoseSame\": 0,\n    \"numTimesCouldNotFind\": 0\n   }\n  },\n  \"ttl\": {\n   \"deletedDocuments\": 0,\n   \"passes\": 8368\n  }\n },\n \"network\": {\n  \"bytesIn\": 18312,\n  \"bytesOut\": 1203604,\n  \"compression\": {\n   \"snappy\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   },\n   \"zlib\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   },\n   \"zstd\": {\n    \"compressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    },\n    \"decompressor\": {\n     \"bytesIn\": 0,\n     \"bytesOut\": 0\n    }\n   }\n  },\n  \"numRequests\": 310,\n  \"numSlowDNSOperations\": 0,\n  \"numSlowSSLOperations\": 0,\n  \"physicalBytesIn\": 18312,\n  \"physicalBytesOut\": 1203604,\n  \"serviceExecutorTaskStats\": {\n   \"executor\": \"passthrough\",\n   \"threadsRunning\": 2\n  },\n  \"tcpFastOpen\": {\n   \"accepted\": 0,\n   \"clientSupported\": false,\n   \"kernelSetting\": 0,\n   \"serverSupported\": true\n  }\n },\n \"ok\": 1,\n \"opLatencies\": {\n  \"commands\": {\n   \"latency\": 165783,\n   \"ops\": 309\n  },\n  \"reads\": {\n   \"latency\": 0,\n   \"ops\": 0\n  },\n  \"transactions\": {\n   \"latency\": 0,\n   \"ops\": 0\n  },\n  \"writes\": {\n   \"latency\": 0,\n   \"ops\": 0\n  }\n },\n \"opReadConcernCounters\": {\n  \"available\": 0,\n  \"linearizable\": 0,\n  \"local\": 0,\n  \"majority\": 0,\n  \"none\": 1675,\n  \"snapshot\": 0\n },\n \"opcounters\": {\n  \"command\": 3658,\n  \"delete\": 0,\n  \"getmore\": 0,\n  \"insert\": 0,\n  \"query\": 1675,\n  \"update\": 0\n },\n \"opcountersRepl\": {\n  \"command\": 0,\n  \"delete\": 0,\n  \"getmore\": 0,\n  \"insert\": 0,\n  \"query\": 0,\n  \"update\": 0\n },\n \"pid\": 1,\n \"process\": \"mongod\",\n \"security\": {\n  \"authentication\": {\n   \"mechanisms\": {\n    \"MONGODB-X509\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    },\n    \"SCRAM-SHA-1\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    },\n    \"SCRAM-SHA-256\": {\n     \"authenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     },\n     \"speculativeAuthenticate\": {\n      \"received\": 0,\n      \"successful\": 0\n     }\n    }\n   }\n  }\n },\n \"storageEngine\": {\n  \"backupCursorOpen\": false,\n  \"dropPendingIdents\": 0,\n  \"name\": \"wiredTiger\",\n  \"oldestRequiredTimestampForCrashRecovery\": 0,\n  \"persistent\": true,\n  \"readOnly\": false,\n  \"supportsCommittedReads\": true,\n  \"supportsPendingDrops\": true,\n  \"supportsSnapshotReadConcern\": true,\n  \"supportsTwoPhaseIndexBuild\": true\n },\n \"tcmalloc\": {\n  \"generic\": {\n   \"current_allocated_bytes\": 82341944,\n   \"heap_size\": 95567872\n  },\n  \"tcmalloc\": {\n   \"aggressive_memory_decommit\": 0,\n   \"central_cache_free_bytes\": 281984,\n   \"current_total_thread_cache_bytes\": 1031240,\n   \"formattedString\": \"------------------------------------------------\\nMALLOC:       82342520 (   78.5 MiB) Bytes in use by application\\nMALLOC: +      4366336 (    4.2 MiB) Bytes in page heap freelist\\nMALLOC: +       281984 (    0.3 MiB) Bytes in central cache freelist\\nMALLOC: +       378368 (    0.4 MiB) Bytes in transfer cache freelist\\nMALLOC: +      1030664 (    1.0 MiB) Bytes in thread cache freelists\\nMALLOC: +      2752512 (    2.6 MiB) Bytes in malloc metadata\\nMALLOC:   ------------\\nMALLOC: =     91152384 (   86.9 MiB) Actual memory used (physical + swap)\\nMALLOC: +      7168000 (    6.8 MiB) Bytes released to OS (aka unmapped)\\nMALLOC:   ------------\\nMALLOC: =     98320384 (   93.8 MiB) Virtual address space used\\nMALLOC:\\nMALLOC:            770              Spans in use\\nMALLOC:             32              Thread heaps in use\\nMALLOC:           4096              Tcmalloc page size\\n------------------------------------------------\\nCall ReleaseFreeMemory() to release freelist memory to the OS (via madvise()).\\nBytes released to the OS take up virtual address space but no physical memory.\\n\",\n   \"max_total_thread_cache_bytes\": 980418560,\n   \"pageheap_commit_count\": 1130,\n   \"pageheap_committed_bytes\": 88399872,\n   \"pageheap_decommit_count\": 566,\n   \"pageheap_free_bytes\": 4366336,\n   \"pageheap_reserve_count\": 57,\n   \"pageheap_scavenge_count\": 566,\n   \"pageheap_total_commit_bytes\": 381353984,\n   \"pageheap_total_decommit_bytes\": 292954112,\n   \"pageheap_total_reserve_bytes\": 95567872,\n   \"pageheap_unmapped_bytes\": 7168000,\n   \"release_rate\": 1,\n   \"spinlock_total_delay_ns\": 22796,\n   \"thread_cache_free_bytes\": 1031240,\n   \"total_free_bytes\": 1691592,\n   \"transfer_cache_free_bytes\": 378368\n  }\n },\n \"trafficRecording\": {\n  \"running\": false\n },\n \"transactions\": {\n  \"currentActive\": 0,\n  \"currentInactive\": 0,\n  \"currentOpen\": 0,\n  \"currentPrepared\": 0,\n  \"retriedCommandsCount\": 0,\n  \"retriedStatementsCount\": 0,\n  \"totalAborted\": 0,\n  \"totalCommitted\": 0,\n  \"totalPrepared\": 0,\n  \"totalPreparedThenAborted\": 0,\n  \"totalPreparedThenCommitted\": 0,\n  \"totalStarted\": 0,\n  \"transactionsCollectionWriteCount\": 0\n },\n \"transportSecurity\": {\n  \"1.0\": 0,\n  \"1.1\": 0,\n  \"1.2\": 0,\n  \"1.3\": 0,\n  \"unknown\": 0\n },\n \"twoPhaseCommitCoordinator\": {\n  \"currentInSteps\": {\n   \"deletingCoordinatorDoc\": 0,\n   \"waitingForDecisionAcks\": 0,\n   \"waitingForVotes\": 0,\n   \"writingDecision\": 0,\n   \"writingParticipantList\": 0\n  },\n  \"totalAbortedTwoPhaseCommit\": 0,\n  \"totalCommittedTwoPhaseCommit\": 0,\n  \"totalCreated\": 0,\n  \"totalStartedTwoPhaseCommit\": 0\n },\n \"uptime\": 502142,\n \"uptimeEstimate\": 502142,\n \"uptimeMillis\": 502142433,\n \"version\": \"4.4.1\",\n \"wiredTiger\": {\n  \"async\": {\n   \"current work queue length\": 0,\n   \"maximum work queue length\": 0,\n   \"number of allocation state races\": 0,\n   \"number of flush calls\": 0,\n   \"number of operation slots viewed for allocation\": 0,\n   \"number of times operation allocation failed\": 0,\n   \"number of times worker found no work\": 0,\n   \"total allocations\": 0,\n   \"total compact calls\": 0,\n   \"total insert calls\": 0,\n   \"total remove calls\": 0,\n   \"total search calls\": 0,\n   \"total update calls\": 0\n  },\n  \"block-manager\": {\n   \"blocks pre-loaded\": 10,\n   \"blocks read\": 8371,\n   \"blocks written\": 41728,\n   \"bytes read\": 34390016,\n   \"bytes read via memory map API\": 0,\n   \"bytes read via system call API\": 0,\n   \"bytes written\": 376033280,\n   \"bytes written for checkpoint\": 376033280,\n   \"bytes written via memory map API\": 0,\n   \"bytes written via system call API\": 0,\n   \"mapped blocks read\": 0,\n   \"mapped bytes read\": 0,\n   \"number of times the file was remapped because it changed size via fallocate or truncate\": 0,\n   \"number of times the region was remapped via write\": 0\n  },\n  \"cache\": {\n   \"application threads page read from disk to cache count\": 6,\n   \"application threads page read from disk to cache time (usecs)\": 8567,\n   \"application threads page write from cache to disk count\": 16692,\n   \"application threads page write from cache to disk time (usecs)\": 855489,\n   \"bytes allocated for updates\": 14936,\n   \"bytes belonging to page images in the cache\": 168647,\n   \"bytes belonging to the history store table in the cache\": 173,\n   \"bytes currently in the cache\": 190350,\n   \"bytes dirty in the cache cumulative\": 309133181,\n   \"bytes not belonging to page images in the cache\": 21702,\n   \"bytes read into cache\": 156155,\n   \"bytes written from cache\": 236052223,\n   \"cache overflow score\": 0,\n   \"checkpoint blocked page eviction\": 0,\n   \"eviction calls to get a page\": 43870,\n   \"eviction calls to get a page found queue empty\": 34914,\n   \"eviction calls to get a page found queue empty after locking\": 390,\n   \"eviction currently operating in aggressive mode\": 0,\n   \"eviction empty score\": 0,\n   \"eviction passes of a file\": 0,\n   \"eviction server candidate queue empty when topping up\": 0,\n   \"eviction server candidate queue not empty when topping up\": 0,\n   \"eviction server evicting pages\": 0,\n   \"eviction server slept, because we did not make progress with eviction\": 7984,\n   \"eviction server unable to reach eviction goal\": 0,\n   \"eviction server waiting for a leaf page\": 0,\n   \"eviction state\": 64,\n   \"eviction walk target pages histogram - 0-9\": 0,\n   \"eviction walk target pages histogram - 10-31\": 0,\n   \"eviction walk target pages histogram - 128 and higher\": 0,\n   \"eviction walk target pages histogram - 32-63\": 0,\n   \"eviction walk target pages histogram - 64-128\": 0,\n   \"eviction walk target strategy both clean and dirty pages\": 0,\n   \"eviction walk target strategy only clean pages\": 0,\n   \"eviction walk target strategy only dirty pages\": 0,\n   \"eviction walks abandoned\": 0,\n   \"eviction walks gave up because they restarted their walk twice\": 0,\n   \"eviction walks gave up because they saw too many pages and found no candidates\": 0,\n   \"eviction walks gave up because they saw too many pages and found too few candidates\": 0,\n   \"eviction walks reached end of tree\": 0,\n   \"eviction walks started from root of tree\": 0,\n   \"eviction walks started from saved location in tree\": 0,\n   \"eviction worker thread active\": 4,\n   \"eviction worker thread created\": 0,\n   \"eviction worker thread evicting pages\": 8341,\n   \"eviction worker thread removed\": 0,\n   \"eviction worker thread stable number\": 0,\n   \"files with active eviction walks\": 0,\n   \"files with new eviction walks started\": 0,\n   \"force re-tuning of eviction workers once in a while\": 0,\n   \"forced eviction - history store pages failed to evict while session has history store cursor open\": 0,\n   \"forced eviction - history store pages selected while session has history store cursor open\": 0,\n   \"forced eviction - history store pages successfully evicted while session has history store cursor open\": 0,\n   \"forced eviction - pages evicted that were clean count\": 0,\n   \"forced eviction - pages evicted that were clean time (usecs)\": 0,\n   \"forced eviction - pages evicted that were dirty count\": 0,\n   \"forced eviction - pages evicted that were dirty time (usecs)\": 0,\n   \"forced eviction - pages selected because of too many deleted items count\": 0,\n   \"forced eviction - pages selected count\": 0,\n   \"forced eviction - pages selected unable to be evicted count\": 0,\n   \"forced eviction - pages selected unable to be evicted time\": 0,\n   \"forced eviction - session returned rollback error while force evicting due to being oldest\": 0,\n   \"hazard pointer blocked page eviction\": 0,\n   \"hazard pointer check calls\": 8341,\n   \"hazard pointer check entries walked\": 14,\n   \"hazard pointer maximum array length\": 1,\n   \"history store score\": 0,\n   \"history store table insert calls\": 0,\n   \"history store table insert calls that returned restart\": 0,\n   \"history store table max on-disk size\": 0,\n   \"history store table on-disk size\": 0,\n   \"history store table out-of-order resolved updates that lose their durable timestamp\": 0,\n   \"history store table out-of-order updates that were fixed up by moving existing records\": 0,\n   \"history store table out-of-order updates that were fixed up during insertion\": 0,\n   \"history store table reads\": 0,\n   \"history store table reads missed\": 0,\n   \"history store table reads requiring squashed modifies\": 0,\n   \"history store table truncation by rollback to stable to remove an unstable update\": 0,\n   \"history store table truncation by rollback to stable to remove an update\": 0,\n   \"history store table truncation due to mixed timestamps that returned restart\": 0,\n   \"history store table truncation to remove an update\": 0,\n   \"history store table truncation to remove range of updates due to key being removed from the data page during reconciliation\": 0,\n   \"history store table truncation to remove range of updates due to mixed timestamps\": 0,\n   \"history store table writes requiring squashed modifies\": 0,\n   \"in-memory page passed criteria to be split\": 0,\n   \"in-memory page splits\": 0,\n   \"internal pages evicted\": 0,\n   \"internal pages queued for eviction\": 0,\n   \"internal pages seen by eviction walk\": 0,\n   \"internal pages seen by eviction walk that are already queued\": 0,\n   \"internal pages split during eviction\": 0,\n   \"leaf pages split during eviction\": 0,\n   \"maximum bytes configured\": 3386900480,\n   \"maximum page size at eviction\": 352,\n   \"modified pages evicted\": 8341,\n   \"modified pages evicted by application threads\": 0,\n   \"operations timed out waiting for space in cache\": 0,\n   \"overflow pages read into cache\": 0,\n   \"page split during eviction deepened the tree\": 0,\n   \"page written requiring history store records\": 0,\n   \"pages currently held in the cache\": 19,\n   \"pages evicted by application threads\": 0,\n   \"pages queued for eviction\": 0,\n   \"pages queued for eviction post lru sorting\": 0,\n ","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 542\r\n\r\n{\n  \"name\" : \"24abfaf805c9\",\n  \"cluster_name\" : \"docker-cluster\",\n  \"cluster_uuid\" : \"WXp-kL7TTJuzp4gXkiBIMA\",\n  \"version\" : {\n    \"number\" : \"7.10.1\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"docker\",\n    \"build_hash\" : \"1c34507e66d7db1211f66f3513706fdf548736aa\",\n    \"build_date\" : \"2020-12-05T01:00:33.671820Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"8.7.0\",\n    \"minimum_wire_compatibility_version\" : \"6.8.0\",\n    \"minimum_index_compatibility_version\" : \"6.0.0-beta1\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 308 Permanent Redirect\r\nServer: nginx/1.16.1\r\nDate: Wed, 24 Nov 2021 07:03:36 GMT\r\nContent-Type: text/html\r\nContent-Length: 171\r\nConnection: close\r\nLocation: https://************:443/","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":5,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"MongoDB, Inc","ports":[27017],"title":"MongoDB-数据库"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":8111,"second_cat_tag":"虚拟化","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":5,"company":"其他","ports":[443],"title":"HARBOR"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n/_cat/ml/anomaly_detectors\n/_cat/ml/anomaly_detectors/{job_id}\n/_cat/ml/trained_models\n/_cat/ml/trained_models/{model_id}\n/_cat/ml/datafeeds\n/_cat/ml/datafeeds/{datafeed_id}\n/_cat/ml/data_frame/analytics\n/_cat/ml/data_frame/analytics/{id}\n/_cat/transforms\n/_cat/transforms/{transform_id}\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"8290b126b7a1eda8adf9f9af49e9138e","_score":1,"_source":{"hosts":null,"ip":"************","port":8080,"name":"System Dashboard - BMH-Jira","common_title":"JIRA缺陷跟踪管理系统由于配置不当导致任意用户注册","mac":"00:0c:29:7c:d9:0c","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ATLASSIAN-JIRA","Oracle-JSP","Oracle-JAVA","OpenSSH","Oracle-MySQL","jQuery","Struts2"],"cat_tags":["其他企业应用","脚本语言","其他支撑系统","数据库系统","中间件","开发框架"],"company_tags":["Atlassian","Oracle Corporation","其他","The jQuery Foundation.","Apache Software Foundation."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:8080","vulfile":"Jira_bad_configuration.json","url":"http://************:8080/secure/Signup!default.jspa","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"c37641bb141f831c09ce7c3afa39abd7","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.1 200 \r\nX-AREQUESTID: 861x4817x1\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Security-Policy: frame-ancestors 'self'\r\nX-ASEN: SEN-L17662553\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_4f9f5deecbf9690db021ef4b717b1b53d9101883_lout; Path=/\r\nX-AUSERNAME: anonymous\r\nSet-Cookie: JSESSIONID=7613BEFC2D4481ED80BFE4885A16A496; Path=/; HttpOnly\r\nX-Accel-Buffering: no\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:21:25 GMT\r\nConnection: close","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.34 \r\nJ\\x00\\x00\\x00\n5.7.34\\x00\\x0e\\xaf\\x03\\x00<x.w\u000b?\\x13\\x1e\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00#M\\x1aN(XD\\9KO:\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8090,"banner":"HTTP/1.1 503 \r\nSet-Cookie: JSESSIONID=167829123ECFC204D469471DB44CCD0F; Path=/; HttpOnly\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:42:04 GMT\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":10,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"Atlassian","ports":[8080],"title":"ATLASSIAN-JIRA"},{"belong_level":0,"rule_id":270,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JSP"},{"belong_level":0,"rule_id":22569,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JAVA"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8080,8090],"title":"jQuery"},{"belong_level":0,"rule_id":674,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Apache Software Foundation.","ports":[8080,8090],"title":"Struts2"}],"common_description":"JIRA平台配置不当导致任意用户注册，可通过该方法非法登陆系统，并泄露系统中项目生命周期内的敏感信息。","common_impact":"<p>攻击者可注册任意用户，并登陆系统，自动赋予查看项目库及出现的问题清单权限，并可泄露开发人员信息或项目敏感信息。</p>","recommandation":"<p>修改JIRA配置文件，<span style=\"color: rgb(51, 51, 51); font-size: 16px;\">访问设置权限，禁止任意</span>用户注册功能。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"任意账户操作","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 \r\nTransfer-Encoding: chunked\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Security-Policy: frame-ancestors 'self'\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:44:55 GMT\r\nExpires: Thu, 01 Jan 1970 00:00:00 GMT\r\nPragma: no-cache\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_462b849837d69deaa785f49be85612bda9615b2c_lout; Path=/\r\nSet-Cookie: JSESSIONID=298672013E7819AA3B3AB4DC59659470; Path=/; HttpOnly\r\nVary: User-Agent\r\nX-Arequestid: 884x4822x1\r\nX-Asen: SEN-L17662553\r\nX-Ausername: anonymous\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\n\r\n\n\n\n\n\n\n\n\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    \n\n\n\n\n\n\n\n\n\n<meta charset=\"utf-8\">\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\"/>\n<title>注册 - BMH-Jira</title>\n<meta name=\"application-name\" content=\"JIRA\" data-name=\"jira\" data-version=\"8.0.2\"><meta name=\"ajs-server-scheme\" content=\"http\">\n<meta name=\"ajs-server-port\" content=\"8080\">\n<meta name=\"ajs-server-name\" content=\"************\">\n<meta name=\"ajs-behind-proxy\" content=\"null\">\n<meta name=\"ajs-base-url\" content=\"http://************:8080\">\n<meta name=\"ajs-dev-mode\" content=\"false\">\n<meta name=\"ajs-context-path\" content=\"\">\n<meta name=\"ajs-version-number\" content=\"8.0.2\">\n<meta name=\"ajs-build-number\" content=\"800010\">\n<meta name=\"ajs-is-beta\" content=\"false\">\n<meta name=\"ajs-is-rc\" content=\"false\">\n<meta name=\"ajs-is-snapshot\" content=\"false\">\n<meta name=\"ajs-is-milestone\" content=\"false\">\n<meta name=\"ajs-remote-user\" content=\"\">\n<meta name=\"ajs-remote-user-fullname\" content=\"\">\n<meta name=\"ajs-user-locale\" content=\"zh_CN\">\n<meta name=\"ajs-user-locale-group-separator\" content=\",\">\n<meta name=\"ajs-app-title\" content=\"BMH-Jira\">\n<meta name=\"ajs-keyboard-shortcuts-enabled\" content=\"true\">\n<meta name=\"ajs-keyboard-accesskey-modifier\" content=\"Ctrl+Alt\">\n<meta name=\"ajs-enabled-dark-features\" content=\"[&quot;com.atlassian.jira.agile.darkfeature.editable.detailsview&quot;,&quot;nps.survey.inline.dialog&quot;,&quot;com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled&quot;,&quot;jira.plugin.devstatus.phasetwo&quot;,&quot;jira.frother.reporter.field&quot;,&quot;atlassian.rest.xsrf.legacy.enabled&quot;,&quot;jira.issue.status.lozenge&quot;,&quot;com.atlassian.jira.config.BIG_PIPE&quot;,&quot;com.atlassian.jira.projects.issuenavigator&quot;,&quot;com.atlassian.jira.config.PDL&quot;,&quot;jira.plugin.devstatus.phasetwo.enabled&quot;,&quot;atlassian.aui.raphael.disabled&quot;,&quot;app-switcher.new&quot;,&quot;frother.assignee.field&quot;,&quot;com.atlassian.jira.projects.************************.Switch&quot;,&quot;jira.onboarding.cyoa&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.enabled&quot;,&quot;com.atlassian.jira.config.ProjectConfig.MENU&quot;,&quot;com.atlassian.jira.projects.sidebar.DEFER_RESOURCES&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled&quot;,&quot;com.atlassian.jira.agile.darkfeature.sprint.goal.enabled&quot;,&quot;jira.zdu.admin-updates-ui&quot;,&quot;jira.zdu.jmx-monitoring&quot;,&quot;sd.new.settings.sidebar.location.disabled&quot;,&quot;jira.zdu.cluster-upgrade-state&quot;,&quot;com.atlassian.jira.agile.darkfeature.splitissue&quot;,&quot;com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED&quot;,&quot;jira.export.csv.enabled&quot;]\">\n<meta name=\"ajs-in-admin-mode\" content=\"false\">\n<meta name=\"ajs-is-sysadmin\" content=\"false\">\n<meta name=\"ajs-is-admin\" content=\"false\">\n<meta name=\"ajs-outgoing-mail-enabled\" content=\"true\">\n<meta name=\"ajs-archiving-enabled\" content=\"true\">\n<meta name=\"ajs-date-relativize\" content=\"true\">\n<meta name=\"ajs-date-time\" content=\"h:mm a\">\n<meta name=\"ajs-date-day\" content=\"EEEE h:mm a\">\n<meta name=\"ajs-date-dmy\" content=\"dd/MMM/yy\">\n<meta name=\"ajs-date-complete\" content=\"dd/MMM/yy h:mm a\">\n<script type=\"text/javascript\">var AJS=AJS||{};AJS.debug=true;</script>\n\n\n    \n<meta id=\"atlassian-token\" name=\"atlassian-token\" content=\"BLLI-K16G-EL6P-NY5F_462b849837d69deaa785f49be85612bda9615b2c_lout\">\n\n\n\n<link rel=\"shortcut icon\" href=\"/s/-nco3pw/800010/6411e0087192541a09d88223fb51a6a0/_/images/fav-jsw.png\">\n<link rel=\"search\" type=\"application/opensearchdescription+xml\" href=\"/osd.jsp\" title=\"注册 - BMH-Jira\"/>\n\n    \n\n\n\n<!--[if IE]><![endif]-->\n<script type=\"text/javascript\">\n    (function() {\n        var contextPath = '';\n        var eventBuffer = [];\n\n        function printDeprecatedMsg() {\n            if (console && console.warn) {\n                console.warn('DEPRECATED JS - contextPath global variable has been deprecated since 7.4.0. Use `wrm/context-path` module instead.');\n            }\n        }\n\n        function sendEvent(analytics, postfix) {\n            analytics.send({\n                name: 'js.globals.contextPath.' + postfix\n            });\n        }\n\n        function sendDeprecatedEvent(postfix) {\n            try {\n                var analytics = require('jira/analytics');\n                if (eventBuffer.length) {\n                    eventBuffer.forEach(function(value) {\n                        sendEvent(analytics, value);\n                    });\n                    eventBuffer = [];\n                }\n\n                if (postfix) {\n                    sendEvent(analytics, postfix);\n                }\n            } catch(ex) {\n                eventBuffer.push(postfix);\n                setTimeout(sendDeprecatedEvent, 1000);\n            }\n        }\n\n        Object.defineProperty(window, 'contextPath', {\n            get: function() {\n                printDeprecatedMsg();\n                sendDeprecatedEvent('get');\n                return contextPath;\n            },\n            set: function(value) {\n                printDeprecatedMsg();\n                sendDeprecatedEvent('set');\n                contextPath = value;\n            }\n        });\n    })();\n\n</script>\n<script>\nwindow.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};\nWRM._unparsedData[\"com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path\"]=\"\\\"\\\"\";\nWRM._unparsedData[\"jira.core:feature-flags-data.feature-flag-data\"]=\"{\\\"enabled-feature-keys\\\":[\\\"com.atlassian.jira.agile.darkfeature.editable.detailsview\\\",\\\"nps.survey.inline.dialog\\\",\\\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled\\\",\\\"jira.plugin.devstatus.phasetwo\\\",\\\"jira.frother.reporter.field\\\",\\\"atlassian.rest.xsrf.legacy.enabled\\\",\\\"jira.issue.status.lozenge\\\",\\\"com.atlassian.jira.config.BIG_PIPE\\\",\\\"com.atlassian.jira.projects.issuenavigator\\\",\\\"com.atlassian.jira.config.PDL\\\",\\\"jira.plugin.devstatus.phasetwo.enabled\\\",\\\"atlassian.aui.raphael.disabled\\\",\\\"app-switcher.new\\\",\\\"frother.assignee.field\\\",\\\"com.atlassian.jira.projects.************************.Switch\\\",\\\"jira.onboarding.cyoa\\\",\\\"com.atlassian.jira.agile.darkfeature.kanplan.enabled\\\",\\\"com.atlassian.jira.config.ProjectConfig.MENU\\\",\\\"com.atlassian.jira.projects.sidebar.DEFER_RESOURCES\\\",\\\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled\\\",\\\"com.atlassian.jira.agile.darkfeature.sprint.goal.enabled\\\",\\\"jira.zdu.admin-updates-ui\\\",\\\"jira.zdu.jmx-monitoring\\\",\\\"sd.new.settings.sidebar.location.disabled\\\",\\\"jira.zdu.cluster-upgrade-state\\\",\\\"com.atlassian.jira.agile.darkfeature.splitissue\\\",\\\"com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED\\\",\\\"jira.export.csv.enabled\\\"],\\\"feature-flag-states\\\":{\\\"mail.batching.override.core\\\":true,\\\"jira.spectrum.m1\\\":true,\\\"jira.spectrum.m2\\\":false,\\\"com.atlassian.jira.issuetable.draggable\\\":true,\\\"mail.batching\\\":false,\\\"com.atlassian.jira.agile.darkfeature.kanban.hide.old.done.issues\\\":true,\\\"jira.jql.suggestrecentfields\\\":false,\\\"com.atlassian.jira.agile.darkfeature.backlog.showmore\\\":true,\\\"com.atlassian.jira.agile.darkfeature.optimistic.transitions\\\":true,\\\"com.atlassian.jira.issuetable.move.links.hidden\\\":true,\\\"jira.renderer.consider.variable.format\\\":true,\\\"com.atlassian.jira.agile.darkfeature.kanplan\\\":false,\\\"jira.priorities.per.project.jsd\\\":true,\\\"jira.instrumentation.laas\\\":false,\\\"com.atlassian.jira.agile.darkfeature.rapid.boards.bands\\\":true,\\\"com.atlassian.jira.sharedEntityEditRights\\\":true,\\\"jira.customfields.paginated.ui\\\":true,\\\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint\\\":false,\\\"jira.create.linked.issue\\\":true,\\\"mail.batching.user.notification\\\":true,\\\"jira.spectrum.m1b\\\":true,\\\"com.atlassian.jira.agile.darkfeature.sprint.goal\\\":false,\\\"com.atlassian.jira.agile.darkfeature.dataonpageload\\\":true,\\\"com.atlassian.jira.agile.darkfeature.sidebar.boards.list\\\":true,\\\"jira.sal.host.connect.accessor.existing.transaction.will.create.transactions\\\":true,\\\"com.atlassian.jira.custom.csv.escaper\\\":true,\\\"com.atlassian.jira.plugin.issuenavigator.filtersUxImprovment\\\":true,\\\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions\\\":false,\\\"jira.quick.search\\\":true,\\\"jira.jql.smartautoselectfirst\\\":false,\\\"com.atlassian.jira.projects.per.project.permission.query\\\":true,\\\"com.atlassian.jira.issues.archiving\\\":false,\\\"com.atlassian.jira.projects.archiving\\\":true,\\\"index.use.snappy\\\":true,\\\"jira.priorities.per.project\\\":true,\\\"com.atlassian.jira.upgrade.startup.fix.index\\\":true}}\";\nWRM._unparsedData[\"jira.core:default-comment-security-level-data.DefaultCommentSecurityLevelHelpLink\"]=\"{\\\"extraClasses\\\":\\\"default-comment-level-help\\\",\\\"title\\\":\\\"Commenting on an Issue\\\",\\\"url\\\":\\\"https://docs.atlassian.com/jira/jcore-docs-080/Editing+and+collaborating+on+issues#Editingandcollaboratingonissues-restrictacomment\\\",\\\"isLocal\\\":false}\";\nWRM._unparsedData[\"com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider\"]=\"false\";\nWRM._unparsedData[\"com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider\"]=\"false\";\nWRM._unparsedData[\"jira.core:dateFormatProvider.allFormats\"]=\"{\\\"dateFormats\\\":{\\\"meridiem\\\":[\\\"\\u4e0a\\u5348\\\",\\\"\\u4e0b\\u5348\\\"],\\\"eras\\\":[\\\"\\u51","has_response":1,"createtime":"2021-11-24 15:15:29","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:29"}}
{"_index":"fofaee_threats","_type":"threats","_id":"71d20f92123142531da925cfb088fccd","_score":1,"_source":{"hosts":null,"ip":"************","port":6379,"name":"信创政务产品安全漏洞专业库","common_title":"Redis未授权访问漏洞","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["redis","Linux-操作系统","NGINX","Oracle-MySQL","OpenSSH"],"cat_tags":["数据库系统","操作系统","服务","其他支撑系统"],"company_tags":["Redis Labs","其他","Oracle Corporation"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:6379","vulfile":"redis_unauthorized_access.json","url":"************:6379","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"fe9d5d9c68cb1cb39f4ff809dbd7542e","scan_engine":4,"port_list":[{"protocol":"redis","port":6379,"banner":"-ERR unknown command `help`, with args beginning with: \r\n$3557\r\n# Server\r\nredis_version:6.0.6\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:19d4277f1e8a2fed\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:8.3.0\r\nprocess_id:1\r\nrun_id:1375402a81dc7558ff26b6ea0b4fe37960f9aa74\r\ntcp_port:6379\r\nuptime_in_seconds:450847\r\nuptime_in_days:5\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10346776\r\nexecutable:/data/redis-server\r\nconfig_file:\r\n\r\n# Clients\r\nconnected_clients:3\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\ntracking_clients:0\r\nclients_in_timeout_table:0\r\n\r\n# Memory\r\nused_memory:922328\r\nused_memory_human:900.71K\r\nused_memory_rss:9633792\r\nused_memory_rss_human:9.19M\r\nused_memory_peak:922328\r\nused_memory_peak_human:900.71K\r\nused_memory_peak_perc:100.16%\r\nused_memory_overhead:840044\r\nused_memory_startup:802936\r\nused_memory_dataset:82284\r\nused_memory_dataset_perc:68.92%\r\nallocator_allocated:908648\r\nallocator_active:1249280\r\nallocator_resident:3612672\r\ntotal_system_memory:1927647232\r\ntotal_system_memory_human:1.80G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nused_memory_scripts:0\r\nused_memory_scripts_human:0B\r\nnumber_of_cached_scripts:0\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nallocator_frag_ratio:1.37\r\nallocator_frag_bytes:340632\r\nallocator_rss_ratio:2.89\r\nallocator_rss_bytes:2363392\r\nrss_overhead_ratio:2.67\r\nrss_overhead_bytes:6021120\r\nmem_fragmentation_ratio:11.22\r\nmem_fragmentation_bytes:8774936\r\nmem_not_counted_for_evict:0\r\nmem_replication_backlog:0\r\nmem_clients_slaves:0\r\nmem_clients_normal:33972\r\nmem_aof_buffer:0\r\nmem_allocator:jemalloc-5.1.0\r\nactive_defrag_running:0\r\nlazyfree_pending_objects:0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:0\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637724830\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\nrdb_last_cow_size:2527232\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\naof_last_cow_size:0\r\nmodule_fork_in_progress:0\r\nmodule_fork_last_cow_size:0\r\n\r\n# Stats\r\ntotal_connections_received:248\r\ntotal_commands_processed:828\r\ninstantaneous_ops_per_sec:0\r\ntotal_net_input_bytes:197509\r\ntotal_net_output_bytes:107164\r\ninstantaneous_input_kbps:0.00\r\ninstantaneous_output_kbps:0.00\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:15\r\nexpired_stale_perc:0.00\r\nexpired_time_cap_reached_count:0\r\nexpire_cycle_cpu_milliseconds:27328\r\nevicted_keys:0\r\nkeyspace_hits:446\r\nkeyspace_misses:60\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:1951\r\nmigrate_cached_sockets:0\r\nslave_expires_tracked_keys:0\r\nactive_defrag_hits:0\r\nactive_defrag_misses:0\r\nactive_defrag_key_hits:0\r\nactive_defrag_key_misses:0\r\ntracking_total_keys:0\r\ntracking_total_items:0\r\ntracking_total_prefixes:0\r\nunexpected_error_replies:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_replid:0f7d90a9e73f35cddfebffe620bf631403ed2baa\r\nmaster_replid2:****************************************\r\nmaster_repl_offset:0\r\nsecond_repl_offset:-1\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:436.765806\r\nused_cpu_user:517.455853\r\nused_cpu_sys_children:0.161798\r\nused_cpu_user_children:0.019590\r\n\r\n# Modules\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=33,expires=33,avg_ttl=255354540\r\n\r\n","certs":null},{"protocol":"http","port":9080,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.21.3\r\nDate: Wed, 24 Nov 2021 06:53:46 GMT\r\nContent-Type: text/html\r\nContent-Length: 1436\r\nLast-Modified: Mon, 22 Nov 2021 09:18:54 GMT\r\nConnection: close\r\nETag: \"619b607e-59c\"\r\nCache-Control: no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0\r\nAccept-Ranges: bytes","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.21.3\r\nDate: Wed, 24 Nov 2021 06:55:19 GMT\r\nContent-Type: text/html\r\nContent-Length: 1235\r\nLast-Modified: Fri, 19 Nov 2021 11:21:05 GMT\r\nConnection: close\r\nETag: \"619788a1-4d3\"\r\nCache-Control: no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0\r\nAccept-Ranges: bytes","certs":null},{"protocol":"http","port":8888,"banner":"HTTP/1.0 404 Not Found\r\nAccess-Control-Allow-Credentials: true\r\nAccess-Control-Allow-Headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id\r\nAccess-Control-Allow-Methods: POST, GET, OPTIONS,DELETE,PUT\r\nAccess-Control-Expose-Headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type\r\nContent-Type: text/plain\r\nDate: Wed, 24 Nov 2021 07:08:26 GMT\r\nContent-Length: 18","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.36 \r\nJ\\x00\\x00\\x00\n5.7.36\\x00E\\x01\\x00\\x00th&+('z&\\x00\\xff\\xff\\x08\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x1frU\\x13\\x05}]XNeF\\x1f\\x00mysql_native_password\\x00","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[8080,9080],"title":"NGINX"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$3556\r\n# Server\r\nredis_version:6.0.6\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:19d4277f1e8a2fed\r\nredis_mode:standalone\r\nos:Linux 3.10.0-957.el7.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:8.3.0\r\nprocess_id:1\r\nrun_id:1375402a81dc7558ff26b6ea0b4fe37960f9aa74\r\ntcp_port:6379\r\nuptime_in_seconds:452291\r\nuptime_in_days:5\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:10348219\r\nexecutable:/data/redis-server\r\nconfig_file:\r\n\r\n# Clients\r\nconnected_clients:2\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\ntracking_clients:0\r\nclients_in_timeout_table:0\r\n\r\n# Memory\r\nused_memory:901368\r\nused_memory_human:880.24K\r\nused_memory_rss:9568256\r\nused_memory_rss_human:9.12M\r\nused_memory_peak:922328\r\nused_memory_peak_human:900.71K\r\nused_memory_peak_perc:97.73%\r\nused_memory_overhead:823058\r\nused_memory_startup:802936\r\nused_memory_dataset:78310\r\nused_memory_dataset_perc:79.56%\r\nallocator_allocated:929648\r\nallocator_active:1273856\r\nallocat","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"4ad6e179e03c59b1efa089c90a49a06a","_score":1,"_source":{"hosts":null,"ip":"************","port":8080,"name":"System Dashboard - BMH-Jira","common_title":"Jira问题跟踪管理软件未授权SSRF漏洞","mac":"00:0c:29:7c:d9:0c","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["ATLASSIAN-JIRA","Oracle-JSP","Oracle-JAVA","OpenSSH","Oracle-MySQL","jQuery","Struts2"],"cat_tags":["其他企业应用","脚本语言","其他支撑系统","数据库系统","中间件","开发框架"],"company_tags":["Atlassian","Oracle Corporation","其他","The jQuery Foundation.","Apache Software Foundation."],"task_ids":[0,2],"state":1,"level":0,"hostinfo":"http://************:8080","vulfile":"Jira_makeRequest_url_CVE_2019_8451_SSRF.json","url":"http://************:8080/plugins/servlet/gadgets/makeRequest?url=http://************:<EMAIL>","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"fc2938afc47655c611eab5e9b8d8c21a","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.1 200 \r\nX-AREQUESTID: 861x4817x1\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Security-Policy: frame-ancestors 'self'\r\nX-ASEN: SEN-L17662553\r\nSet-Cookie: atlassian.xsrf.token=BLLI-K16G-EL6P-NY5F_4f9f5deecbf9690db021ef4b717b1b53d9101883_lout; Path=/\r\nX-AUSERNAME: anonymous\r\nSet-Cookie: JSESSIONID=7613BEFC2D4481ED80BFE4885A16A496; Path=/; HttpOnly\r\nX-Accel-Buffering: no\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:21:25 GMT\r\nConnection: close","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.7.34 \r\nJ\\x00\\x00\\x00\n5.7.34\\x00\\x0e\\xaf\\x03\\x00<x.w\u000b?\\x13\\x1e\\x00\\xff\\xff-\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00#M\\x1aN(XD\\9KO:\\x00mysql_native_password\\x00","certs":null},{"protocol":"http","port":8090,"banner":"HTTP/1.1 503 \r\nSet-Cookie: JSESSIONID=167829123ECFC204D469471DB44CCD0F; Path=/; HttpOnly\r\nContent-Type: text/html;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:42:04 GMT\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":10,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":5,"company":"Atlassian","ports":[8080],"title":"ATLASSIAN-JIRA"},{"belong_level":0,"rule_id":270,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JSP"},{"belong_level":0,"rule_id":22569,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Oracle Corporation","ports":[8080,8090],"title":"Oracle-JAVA"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[8080,8090],"title":"jQuery"},{"belong_level":0,"rule_id":674,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Apache Software Foundation.","ports":[8080,8090],"title":"Struts2"}],"common_description":"Atlassian Jira是澳大利亚Atlassian公司的一套缺陷跟踪管理系统。该系统主要用于对工作中各类问题、缺陷进行跟踪管理。Jira 8.4.0版本之前的/plugins/servlet/gadgets/makeRequest允许远程攻击者通过一个服务器端请求伪造(SSRF)漏洞访问内部网络资源的内容，该漏洞是由JiraWhitelist类中的一个逻辑错误引起的。攻击者通过构造特殊的请求包，无需登录即可利用该漏洞。","common_impact":"<p>远程攻击者可利用该漏洞对部署在内网的应用进行SSRF攻击，获取部署在内网应用中的敏感信息，访问内部网络资源，探测内网端口等信息。<br></p>","recommandation":"<p style=\"\">官方已修复该漏洞，请用户升级至8.4.0或其以后版本：<a href=\"https://www.atlassian.com/zh/software/jira/download\" target=\"_blank\">https://www.atlassian.com/zh/software/jira/download</a><br></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2019-8451","vulType":"SSRF漏洞","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 \r\nConnection: close\r\nCache-Control: public,max-age=3600\r\nContent-Disposition: attachment;filename=p.txt\r\nContent-Security-Policy: frame-ancestors 'self'\r\nContent-Type: application/json;charset=UTF-8\r\nDate: Wed, 24 Nov 2021 06:44:57 GMT\r\nExpires: Wed, 24 Nov 2021 07:44:57 GMT\r\nVary: User-Agent\r\nX-Arequestid: 884x4824x1\r\nX-Asen: SEN-L17662553\r\nX-Asessionid: p9soyf\r\nX-Ausername: anonymous\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\n\r\nthrow 1; < don't be evil' >{\"http://************:<EMAIL>\":{\"rc\":200,\"headers\":{},\"body\":\"<!DOCTYPE html>\\r\\n<!--STATUS OK--><html> <head><meta http-equiv=content-type content=text/html;charset=utf-8><meta http-equiv=X-UA-Compatible content=IE=Edge><meta content=always name=referrer><link rel=stylesheet type=text/css href=http://s1.bdstatic.com/r/www/cache/bdorz/baidu.min.css><title>百度一下，你就知道<\\/title><\\/head> <body link=#0000cc> <div id=wrapper> <div id=head> <div class=head_wrapper> <div class=s_form> <div class=s_form_wrapper> <div id=lg> <img hidefocus=true src=//www.baidu.com/img/bd_logo1.png width=270 height=129> <\\/div> <form id=form name=f action=//www.baidu.com/s class=fm> <input type=hidden name=bdorz_come value=1> <input type=hidden name=ie value=utf-8> <input type=hidden name=f value=8> <input type=hidden name=rsv_bp value=1> <input type=hidden name=rsv_idx value=1> <input type=hidden name=tn value=baidu><span class=\\\"bg s_ipt_wr\\\"><input id=kw name=wd class=s_ipt value maxlength=255 autocomplete=off autofocus><\\/span><span class=\\\"bg s_btn_wr\\\"><input type=submit id=su value=百度一下 class=\\\"bg s_btn\\\"><\\/span> <\\/form> <\\/div> <\\/div> <div id=u1> <a href=http://news.baidu.com name=tj_trnews class=mnav>新闻<\\/a> <a href=http://www.hao123.com name=tj_trhao123 class=mnav>hao123<\\/a> <a href=http://map.baidu.com name=tj_trmap class=mnav>地图<\\/a> <a href=http://v.baidu.com name=tj_trvideo class=mnav>视频<\\/a> <a href=http://tieba.baidu.com name=tj_trtieba class=mnav>贴吧<\\/a> <noscript> <a href=http://www.baidu.com/bdorz/login.gif?login&amp;tpl=mn&amp;u=http%3A%2F%2Fwww.baidu.com%2f%3fbdorz_come%3d1 name=tj_login class=lb>登录<\\/a> <\\/noscript> <script>document.write('<a href=\\\"http://www.baidu.com/bdorz/login.gif?login&tpl=mn&u='+ encodeURIComponent(window.location.href+ (window.location.search === \\\"\\\" ? \\\"?\\\" : \\\"&\\\")+ \\\"bdorz_come=1\\\")+ '\\\" name=\\\"tj_login\\\" class=\\\"lb\\\">登录<\\/a>');<\\/script> <a href=//www.baidu.com/more/ name=tj_briicon class=bri style=\\\"display: block;\\\">更多产品<\\/a> <\\/div> <\\/div> <\\/div> <div id=ftCon> <div id=ftConw> <p id=lh> <a href=http://home.baidu.com>关于百度<\\/a> <a href=http://ir.baidu.com>About Baidu<\\/a> <\\/p> <p id=cp>&copy;2017&nbsp;Baidu&nbsp;<a href=http://www.baidu.com/duty/>使用百度前必读<\\/a>&nbsp; <a href=http://jianyi.baidu.com/ class=cp-feedback>意见反馈<\\/a>&nbsp;京ICP证030173号&nbsp; <img src=//www.baidu.com/img/gs.gif> <\\/p> <\\/div> <\\/div> <\\/div> <\\/body> <\\/html>\\r\\n\"}}","has_response":1,"createtime":"2021-11-24 15:15:31","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:31"}}
{"_index":"fofaee_threats","_type":"threats","_id":"02ec03dd29f1e329c9ae58b84eef0cff","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"网络资产测绘及风险分析系统","common_title":"Elasticsearch未授权访问","mac":"9c:69:b4:60:ad:2e","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["elastic-Elasticsearch","OpenSSH","Oracle-MySQL","NGINX","jQuery","Ruby","Layui","华顺信安-FOEYE","vmware-Spring-Framework"],"cat_tags":["数据库系统","其他支撑系统","服务","中间件","脚本语言","组件","威胁分析与管理","开发框架"],"company_tags":["Elastic, Inc.","其他","Oracle Corporation","The jQuery Foundation.","北京华顺信安科技有限公司","Pivotal Software, Inc."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"37263e76613c6d4ac3a1dce74e99c6ab","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"wUc51H7\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"zTFoPVf2QiK6JzXDUR-7nA\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":8000,"banner":"HTTP/1.0 200 OK\r\nAccess-Control-Allow-Credentials: true\r\nAccess-Control-Allow-Headers: Content-Type,Content-Length,Accept,Accept-Encoding,Accept-Language,Referer,Connection,X-Access-Token,Authorization,Origin,Cache-Control,X-Requested-With,X-Check-Result,Content-Disposition,Host\r\nAccess-Control-Allow-Methods: GET,HEAD,POST,PUT,PATCH,DELETE,CONNECT,OPTIONS,TRACE\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Expose-Headers: Content-Type,Content-Length,Accept,Accept-Encoding,Accept-Language,Referer,Connection,X-Access-Token,Authorization,Origin,Cache-Control,X-Requested-With,X-Check-Result,Content-Disposition,Host\r\nContent-Type: application/json; charset=utf-8\r\nDate: Wed, 24 Nov 2021 06:51:00 GMT\r\nContent-Length: 88","certs":null},{"protocol":"http","port":8081,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.21.3\r\nDate: Wed, 24 Nov 2021 06:56:06 GMT\r\nContent-Type: text/html; charset=UTF-8\r\nContent-Length: 1495\r\nLast-Modified: Tue, 23 Nov 2021 02:22:40 GMT\r\nConnection: close\r\nETag: \"619c5070-5d7\"\r\nX-Frame-Options: SAMEORIGIN\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Headers: X-Request-With\r\nAccess-Control-Allow-Methods: GET,POST,OPTIONS\r\nAccept-Ranges: bytes","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.68-MariaDB \r\nR\\x00\\x00\\x00\n5.5.68-MariaDB\\x00\\x9ae\\x00\\x003T/-2U7P\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00kKr~Ds:fq'jc\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_x.x\r\n","certs":null},{"protocol":"java-rmi","port":1099,"banner":"N\\x00\f*.*.*.*\\x00\\x00\\xe0F","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:06:36 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nLocation: https://************/auth/login\r\nCache-Control: no-cache\r\nSet-Cookie: _fofa_EE_session=5838b9d453d98c0ac22c08d8c9edbc11; path=/;httponly;secure;; HttpOnly\r\nX-Request-Id: 9c3310ec-34e1-4085-bf92-b450296e3790\r\nX-Runtime: 0.038351","certs":{"not_after":"2031-10-21 12:49:02","subject_cn":"127.0.0.0","issuer_cn":"127.0.0.0","subject_org":["HSXA"],"issuer_org":["HSXA"],"not_before":"2021-10-23 12:49:02","v":"v1","sn":"26874870686569552840301774941171889746792051773","sig_alth":"SHA256-RSA"}},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:09:13 GMT\r\nContent-Type: text/html\r\nContent-Length: 178\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"http","port":8080,"banner":"HTTP/1.1 404 \r\nContent-Type: application/json\r\nDate: Wed, 24 Nov 2021 07:12:00 GMT\r\nConnection: close","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443,8081],"title":"NGINX"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443],"title":"jQuery"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":20777,"second_cat_tag":"组件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Layui"},{"belong_level":0,"rule_id":532625,"second_cat_tag":"威胁分析与管理","soft_hard_code":1,"first_cat_tag":"安全产品","level_code":1,"company":"北京华顺信安科技有限公司","ports":[443],"title":"华顺信安-FOEYE"},{"belong_level":0,"rule_id":8772,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Pivotal Software, Inc.","ports":[8080],"title":"vmware-Spring-Framework"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"f691033d8bbdc5e1f019bf97c9b9d093","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"Laravel","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:40:aa:4e","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","OpenSSH","NGINX","elastic-Elasticsearch","php","Laravel-Framework"],"cat_tags":["数据库系统","其他支撑系统","服务","脚本语言","开发框架"],"company_tags":["Oracle Corporation","其他","Elastic, Inc.","PHP Group"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"e59d6f0c3f83f9b46da8fe5b63dcd434","scan_engine":4,"port_list":[{"protocol":"http","port":8080,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx/1.10.2\r\nDate: Wed, 24 Nov 2021 06:59:50 GMT\r\nContent-Type: text/html\r\nContent-Length: 185\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"DVFyuSF\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"qjZHMCmKR4u10P6AKbolEA\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.10.2\r\nContent-Type: text/html; charset=UTF-8\r\nConnection: close\r\nX-Powered-By: PHP/7.2.31\r\nCache-Control: private, must-revalidate\r\nDate: Wed, 24 Nov 2021 07:02:05 GMT\r\npragma: no-cache\r\nexpires: -1\r\nSet-Cookie: XSRF-TOKEN=eyJpdiI6InNiSnlHUjBMbWtcL1dqV0lwbW9yWmdRPT0iLCJ2YWx1ZSI6IkllU2VpSVM4MmNqWWdYbnMrbE5sZU1KaExpWWFzSFwvOUdMUkJWN2M3N1hmRVB4ak96MHBadExvVVUrRUN3N0ZHIiwibWFjIjoiNWQ5YTU0MzFiNmE1OWYwNTNhYjk2NjJjNTIyYWQzNTcwNWUwZjE2MmMzMmRkOWZjMDQzZTU4MzkxMWEwYjEzMiJ9; expires=Wed, 24-Nov-2021 09:02:05 GMT; Max-Age=7200; path=/\r\nSet-Cookie: laravel_session=eyJpdiI6IjJKUzNkbDN6ZCt3eFhnaHErZ1JwMXc9PSIsInZhbHVlIjoiQnJsazJHRVIyenRTWlRkVXd6aThYMVE0OFwvTTNNN3FBaDQ2UlwvWjJHRWJzeElPbEpCUEowS0ZYT0YwZktsSHZPIiwibWFjIjoiNmY3YzY2YWFhZGU0NTliZGQ3ZmFmYWMxODQ4NjhlNjE0OWY1MTAyZDYxODI4OWU4MzBiZTMxMWNhYmVmODIyOCJ9; expires=Wed, 24-Nov-2021 09:02:05 GMT; Max-Age=7200; path=/; httponly","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.10.2\r\nDate: Wed, 24 Nov 2021 07:03:08 GMT\r\nContent-Type: text/html\r\nContent-Length: 4984\r\nLast-Modified: Tue, 07 Sep 2021 09:45:51 GMT\r\nConnection: close\r\nETag: \"613734cf-1378\"\r\nAccept-Ranges: bytes","certs":{"not_after":"2029-03-01 10:39:13","subject_cn":"dcc.hsxa.net","issuer_cn":"dcc.hsxa.net","subject_org":["hsxa"],"issuer_org":["hsxa"],"not_before":"2019-03-04 10:39:13","v":"v1","sn":"9288330925461333812","sig_alth":"SHA256-RSA"}},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_6.6.1\r\n","certs":null},{"protocol":"mysql","port":3306,"banner":"G\\x00\\x00\\x00\\xffj\\x04Host '************' is not allowed to connect to this MariaDB server","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[8080,443,80],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":266,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"PHP Group","ports":[80],"title":"php"},{"belong_level":0,"rule_id":6156,"second_cat_tag":"开发框架","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[80],"title":"Laravel-Framework"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"d4ecd6fe94dbc4935bcda0727098bcf2","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"网络资产安全管理平台","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:1d:e8:f4","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Oracle-MySQL","NGINX","elastic-Elasticsearch","OpenSSH","jQuery","Ruby","Layui","华顺信安-FOEYE"],"cat_tags":["数据库系统","服务","其他支撑系统","中间件","脚本语言","组件","威胁分析与管理"],"company_tags":["Oracle Corporation","其他","Elastic, Inc.","The jQuery Foundation.","北京华顺信安科技有限公司"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"4df2c630a6e84dbf631ef580c8f7d0fe","scan_engine":4,"port_list":[{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.64-MariaDB \r\nR\\x00\\x00\\x00\n5.5.64-MariaDB\\x00(\\x15\\x00\\x00f!p2FI|X\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00~727\\H4kACif\\x00mysql_native_password\\x00","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_x.x\r\n","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"dDOwQWW\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"iDMQrDzoQf6O6LdgaFBAVQ\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 06:56:57 GMT\r\nContent-Type: text/html\r\nContent-Length: 178\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:11:52 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nLocation: https://************/auth/login\r\nCache-Control: no-cache\r\nSet-Cookie: _fofa_EE_session=95bc95fb4dacd8b340e6bd4e46779a36; path=/;httponly;secure;; HttpOnly\r\nX-Request-Id: bce70c85-78d8-4031-980e-2ed60f044785\r\nX-Runtime: 0.092150","certs":{"not_after":"2030-06-03 06:47:03","subject_cn":"10.10.10.167","issuer_cn":"10.10.10.167","subject_org":["HSXA"],"issuer_org":["HSXA"],"not_before":"2020-06-05 06:47:03","v":"v1","sn":"13545525945524935683","sig_alth":"SHA256-RSA"}}],"rule_infos":[{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443],"title":"jQuery"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":20777,"second_cat_tag":"组件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Layui"},{"belong_level":0,"rule_id":532625,"second_cat_tag":"威胁分析与管理","soft_hard_code":1,"first_cat_tag":"安全产品","level_code":1,"company":"北京华顺信安科技有限公司","ports":[443],"title":"华顺信安-FOEYE"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"a3e947aad7b95908a548db635dcf84fb","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":"资产测绘系统","common_title":"Elasticsearch未授权访问","mac":"9c:69:b4:60:ae:e6","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","OpenSSH","elastic-Elasticsearch","jQuery","Ruby","Layui"],"cat_tags":["服务","其他支撑系统","数据库系统","中间件","脚本语言","组件"],"company_tags":["其他","Elastic, Inc.","The jQuery Foundation."],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"0a020d1712b97fab7d6e9654f262ca33","scan_engine":4,"port_list":[{"protocol":"http","port":12345,"banner":"HTTP/1.0 307 Temporary Redirect\r\nContent-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; object-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self'; media-src 'self'; frame-src 'self'; font-src 'self' data:; connect-src 'self'\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /cland/\r\nX-Content-Type-Options: nosniff\r\nX-Frame-Options: SAMEORIGIN\r\nX-Xss-Protection: 1; mode=block\r\nDate: Wed, 24 Nov 2021 06:53:56 GMT\r\nContent-Length: 43","certs":null},{"protocol":"openvpn-udp","port":1194,"banner":"@\\x9f\\xf4\\x1c\\x8d\\xf8D\\x9d\\x14\\x00\\x00\\x00\\x00\\x00","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 302 Found\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:03:26 GMT\r\nContent-Type: text/html; charset=utf-8\r\nConnection: close\r\nX-Frame-Options: SAMEORIGIN\r\nX-XSS-Protection: 1; mode=block\r\nX-Content-Type-Options: nosniff\r\nLocation: https://************/auth/login\r\nCache-Control: no-cache\r\nSet-Cookie: _fofa_EE_session=5ea8e85162fccb7c4020781519fed402; path=/; HttpOnly\r\nX-Request-Id: 37cab57c-8dee-4424-ba05-f96000b84ae8\r\nX-Runtime: 0.033314","certs":{"not_after":"2031-10-28 03:15:21","subject_cn":"************","issuer_cn":"************","subject_org":["HSXA"],"issuer_org":["HSXA"],"not_before":"2021-10-30 03:15:21","v":"v1","sn":"11136842714992772787","sig_alth":"SHA256-RSA"}},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 301 Moved Permanently\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:12:19 GMT\r\nContent-Type: text/html\r\nContent-Length: 178\r\nConnection: close\r\nLocation: https://************/","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 435\r\n\r\n{\n  \"name\" : \"izaNjxR\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"JB8wqqmrQkCWZ5xDV9kMAg\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[443,80],"title":"NGINX"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":138,"second_cat_tag":"中间件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"The jQuery Foundation.","ports":[443],"title":"jQuery"},{"belong_level":0,"rule_id":269,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Ruby"},{"belong_level":0,"rule_id":20777,"second_cat_tag":"组件","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"其他","ports":[443],"title":"Layui"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"5bb10f8c14b56dd1427eeb1e439c5020","_score":1,"_source":{"hosts":null,"ip":"***********","port":9200,"name":"网络空间测绘，网络空间安全搜索引擎，网络空间搜索引擎，安全态势感知 - FOFA网络空间测绘系统","common_title":"Elasticsearch未授权访问","mac":"00:0c:29:b9:22:d9","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["OpenResty-产品","OpenSSH","elastic-Elasticsearch","Google-站长平台"],"cat_tags":["服务","其他支撑系统","数据库系统","其他企业应用"],"company_tags":["OpenResty Inc.","其他","Elastic, Inc.","Google LLC"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://***********:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://***********:9200/_cat","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"13e1cd9ec2f62260c37338d661072d34","scan_engine":4,"port_list":[{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: openresty/********\r\nDate: Wed, 24 Nov 2021 06:25:15 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 259680\r\nConnection: close\r\nSet-Cookie: befor_router=; Max-Age=43200; Path=/\r\nETag: \"3f660-oa+UCEeARwtHi5vAtUYNrugYtck\"\r\nAccept-Ranges: none\r\nVary: Accept-Encoding\r\nExpires: Thu, 01 Jan 1970 00:00:01 GMT\r\nCache-Control: no-cache","certs":null},{"protocol":"http","port":8000,"banner":"HTTP/1.1 200 OK\r\nServer: openresty/********\r\nDate: Wed, 24 Nov 2021 06:27:38 GMT\r\nContent-Type: application/octet-stream\r\nContent-Length: 4\r\nConnection: close","certs":null},{"protocol":"http","port":5000,"banner":"HTTP/1.0 503 Service Unavailable\r\nCache-Control: no-cache\r\nConnection: close\r\nContent-Type: text/html","certs":null},{"protocol":"unknown","port":9100,"banner":"","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"http","port":8081,"banner":"HTTP/1.0 200 OK\r\nAccess-Control-Allow-Credentials: true\r\nAccess-Control-Allow-Headers: *\r\nAccess-Control-Allow-Methods: GET, OPTIONS, POST, PUT, DELETE\r\nAccess-Control-Allow-Origin: *\r\nContent-Type: application/json; charset=utf-8\r\nFofa-Status-Code: 0\r\nDate: Wed, 24 Nov 2021 06:38:07 GMT\r\nContent-Length: 68","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"http","port":8001,"banner":"HTTP/1.1 200 OK\r\nServer: openresty/********\r\nDate: Wed, 24 Nov 2021 06:42:13 GMT\r\nContent-Type: application/octet-stream\r\nContent-Length: 4\r\nConnection: close","certs":null},{"protocol":"http","port":81,"banner":"HTTP/1.1 200 OK\r\nServer: openresty/********\r\nDate: Wed, 24 Nov 2021 06:42:13 GMT\r\nContent-Type: application/json; charset=utf-8\r\nContent-Length: 68\r\nConnection: close\r\nAccess-Control-Allow-Credentials: true\r\nAccess-Control-Allow-Headers: *\r\nAccess-Control-Allow-Methods: GET, OPTIONS, POST, PUT, DELETE\r\nAccess-Control-Allow-Origin: *\r\nFofa-Status-Code: 0","certs":null},{"protocol":"http","port":3000,"banner":"HTTP/1.1 200 OK\r\nSet-Cookie: befor_router=; Max-Age=43200; Path=/\r\nETag: \"3f660-i6A2rYPW825oU30Wb5pX4kcMuYg\"\r\nContent-Type: text/html; charset=utf-8\r\nAccept-Ranges: none\r\nContent-Length: 259680\r\nVary: Accept-Encoding\r\nDate: Wed, 24 Nov 2021 06:42:40 GMT\r\nConnection: close","certs":null},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 436\r\n\r\n{\n  \"name\" : \"node-3-1\",\n  \"cluster_name\" : \"fofa-es6-test\",\n  \"cluster_uuid\" : \"TQ2ur0ouTAS7gxqu_wM-Yw\",\n  \"version\" : {\n    \"number\" : \"6.2.4\",\n    \"build_hash\" : \"ccec39f\",\n    \"build_date\" : \"2018-04-12T20:37:28.497551Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.2.1\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":217,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"OpenResty Inc.","ports":[8000,81,8001,80],"title":"OpenResty-产品"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":151,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":4,"company":"Google LLC","ports":[80,3000],"title":"Google-站长平台"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:05","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:05"}}
{"_index":"fofaee_threats","_type":"threats","_id":"c398027530af31acce1de2f08eaec08d","_score":1,"_source":{"hosts":null,"ip":"************","port":9200,"name":null,"common_title":"Elasticsearch未授权访问","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["elastic-Elasticsearch","OpenSSH"],"cat_tags":["数据库系统","其他支撑系统"],"company_tags":["Elastic, Inc.","其他"],"task_ids":[0,2],"state":1,"level":1,"hostinfo":"http://************:9200","vulfile":"Elasticsearch_Unauthorized.json","url":"http://************:9200/_cat","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"e85dc13fd3083e7a79a899f9c9f64f46","scan_engine":4,"port_list":[{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 487\r\n\r\n{\n  \"name\" : \"node-1\",\n  \"cluster_name\" : \"fofa-es6\",\n  \"cluster_uuid\" : \"6VrCPVCvTSKxJR8MsVMYLw\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Elasticsearch是一款java编写的企业级搜索服务。越来越多的公司使用ELK作为日志分析，启动此服务默认会开放9200端口，可被非法操作数据。","common_impact":"<p>该漏洞导致，攻击者可以拥有Elasticsearch的所有权限。可以对数据进行任意操作。业务系统将面临敏感数据泄露、数据丢失、数据遭到破坏甚至遭到攻击者的勒索。<br></p>","recommandation":"<p>1、防火墙上设置禁止外网访问elasticsearch端口。&nbsp;</p><p>2、使用Nginx搭建反向代理，通过配置Nginx实现对Elasticsearch的认证&nbsp;</p><p>3、限制IP访问，绑定固定IP&nbsp;</p><p>4、在<code>config/elasticsearch.yml</code>中为9200端口（elasticsearch端口）设置认证：&nbsp;</p><p><code>http.basic.enabled true #开关，开启会接管全部HTTP连接</code></p><p><code>http.basic.user \"admin\" #账号</code></p><p><code>http.basic.password \"admin_pw\" #密码</code></p><p><code>http.basic.ipwhitelist [\"localhost\", \"127.0.0.1\"]&nbsp;</code></p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"未授权访问","has_exp":0,"is_ipv6":false,"last_response":"HTTP/1.1 200 OK\r\nContent-Type: text/plain; charset=UTF-8\r\n\r\n=^.^=\n/_cat/allocation\n/_cat/shards\n/_cat/shards/{index}\n/_cat/master\n/_cat/nodes\n/_cat/tasks\n/_cat/indices\n/_cat/indices/{index}\n/_cat/segments\n/_cat/segments/{index}\n/_cat/count\n/_cat/count/{index}\n/_cat/recovery\n/_cat/recovery/{index}\n/_cat/health\n/_cat/pending_tasks\n/_cat/aliases\n/_cat/aliases/{alias}\n/_cat/thread_pool\n/_cat/thread_pool/{thread_pools}\n/_cat/plugins\n/_cat/fielddata\n/_cat/fielddata/{fields}\n/_cat/nodeattrs\n/_cat/repositories\n/_cat/snapshots/{repository}\n/_cat/templates\n","has_response":1,"createtime":"2021-11-24 15:15:04","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:15:04"}}
{"_index":"fofaee_threats","_type":"threats","_id":"f889c0e77e82dab364e573233d6bc2d6","_score":1,"_source":{"hosts":null,"ip":"***********","port":6379,"name":null,"common_title":"Redis未授权访问漏洞","mac":"00:0c:29:e8:2c:a2","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["NGINX","Oracle-MySQL","redis","Linux-操作系统","elastic-Elasticsearch","OpenSSH"],"cat_tags":["服务","数据库系统","操作系统","其他支撑系统"],"company_tags":["其他","Oracle Corporation","Redis Labs","Elastic, Inc."],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://***********:6379","vulfile":"redis_unauthorized_access.json","url":"***********:6379","obj_type":1,"object":"***********","intranet_ip":1,"addition":null,"merge_md5":"77120ff1af95b790e2acda39b630e2ab","scan_engine":4,"port_list":[{"protocol":"http","port":8000,"banner":"HTTP/1.1 404 Not Found\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:49:06 GMT\r\nContent-Type: text/html\r\nContent-Length: 169\r\nConnection: close","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 06:50:01 GMT\r\nContent-Type: text/html\r\nContent-Length: 6354\r\nLast-Modified: Wed, 24 Nov 2021 03:52:16 GMT\r\nConnection: close\r\nETag: \"619db6f0-18d2\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":null},{"protocol":"mysql","port":3306,"banner":"Mysql Version: 5.5.68-MariaDB \r\nR\\x00\\x00\\x00\n5.5.68-MariaDB\\x00\\x84I\\x00\\x00ggP:n?/m\\x00\\xff\\xf7\\x08\\x02\\x00\\x0f\\xa0\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00XOc%*F^+VS};\\x00mysql_native_password\\x00","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"https","port":443,"banner":"HTTP/1.1 200 OK\r\nServer: nginx/1.12.2\r\nDate: Wed, 24 Nov 2021 07:08:28 GMT\r\nContent-Type: text/html\r\nContent-Length: 6354\r\nLast-Modified: Wed, 24 Nov 2021 03:52:16 GMT\r\nConnection: close\r\nETag: \"619db6f0-18d2\"\r\nAccess-Control-Allow-Origin: *\r\nAccess-Control-Allow-Methods: GET, POST, OPTIONS\r\nAccess-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization\r\nAccept-Ranges: bytes","certs":{"not_after":"2020-06-15 23:59:59","subject_cn":"focii.cn","issuer_cn":"Sectigo RSA Domain Validation Secure Server CA","issuer_org":["Sectigo Limited"],"not_before":"2019-05-13 00:00:00","v":"v3","domain":"focii.cn","sn":"182191660422272240828351337638536332243","sig_alth":"SHA256-RSA"}},{"protocol":"elastic","port":9200,"banner":"HTTP/1.0 200 OK\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 493\r\n\r\n{\n  \"name\" : \"NryzzqS\",\n  \"cluster_name\" : \"elasticsearch\",\n  \"cluster_uuid\" : \"rLkxJSu6RZ6DOAjsIiRwwA\",\n  \"version\" : {\n    \"number\" : \"6.4.3\",\n    \"build_flavor\" : \"default\",\n    \"build_type\" : \"rpm\",\n    \"build_hash\" : \"fe40335\",\n    \"build_date\" : \"2018-10-30T23:17:19.084789Z\",\n    \"build_snapshot\" : false,\n    \"lucene_version\" : \"7.4.0\",\n    \"minimum_wire_compatibility_version\" : \"5.6.0\",\n    \"minimum_index_compatibility_version\" : \"5.0.0\"\n  },\n  \"tagline\" : \"You Know, for Search\"\n}\n","certs":null},{"protocol":"redis","port":6379,"banner":"-ERR unknown command 'help'\r\n$2210\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 5.9.11-1.el7.elrepo.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6977\r\nrun_id:82c55bba1b758f46c7a65fd1cf939dcb619f8015\r\ntcp_port:6379\r\nuptime_in_seconds:504581\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348084\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:15\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:6\r\n\r\n# Memory\r\nused_memory:1178432\r\nused_memory_human:1.12M\r\nused_memory_rss:7319552\r\nused_memory_rss_human:6.98M\r\nused_memory_peak:1579280\r\nused_memory_peak_human:1.51M\r\ntotal_system_memory:8350412800\r\ntotal_system_memory_human:7.78G\r\nused_memory_lua:41984\r\nused_memory_lua_human:41.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:6.21\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last_save:37\r\nrdb_bgsave_in_progress:0\r\nrdb_last_save_time:1637737785\r\nrdb_last_bgsave_status:ok\r\nrdb_last_bgsave_time_sec:0\r\nrdb_current_bgsave_time_sec:-1\r\naof_enabled:0\r\naof_rewrite_in_progress:0\r\naof_rewrite_scheduled:0\r\naof_last_rewrite_time_sec:-1\r\naof_current_rewrite_time_sec:-1\r\naof_last_bgrewrite_status:ok\r\naof_last_write_status:ok\r\n\r\n# Stats\r\ntotal_connections_received:46850\r\ntotal_commands_processed:7156762\r\ninstantaneous_ops_per_sec:19\r\ntotal_net_input_bytes:444713744\r\ntotal_net_output_bytes:42747430\r\ninstantaneous_input_kbps:1.18\r\ninstantaneous_output_kbps:0.11\r\nrejected_connections:0\r\nsync_full:0\r\nsync_partial_ok:0\r\nsync_partial_err:0\r\nexpired_keys:2669\r\nevicted_keys:0\r\nkeyspace_hits:76617\r\nkeyspace_misses:1124934\r\npubsub_channels:0\r\npubsub_patterns:0\r\nlatest_fork_usec:443\r\nmigrate_cached_sockets:0\r\n\r\n# Replication\r\nrole:master\r\nconnected_slaves:0\r\nmaster_repl_offset:0\r\nrepl_backlog_active:0\r\nrepl_backlog_size:1048576\r\nrepl_backlog_first_byte_offset:0\r\nrepl_backlog_histlen:0\r\n\r\n# CPU\r\nused_cpu_sys:933.50\r\nused_cpu_user:439.22\r\nused_cpu_sys_children:1.85\r\nused_cpu_user_children:0.73\r\n\r\n# Cluster\r\ncluster_enabled:0\r\n\r\n# Keyspace\r\ndb0:keys=40,expires=2,avg_ttl=45483\r\n\r\n","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":209,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[80,443,8000],"title":"NGINX"},{"belong_level":0,"rule_id":7204,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Oracle Corporation","ports":[3306],"title":"Oracle-MySQL"},{"belong_level":0,"rule_id":7208,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Redis Labs","ports":[6379],"title":"redis"},{"belong_level":0,"rule_id":7556,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"其他","ports":[6379],"title":"Linux-操作系统"},{"belong_level":0,"rule_id":12,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Elastic, Inc.","ports":[9200],"title":"elastic-Elasticsearch"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"Redis是一个开源的使用ANSI C语言编写的数据库。从2010年3月15日起，Redis因配置不当可以未授权访问，攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露。","common_impact":"<p>攻击者无需认证即可访问到内部数据，可能导致敏感信息泄露，可以恶意执行flushall来清空所有数据。攻击者也可已通过EVAL执行lua代码，或通过数据备份功能往磁盘写入后门文件。如果Redis以root身份运行，攻击者还可以给root账户写入SSH公钥文件，直接通过SSH登录受害服务器。<br></p>","recommandation":"<p>&nbsp;1、为 Redis 添加密码验证（重启redis才能生效）</p><p>修改 redis.conf 文件，添加</p><p><code>requirepass mypassword</code></p><p>（注意redis不要用-a参数，明文输入密码，连接后使用auth认证）</p><p>2、禁止外网访问 Redis（重启redis才能生效）</p><p>修改 redis.conf 文件，添加或修改，使得 Redis 服务只在当前主机可用</p><p><code>bind 127.0.0.1&nbsp; &nbsp; &nbsp; &nbsp; //修改后只有本机才能访问Redis，也可以指定访问源IP访问Redis</code></p><p>在redis3.2之后，redis增加了protected-mode，在这个模式下，非绑定IP或者没有配置密码访问时都会报错。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":null,"vulType":"命令执行","has_exp":1,"is_ipv6":false,"last_response":"$2210\r\n# Server\r\nredis_version:3.2.12\r\nredis_git_sha1:********\r\nredis_git_dirty:0\r\nredis_build_id:7897e7d0e13773f\r\nredis_mode:standalone\r\nos:Linux 5.9.11-1.el7.elrepo.x86_64 x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\ngcc_version:4.8.5\r\nprocess_id:6977\r\nrun_id:82c55bba1b758f46c7a65fd1cf939dcb619f8015\r\ntcp_port:6379\r\nuptime_in_seconds:504846\r\nuptime_in_days:5\r\nhz:10\r\nlru_clock:10348349\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis.conf\r\n\r\n# Clients\r\nconnected_clients:15\r\nclient_longest_output_list:0\r\nclient_biggest_input_buf:0\r\nblocked_clients:6\r\n\r\n# Memory\r\nused_memory:1215288\r\nused_memory_human:1.16M\r\nused_memory_rss:7311360\r\nused_memory_rss_human:6.97M\r\nused_memory_peak:1579280\r\nused_memory_peak_human:1.51M\r\ntotal_system_memory:8350412800\r\ntotal_system_memory_human:7.78G\r\nused_memory_lua:37888\r\nused_memory_lua_human:37.00K\r\nmaxmemory:0\r\nmaxmemory_human:0B\r\nmaxmemory_policy:noeviction\r\nmem_fragmentation_ratio:6.02\r\nmem_allocator:jemalloc-3.6.0\r\n\r\n# Persistence\r\nloading:0\r\nrdb_changes_since_last","has_response":1,"createtime":"2021-11-24 15:17:37","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:37"}}
{"_index":"fofaee_threats","_type":"threats","_id":"a48f01b44245f7bfa5f1323f69646b81","_score":1,"_source":{"hosts":null,"ip":"************","port":3389,"name":null,"common_title":"RDP远程命令执行漏洞","mac":null,"net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Microsoft-Windows远程连接","Microsoft-Windows","OpenSSH"],"cat_tags":["其他企业应用","操作系统","其他支撑系统"],"company_tags":["Microsoft Corporation","其他"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:3389","vulfile":"rdp_rce_cve_2019_0708.json","url":"http://************:3389","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"d2ac132824dc0fb5b1347c122b31b2f0","scan_engine":4,"port_list":[{"protocol":"ssh","port":22,"banner":"SSH-2.0-OpenSSH_7.4\r\n","certs":null},{"protocol":"portmap","port":111,"banner":"100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)","certs":null},{"protocol":"rdp","port":3389,"banner":"Remote Desktop Protocol\n\\x03\\x00\\x00\u000b\\x06\\xd0\\x00\\x00\\x124\\x00","certs":null},{"protocol":"unknown","port":2379,"banner":"","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7203,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"Microsoft Corporation","ports":[3389],"title":"Microsoft-Windows远程连接"},{"belong_level":0,"rule_id":7220,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Microsoft Corporation","ports":[3389],"title":"Microsoft-Windows"},{"belong_level":0,"rule_id":7512,"second_cat_tag":"其他支撑系统","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"其他","ports":[22],"title":"OpenSSH"}],"common_description":"微软公司于2019年5月14日发布重要安全公告，其操作系统远程桌面（Remote Desktop Services），俗称的3389服务存在严重安全漏洞（编号CVE-2019-0708）：攻击者在没有任何授权的情况下，可以远程直接攻击操作系统开放的3389服务，在受害主机上执行恶意攻击行为，包括安装后门，查看、篡改隐私数据，创建拥有完全用户权限的新账户，影响范围从Windows XP到Windows 2008 R2。由于3389服务应用广泛且该漏洞利用条件低，只要服务端口开放即可，导致该漏洞影响和危害程序堪比“WannaCry”。因此，微软额外为Windows XP、Windows 2003这些已经停止支持的系统发布了该漏洞的安全补丁。","common_impact":"<p>CVE-2019-0708漏洞是通过检查用户的身份认证，导致可以绕过认证，不用任何的交互，直接通</p><p>过rdp协议进行连接发送恶意代码执行命令到服务器中去。如果被攻击者利用，会导致服务器入侵</p><p>，中病毒，像WannaCry 永恒之蓝漏洞一样大规模的感染。无需用户任何操作，只要开机上网，不法分子就能在电脑和服务器中植入勒索软件、远程控制木马、虚拟货币挖矿机等恶意程序。</p>","recommandation":"<p><strong>修复建议：</strong></p><p>1. 升级微软官方补丁：</p><p>Windos XP、Windows 2003等老旧系统需手动下载补丁：<a href=\"https://support.microsoft.com/en-ca/help/4500705/customer-guidance-for-cve-2019-0708\" target=\"_blank\">https://support.microsoft.com/en-ca/help/4500705/customer-guidance-for-cve-2019-0708</a>；</p><p>Windows 7、Windows 2008系统自动升级即可，手动升级可到如下链接下载补丁<a href=\"https://www.catalog.update.microsoft.com/Search.aspx?q=KB4499175\" target=\"_blank\">https://www.catalog.update.microsoft.com/Search.aspx?q=KB4499175</a>；</p><p>2. 如非必要，请关闭远程桌面服务；</p><p>3. 建议网络管理员尽量限制对3389端口的访问，防止攻击发生。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2019-0708","vulType":"命令执行","has_exp":0,"is_ipv6":false,"last_response":"","has_response":0,"createtime":"2021-11-24 15:17:54","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:54"}}
{"_index":"fofaee_threats","_type":"threats","_id":"c691f9322216367689ba77ef2d82e844","_score":1,"_source":{"hosts":null,"ip":"************","port":3389,"name":"IIS7","common_title":"RDP远程命令执行漏洞","mac":"00:0c:29:26:a6:39","net_bios":null,"add_way":"self_defined","business_app":null,"province":null,"city":null,"company":"","username":null,"belong_user_id":1,"manager_mobile":null,"manager_email":null,"computer_room":null,"descriptions":null,"country":null,"operator":null,"operating_company":null,"rule_tags":["Microsoft-Windows远程连接","Microsoft-Windows","Microsoft-IIS","Microsoft-ASP.NET","Microsoft-ASP","Microsoft-SQL-Server"],"cat_tags":["其他企业应用","操作系统","服务","脚本语言","数据库系统"],"company_tags":["Microsoft Corporation"],"task_ids":[0,2],"state":1,"level":3,"hostinfo":"http://************:3389","vulfile":"rdp_rce_cve_2019_0708.json","url":"http://************:3389","obj_type":1,"object":"************","intranet_ip":1,"addition":null,"merge_md5":"cc2a5c01bf299ae5a018cf868cfb6628","scan_engine":4,"port_list":[{"protocol":"unknown","port":49153,"banner":"","certs":null},{"protocol":"mssql","port":1434,"banner":"\\x05a\\x00ServerName;WIN-3KAH4TLVD3M;InstanceName;SQLEXPRESS;IsClustered;No;Version;10.50.1600.1;tcp;1433;;","certs":null},{"protocol":"unknown","port":49152,"banner":"","certs":null},{"protocol":"unknown","port":49155,"banner":"","certs":null},{"protocol":"netbios","port":137,"banner":"NetBIOS Response\nMAC:00:0c:29:26:a6:39\nHostname:WIN-3KAH4TLVD3M<0>\nWORKGROUP<0>\nWIN-3KAH4TLVD3M<20>\n","certs":null},{"protocol":"netbios-ssn","port":139,"banner":"\\x83\\x00\\x00\\x01\\x8f","certs":null},{"protocol":"smb","port":445,"banner":"Version: 6.1Build 7601\nTarget Name : WIN-3KAH4TLVD3M\n","certs":null},{"protocol":"http","port":80,"banner":"HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nLast-Modified: Fri, 19 Mar 2021 03:27:28 GMT\r\nAccept-Ranges: bytes\r\nETag: \"f979aec96f1cd71:0\"\r\nServer: Microsoft-IIS/7.5\r\nX-Powered-By: ASP.NET\r\nDate: Wed, 24 Nov 2021 07:03:04 GMT\r\nConnection: close\r\nContent-Length: 689","certs":null},{"protocol":"mssql","port":1433,"banner":"MSSQL Server\nVersion: 171050560 (0xa320640)\nSub-Build: 0\nEncryption:Not available\n","certs":null},{"protocol":"unknown","port":49154,"banner":"","certs":null},{"protocol":"rdp","port":3389,"banner":"Remote Desktop Protocol\n\\x03\\x00\\x00\\x13\\x0e\\xd0\\x00\\x00\\x124\\x00\\x02\\x01\\x08\\x00\\x00\\x00\\x00\\x00\n\nFlag: PROTOCOL_RDP\n","certs":{"not_after":"2022-02-15 05:01:18","subject_cn":"WIN-3KAH4TLVD3M","issuer_cn":"WIN-3KAH4TLVD3M","not_before":"2021-08-16 05:01:18","v":"v3","sn":"92775521041630093646646913687089630543","sig_alth":"SHA1-RSA"}},{"protocol":"decrpc","port":135,"banner":"\\x05\\x00\r\\x03\\x10\\x00\\x00\\x00\\x18\\x00\\x00\\x00\\x01\\x00\\x00\\x00\\x04\\x00\\x01\\x05\\x00\\x00\\x00\\x00","certs":null}],"rule_infos":[{"belong_level":0,"rule_id":7203,"second_cat_tag":"其他企业应用","soft_hard_code":2,"first_cat_tag":"企业应用","level_code":3,"company":"Microsoft Corporation","ports":[3389],"title":"Microsoft-Windows远程连接"},{"belong_level":0,"rule_id":7220,"second_cat_tag":"操作系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":2,"company":"Microsoft Corporation","ports":[3389,445,80],"title":"Microsoft-Windows"},{"belong_level":0,"rule_id":208,"second_cat_tag":"服务","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":3,"company":"Microsoft Corporation","ports":[80],"title":"Microsoft-IIS"},{"belong_level":0,"rule_id":267,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Microsoft Corporation","ports":[80],"title":"Microsoft-ASP.NET"},{"belong_level":0,"rule_id":273,"second_cat_tag":"脚本语言","soft_hard_code":2,"first_cat_tag":"支撑系统","level_code":4,"company":"Microsoft Corporation","ports":[80],"title":"Microsoft-ASP"},{"belong_level":0,"rule_id":7205,"second_cat_tag":"数据库系统","soft_hard_code":2,"first_cat_tag":"系统软件","level_code":3,"company":"Microsoft Corporation","ports":[1433,1434],"title":"Microsoft-SQL-Server"}],"common_description":"微软公司于2019年5月14日发布重要安全公告，其操作系统远程桌面（Remote Desktop Services），俗称的3389服务存在严重安全漏洞（编号CVE-2019-0708）：攻击者在没有任何授权的情况下，可以远程直接攻击操作系统开放的3389服务，在受害主机上执行恶意攻击行为，包括安装后门，查看、篡改隐私数据，创建拥有完全用户权限的新账户，影响范围从Windows XP到Windows 2008 R2。由于3389服务应用广泛且该漏洞利用条件低，只要服务端口开放即可，导致该漏洞影响和危害程序堪比“WannaCry”。因此，微软额外为Windows XP、Windows 2003这些已经停止支持的系统发布了该漏洞的安全补丁。","common_impact":"<p>CVE-2019-0708漏洞是通过检查用户的身份认证，导致可以绕过认证，不用任何的交互，直接通</p><p>过rdp协议进行连接发送恶意代码执行命令到服务器中去。如果被攻击者利用，会导致服务器入侵</p><p>，中病毒，像WannaCry 永恒之蓝漏洞一样大规模的感染。无需用户任何操作，只要开机上网，不法分子就能在电脑和服务器中植入勒索软件、远程控制木马、虚拟货币挖矿机等恶意程序。</p>","recommandation":"<p><strong>修复建议：</strong></p><p>1. 升级微软官方补丁：</p><p>Windos XP、Windows 2003等老旧系统需手动下载补丁：<a href=\"https://support.microsoft.com/en-ca/help/4500705/customer-guidance-for-cve-2019-0708\" target=\"_blank\">https://support.microsoft.com/en-ca/help/4500705/customer-guidance-for-cve-2019-0708</a>；</p><p>Windows 7、Windows 2008系统自动升级即可，手动升级可到如下链接下载补丁<a href=\"https://www.catalog.update.microsoft.com/Search.aspx?q=KB4499175\" target=\"_blank\">https://www.catalog.update.microsoft.com/Search.aspx?q=KB4499175</a>；</p><p>2. 如非必要，请关闭远程桌面服务；</p><p>3. 建议网络管理员尽量限制对3389端口的访问，防止攻击发生。</p>","custom_fields":null,"gid":null,"uploaded":0,"notice_time":null,"cveId":"CVE-2019-0708","vulType":"命令执行","has_exp":0,"is_ipv6":false,"last_response":"","has_response":0,"createtime":"2021-11-24 15:17:54","lastchecktime":"2021-11-24 15:14:12","lastupdatetime":"2021-11-24 15:17:54"}}
