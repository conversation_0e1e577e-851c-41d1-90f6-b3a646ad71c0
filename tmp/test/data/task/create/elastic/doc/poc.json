{"id": 3332, "name": "CoreOS ETCD集群API未授权访问漏洞", "description": "ETCD是CoreOS公司发起的一个开源项目，ETCD是用于共享配置和服务发现的分布式、一致性的KV存储系统。CoreOS ETCD集群API存在未授权访问漏洞，ETCD用作所有群集数据的Kubernetes后备存储区，该漏洞可能会泄露大量的敏感信息。", "filename": "CoreOS_etcd_api_disclosure.json", "author": "vaf", "product": "CoreOS etcd ", "homepage": "https://coreos.com/etcd/", "references": "https://elweb.co/the-security-footgun-in-etcd/", "fofaquery": "protocol==\"etcd\"", "content": "[]", "state": 1, "user_id": null, "price": null, "score": null, "comments_count": 0, "fofa_records": 1, "level": 1, "xday_st": null, "affect_amount": 0, "affect_enterprise": null, "reject_reason": null, "vul_define": null, "production_define": null, "price_define": null, "task_state": 1, "task_percent": "0", "created_at": "2021-09-13 16:57:03", "updated_at": "2021-09-13 17:20:50", "last_scaned_at": null, "last_tid": null, "impact": "<p>攻击者可以获取AWS密钥和API密钥以及一系列服务的敏感信息，使用获取的密钥可以控制该集群进行进一步攻击，严重威胁用户的数据安全。</p>", "recommandation": "<p>1、请参考官方设置身份验证文档增加身份验证：<a href=\"https://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md\">https://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md</a>，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。</p><p>2、如非必要，禁止公网访问该服务。</p><p>3、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>", "has_exp": 0, "threat_count": null, "scan_task_id": null, "begin_scan_time": "2021-11-24 15:14:52", "cveId": null, "vulType": "未授权访问", "disclosure_date": "2018-03-16", "vulNum": "CVD-2018-0121"}