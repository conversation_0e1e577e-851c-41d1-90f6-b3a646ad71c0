{"_index": "fofaee_assets", "_type": "ips", "_id": "**********", "_score": 1, "_source": {"createtime": "2021-11-24 15:14:12", "lastupdatetime": "2021-11-24 16:52:48", "port_size": 14, "port_list": [{"protocol": "unknown", "port": 9100, "banner": "", "certs": null}, {"protocol": "zookeeper", "port": 2181, "banner": "Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:49382[1](queued=0,recved=38333,sent=38334)\n /*.*.*.*:57584[0](queued=0,recved=1,sent=0)\n\nLatency min/avg/max: 0/0/59\nReceived: 89329\nSent: 89329\nConnections: 2\nOutstanding: 0\nZxid: 0x2d9\nMode: standalone\nNode count: 166\n", "certs": null}, {"protocol": "http", "port": 9093, "banner": "HTTP/1.0 200 OK\r\nAccept-Ranges: bytes\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Length: 1314\r\nContent-Type: text/html; charset=utf-8\r\nExpires: 0\r\nLast-Modified: Thu, 01 Jan 1970 00:00:01 GMT\r\nPragma: no-cache\r\nDate: Wed, 24 Nov 2021 08:53:16 GMT", "certs": null}, {"protocol": "http", "port": 9200, "banner": "HTTP/1.0 401 Unauthorized\r\nWWW-Authenticate: Basic realm=\"security\" charset=\"UTF-8\"\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 381\r\n\r\n{\"error\":{\"root_cause\":[{\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}}],\"type\":\"security_exception\",\"reason\":\"missing authentication credentials for REST request [/]\",\"header\":{\"WWW-Authenticate\":\"Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\"}},\"status\":401}", "certs": null}, {"protocol": "kafka", "port": 9092, "banner": "Kafka\nAddr: **********:9092\nTopics: DOCS_STAT_APP_ACTIVITY DOCS_CPS_TASK __consumer_offsets", "certs": null}, {"protocol": "mysql", "port": 3306, "banner": "Mysql Version: 5.7.33-log \r\nN\\x00\\x00\\x00\n5.7.33-log\\x00[\\\\x00\\x00,_\\x15=\\x193\\x0f;\\x00\\xff\\xff\\xe0\\x02\\x00\\xff\\xc1\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00!AIuh_(J)yJL\\x00mysql_native_password\\x00", "certs": null}, {"protocol": "http", "port": 5000, "banner": "HTTP/1.0 200 OK\r\nCache-Control: no-cache\r\nDate: Wed, 24 Nov 2021 08:50:53 GMT\r\nContent-Length: 0", "certs": null}, {"protocol": "etcd", "port": 2379, "banner": "Name: etcd1\nId: 80286fd956d980e5\nStartTime: 2021-11-18T11:28:14.475600154Z\nVersion: 3.2.0+git\n", "certs": null}, {"protocol": "ssh", "port": 22, "banner": "SSH-2.0-OpenSSH_7.4\r\n", "certs": null}, {"protocol": "http", "port": 80, "banner": "HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 7975\r\nConnection: close\r\nVary: Accept-Encoding\r\nServer: APISIX/2.0\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 08:50:23 GMT\r\nVary: Accept-Encoding\r\nLast-Modified: <PERSON><PERSON>, 26 Oct 2021 15:52:59 GMT\r\nVary: Accept-Encoding\r\nETag: \"6178245b-1f27\"\r\nAccept-Ranges: bytes\r\nx-origin: http://**********\r\nEncryption: 0", "certs": null}, {"protocol": "portmap", "port": 111, "banner": "100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)", "certs": null}, {"protocol": "https", "port": 8081, "banner": "HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 08:53:33 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 323\r\nConnection: close", "certs": {"not_after": "2119-04-16 15:24:08", "subject_cn": "**********", "issuer_cn": "WPS", "not_before": "2021-10-26 15:24:08", "v": "v3", "domain": "localhost", "sn": "11157282297511052792", "sig_alth": "SHA256-RSA"}}, {"protocol": "http", "port": 9090, "banner": "HTTP/1.0 302 Found\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /prometheus\r\nDate: Wed, 24 Nov 2021 08:47:10 GMT\r\nContent-Length: 34", "certs": null}, {"protocol": "http", "port": 8888, "banner": "HTTP/1.0 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Length: 6586\r\nX-Content-Type-Options: nosniff\r\nReferrer-Policy: same-origin", "certs": null}], "ip": "**********", "is_ipv6": false, "state": 1, "ports": ["9100", "2181", "9093", "9200", "9092", "3306", "5000", "2379", "22", "80", "111", "8081", "9090", "8888"], "protocols": ["unknown", "zookeeper", "http", "kafka", "mysql", "etcd", "ssh", "portmap", "https"], "title_list": [{"port": 9093, "host": "**********:9093", "title": "Alert<PERSON><PERSON>"}, {"port": 80, "host": "**********", "title": "WPS 开放平台"}, {"port": 8081, "host": "https://**********:8081", "title": "WPS 部署可视化平台"}, {"port": 9090, "host": "**********:9090", "title": "Prometheus Time Series Collection and Processing Server"}, {"port": 8888, "host": "**********:8888", "title": "WWO运维管理后台"}], "add_way": "self_defined", "lastchecktime": "2021-11-24 16:54:52", "name": "WPS 开放平台", "host": null, "business_app": null, "group_name": null, "computer_room": null, "province": null, "city": null, "company": null, "belong_user_id": 1, "custom_fields": null, "custom_names": null, "username": null, "manager_email": null, "manager_mobile": null, "cat_tags": ["服务", "其他支撑系统", "数据库系统", "大数据处理", "中间件", "开发框架"], "rule_tags": ["NGINX", "OpenSSH", "Oracle-MySQL", "APACHE-<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "Bootstrap", "jQuery_UI", "七牛云-七牛CDN", "Prometheus-Alertmanager", "Prometheus-Time-Series-Collection-and-Processing-Server"], "rule_infos": [{"belong_level": 0, "rule_id": 209, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 3, "company": "其他", "ports": [8081, 80], "title": "NGINX"}, {"belong_level": 0, "rule_id": 7512, "second_cat_tag": "其他支撑系统", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 3, "company": "其他", "ports": [22], "title": "OpenSSH"}, {"belong_level": 0, "rule_id": 7204, "second_cat_tag": "数据库系统", "soft_hard_code": 2, "first_cat_tag": "系统软件", "level_code": 3, "company": "Oracle Corporation", "ports": [3306], "title": "Oracle-MySQL"}, {"belong_level": 0, "rule_id": 21157, "second_cat_tag": "大数据处理", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 5, "company": "Apache Software Foundation.", "ports": [2181], "title": "APACHE-<PERSON><PERSON><PERSON><PERSON>"}, {"belong_level": 0, "rule_id": 138, "second_cat_tag": "中间件", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "The jQuery Foundation.", "ports": [8888], "title": "j<PERSON><PERSON><PERSON>"}, {"belong_level": 0, "rule_id": 139, "second_cat_tag": "开发框架", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "twitter", "ports": [8888], "title": "Bootstrap"}, {"belong_level": 0, "rule_id": 255, "second_cat_tag": "开发框架", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "The jQuery Foundation.", "ports": [8888], "title": "jQuery_UI"}, {"belong_level": 0, "rule_id": 4605, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "上海七牛信息技术有限公司", "ports": [8888], "title": "七牛云-七牛CDN"}, {"belong_level": 0, "rule_id": 338447, "second_cat_tag": "其他支撑系统", "soft_hard_code": 0, "first_cat_tag": "支撑系统", "level_code": 5, "company": "The Linux Foundation.", "ports": [9093], "title": "Prometheus-Alertmanager"}, {"belong_level": 0, "rule_id": 338397, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 5, "company": "The Linux Foundation.", "ports": [9090], "title": "Prometheus-Time-Series-Collection-and-Processing-Server"}], "company_tags": ["其他", "Oracle Corporation", "Apache Software Foundation.", "The jQuery Foundation.", "twitter", "上海七牛信息技术有限公司", "The Linux Foundation."], "software_num": 10, "first_tag_num": [{"hardware_num": 0, "first_cat_tag": "支撑系统", "num": 9, "software_num": 9}, {"hardware_num": 0, "first_cat_tag": "系统软件", "num": 1, "software_num": 1}], "second_tag_num": [{"hardware_num": 0, "second_cat_tag": "服务", "num": 3, "software_num": 3}, {"hardware_num": 0, "second_cat_tag": "其他支撑系统", "num": 2, "software_num": 2}, {"hardware_num": 0, "second_cat_tag": "数据库系统", "num": 1, "software_num": 1}, {"hardware_num": 0, "second_cat_tag": "大数据处理", "num": 1, "software_num": 1}, {"hardware_num": 0, "second_cat_tag": "中间件", "num": 1, "software_num": 1}, {"hardware_num": 0, "second_cat_tag": "开发框架", "num": 2, "software_num": 2}], "mac": "b4:96:91:98:4b:f8"}}