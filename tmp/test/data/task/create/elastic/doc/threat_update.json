{"hosts": null, "ip": "**********", "port": 2379, "name": "WPS 开放平台", "common_title": "CoreOS ETCD集群API未授权访问漏洞(HELLOWORLD)", "mac": "b4:96:91:98:4b:f8", "net_bios": null, "add_way": "self_defined", "business_app": null, "province": null, "city": null, "company": "", "username": null, "belong_user_id": 1, "manager_mobile": null, "manager_email": null, "computer_room": null, "descriptions": null, "country": null, "operator": null, "operating_company": null, "rule_tags": ["NGINX", "OpenSSH", "Oracle-MySQL", "APACHE-<PERSON><PERSON><PERSON><PERSON>", "Prometheus-Time-Series-Collection-and-Processing-Server", "Prometheus-Alertmanager", "j<PERSON><PERSON><PERSON>", "Bootstrap", "jQuery_UI", "七牛云-七牛CDN"], "cat_tags": ["服务", "其他支撑系统", "数据库系统", "大数据处理", "中间件", "开发框架"], "company_tags": ["其他", "Oracle Corporation", "Apache Software Foundation.", "The Linux Foundation.", "The jQuery Foundation.", "twitter", "上海七牛信息技术有限公司"], "task_ids": [0, 2], "state": 1, "level": 1, "hostinfo": "http://**********:2389", "vulfile": "CoreOS_etcd_api_disclosure.json", "url": "http://**********:2379/v2/keys/?recursive=true", "obj_type": 1, "object": "**********", "intranet_ip": 1, "addition": null, "merge_md5": "ead45f4aa73085ac956cc51a2dfcc3b5", "scan_engine": 4, "port_list": [{"protocol": "unknown", "port": 9100, "banner": "", "certs": null}, {"protocol": "zookeeper", "port": 2181, "banner": "Zookeeper version: 3.4.13-2d71af4dbe22557fda74f9a9b4309b15a7487f03, built on 06/29/2018 04:05 GMT\nClients:\n /*.*.*.*:51222[0](queued=0,recved=1,sent=0)\n /*.*.*.*:49382[1](queued=0,recved=37794,sent=37795)\n\nLatency min/avg/max: 0/0/59\nReceived: 88067\nSent: 88067\nConnections: 2\nOutstanding: 0\nZxid: 0x2d9\nMode: standalone\nNode count: 166\n", "certs": null}, {"protocol": "http", "port": 9093, "banner": "HTTP/1.0 200 OK\r\nAccept-Ranges: bytes\r\nCache-Control: no-cache, no-store, must-revalidate\r\nContent-Length: 1314\r\nContent-Type: text/html; charset=utf-8\r\nExpires: 0\r\nLast-Modified: Thu, 01 Jan 1970 00:00:01 GMT\r\nPragma: no-cache\r\nDate: Wed, 24 Nov 2021 06:54:27 GMT", "certs": null}, {"protocol": "http", "port": 9200, "banner": "HTTP/1.0 401 Unauthorized\r\nWWW-Authenticate: Basic realm=\\\"security\\\" charset=\\\"UTF-8\\\"\r\ncontent-type: application/json; charset=UTF-8\r\ncontent-length: 381\r\n\r\n{\\\"error\\\":{\\\"root_cause\\\":[{\\\"type\\\":\\\"security_exception\\\",\\\"reason\\\":\\\"missing authentication credentials for REST request [/]\\\",\\\"header\\\":{\\\"WWW-Authenticate\\\":\\\"Basic realm=\\\\\\\"security\\\\\\\" charset=\\\\\\\"UTF-8\\\\\\\"\\\"}}],\\\"type\\\":\\\"security_exception\\\",\\\"reason\\\":\\\"missing authentication credentials for REST request [/]\\\",\\\"header\\\":{\\\"WWW-Authenticate\\\":\\\"Basic realm=\\\\\\\"security\\\\\\\" charset=\\\\\\\"UTF-8\\\\\\\"\\\"}},\\\"status\\\":401}", "certs": null}, {"protocol": "kafka", "port": 9092, "banner": "Kafka\nAddr: **********:9092\nTopics: DOCS_CPS_TASK __consumer_offsets DOCS_STAT_APP_ACTIVITY", "certs": null}, {"protocol": "mysql", "port": 3306, "banner": "Mysql Version: 5.7.33-log \r\nN\\\\x00\\\\x00\\\\x00\n5.7.33-log\\\\x007[\\\\x00\\\\x00?]a\\\\x15Gp\\\\x16/\\\\x00\\\\xff\\\\xff\\\\xe0\\\\x02\\\\x00\\\\xff\\\\xc1\\\\x15\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x1c4%\\\\x17{|6Y\\u000b1-Y\\\\x00mysql_native_password\\\\x00", "certs": null}, {"protocol": "http", "port": 5000, "banner": "HTTP/1.0 200 OK\r\nCache-Control: no-cache\r\nDate: Wed, 24 Nov 2021 07:07:06 GMT\r\nContent-Length: 0", "certs": null}, {"protocol": "etcd", "port": 2379, "banner": "Name: etcd1\nId: 80286fd956d980e5\nStartTime: 2021-11-18T11:28:14.475600154Z\nVersion: 3.2.0+git\n", "certs": null}, {"protocol": "ssh", "port": 22, "banner": "SSH-2.0-OpenSSH_7.4\r\n", "certs": null}, {"protocol": "http", "port": 80, "banner": "HTTP/1.1 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 7975\r\nConnection: close\r\nVary: Accept-Encoding\r\nServer: APISIX/2.0\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:08:59 GMT\r\nVary: Accept-Encoding\r\nLast-Modified: <PERSON><PERSON>, 26 Oct 2021 15:52:59 GMT\r\nVary: Accept-Encoding\r\nETag: \\\"6178245b-1f27\\\"\r\nAccept-Ranges: bytes\r\nx-origin: http://**********\r\nEncryption: 0", "certs": null}, {"protocol": "portmap", "port": 111, "banner": "100000 v4 TCP(111), 100000 v3 TCP(111), 100000 v2 TCP(111), 100000 v4 UDP(111), 100000 v3 UDP(111), 100000 v2 UDP(111)", "certs": null}, {"protocol": "https", "port": 8081, "banner": "HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Wed, 24 Nov 2021 07:11:39 GMT\r\nContent-Type: text/html; charset=utf-8\r\nContent-Length: 323\r\nConnection: close", "certs": {"not_after": "2119-04-16 15:24:08", "subject_cn": "**********", "issuer_cn": "WPS", "not_before": "2021-10-26 15:24:08", "v": "v3", "domain": "localhost", "sn": "11157282297511052792", "sig_alth": "SHA256-RSA"}}, {"protocol": "http", "port": 9090, "banner": "HTTP/1.0 302 Found\r\nContent-Type: text/html; charset=utf-8\r\nLocation: /prometheus\r\nDate: Wed, 24 Nov 2021 07:13:22 GMT\r\nContent-Length: 34", "certs": null}, {"protocol": "http", "port": 8888, "banner": "HTTP/1.0 200 OK\r\nContent-Type: text/html; charset=utf-8\r\nX-Frame-Options: SAMEORIGIN\r\nContent-Length: 6586\r\nX-Content-Type-Options: nosniff\r\nReferrer-Policy: same-origin", "certs": null}], "rule_infos": [{"belong_level": 0, "rule_id": 209, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 3, "company": "其他", "ports": [80, 8081], "title": "NGINX"}, {"belong_level": 0, "rule_id": 7512, "second_cat_tag": "其他支撑系统", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 3, "company": "其他", "ports": [22], "title": "OpenSSH"}, {"belong_level": 0, "rule_id": 7204, "second_cat_tag": "数据库系统", "soft_hard_code": 2, "first_cat_tag": "系统软件", "level_code": 3, "company": "Oracle Corporation", "ports": [3306], "title": "Oracle-MySQL"}, {"belong_level": 0, "rule_id": 21157, "second_cat_tag": "大数据处理", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 5, "company": "Apache Software Foundation.", "ports": [2181], "title": "APACHE-<PERSON><PERSON><PERSON><PERSON>"}, {"belong_level": 0, "rule_id": 338397, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 5, "company": "The Linux Foundation.", "ports": [9090], "title": "Prometheus-Time-Series-Collection-and-Processing-Server"}, {"belong_level": 0, "rule_id": 338447, "second_cat_tag": "其他支撑系统", "soft_hard_code": 0, "first_cat_tag": "支撑系统", "level_code": 5, "company": "The Linux Foundation.", "ports": [9093], "title": "Prometheus-Alertmanager"}, {"belong_level": 0, "rule_id": 138, "second_cat_tag": "中间件", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "The jQuery Foundation.", "ports": [8888], "title": "j<PERSON><PERSON><PERSON>"}, {"belong_level": 0, "rule_id": 139, "second_cat_tag": "开发框架", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "twitter", "ports": [8888], "title": "Bootstrap"}, {"belong_level": 0, "rule_id": 255, "second_cat_tag": "开发框架", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "The jQuery Foundation.", "ports": [8888], "title": "jQuery_UI"}, {"belong_level": 0, "rule_id": 4605, "second_cat_tag": "服务", "soft_hard_code": 2, "first_cat_tag": "支撑系统", "level_code": 4, "company": "上海七牛信息技术有限公司", "ports": [8888], "title": "七牛云-七牛CDN"}], "common_description": "ETCD是CoreOS公司发起的一个开源项目，ETCD是用于共享配置和服务发现的分布式、一致性的KV存储系统。CoreOS ETCD集群API存在未授权访问漏洞，ETCD用作所有群集数据的Kubernetes后备存储区，该漏洞可能会泄露大量的敏感信息。", "common_impact": "\\u003cp\\u003e攻击者可以获取AWS密钥和API密钥以及一系列服务的敏感信息，使用获取的密钥可以控制该集群进行进一步攻击，严重威胁用户的数据安全。\\u003c/p\\u003e", "recommandation": "\\u003cp\\u003e1、请参考官方设置身份验证文档增加身份验证：\\u003ca href=\\\"https://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md\\\"\\u003ehttps://github.com/etcd-io/etcd/blob/master/Documentation/v2/authentication.md\\u003c/a\\u003e，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。\\u003c/p\\u003e\\u003cp\\u003e2、如非必要，禁止公网访问该服务。\\u003c/p\\u003e\\u003cp\\u003e3、通过防火墙等安全设备设置访问策略，设置白名单访问。\\u003c/p\\u003e", "custom_fields": null, "gid": null, "uploaded": 0, "notice_time": null, "cveId": null, "vulType": "未授权访问", "has_exp": 0, "is_ipv6": false, "last_response": "HTTP/1.1 200 OK\r\nContent-Length: 37\r\nContent-Type: application/json\r\nDate: Wed, 24 Nov 2021 07:15:42 GMT\r\nX-Etcd-Cluster-Id: b2a60b81abc136b4\r\nX-Etcd-Index: 15\r\nX-Raft-Index: 1561\r\nX-Raft-Term: 14\r\n\r\n{\\\"action\\\":\\\"get\\\",\\\"node\\\":{\\\"dir\\\":true}}\n", "has_response": 1, "createtime": "2021-11-24 15:14:57", "lastchecktime": "2021-11-24 15:14:12", "lastupdatetime": "2021-11-24 15:14:57"}