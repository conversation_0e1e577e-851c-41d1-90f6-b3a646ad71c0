package searcher

import (
	"bytes"
	"encoding/json"
)

type ExpressionInter interface {
	Expression() *Expression
}

// Expression where condition expression.
type Expression struct {
	Query interface{}   `json:"query"`
	Args  []interface{} `json:"args"`
}

func (expression *Expression) String() string {
	marshal, _ := JSONMarshal(expression, false)
	return string(marshal)
}

func NewExpression(query interface{}, args ...interface{}) *Expression {
	return &Expression{Query: query, Args: args}
}

// JSONMarshal custom JSON marshal impl.
func JSONMarshal(v interface{}, escape bool) ([]byte, error) {
	buffer := &bytes.Buffer{}

	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(escape)
	err := encoder.Encode(v)
	if err != nil {
		return nil, err
	}

	if buffer.Len() > 0 {
		buffer.Truncate(buffer.Len() - 1)
	}

	return buffer.Bytes(), nil
}
