package searcher

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestSearcherSuite(t *testing.T) {
	suite.Run(t, &SearcherSuite{})
}

type SearcherSuite struct {
	suite.Suite
	expr *Expression
}

func (suite *SearcherSuite) BeforeTest(suiteName, testName string) {
	suite.expr = NewExpression("id = ?", 1)
	assert.NotNil(suite.T(), suite.expr)
}

func (suite *SearcherSuite) Test_String() {
	expected := `{"query":"id = ?","args":[1]}`
	str := suite.expr.String()
	fmt.Println(str)
	assert.Equal(suite.T(), expected, str)
}
