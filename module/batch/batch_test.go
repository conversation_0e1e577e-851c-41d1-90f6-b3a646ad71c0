package batch

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestBatchSuite(t *testing.T) {
	suite.Run(t, &BatchSuite{})
}

type BatchSuite struct {
	suite.Suite
}

func (suite *BatchSuite) Test_Calc() {
	grids := []struct {
		total    int
		batch    int
		expected int
	}{
		{
			total:    1225,
			batch:    100,
			expected: 13,
		},
		{
			total:    25,
			batch:    100,
			expected: 1,
		},
		{
			total:    100,
			batch:    100,
			expected: 1,
		},
	}

	for _, grid := range grids {
		actual := Calc(grid.total, grid.batch)
		assert.Equal(suite.T(), grid.expected, actual)
	}
}
