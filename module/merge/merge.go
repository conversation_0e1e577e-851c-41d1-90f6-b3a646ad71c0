package merge

import (
	"sync"
)

type Merge struct {
	store []interface{}
}

func (merge *Merge) Append(values ...interface{}) *Merge {
	merge.store = append(merge.store, values...)

	return merge
}

func (merge *Merge) Merged(fn HandlerFunc) Merged {
	var sets = &sync.Map{}
	var merged Merged

	// Filter
	for idx, item := range merge.store {
		// If HandlerFunc return false, then save store
		if !fn(item, sets) {
			sets.Store(idx, item)
		}
	}

	// Range sync map
	sets.Range(func(key, value interface{}) bool {
		if merged == nil {
			merged = make(Merged, 0)
		}

		merged = append(merged, value)

		return true
	})

	return merged
}

func NewMerge() *Merge {
	return &Merge{store: make([]interface{}, 0)}
}
