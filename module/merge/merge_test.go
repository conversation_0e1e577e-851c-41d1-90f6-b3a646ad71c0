package merge

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestMergeSuite(t *testing.T) {
	suite.Run(t, &MergeSuite{})
}

type MergeSuite struct {
	suite.Suite
	intMerge    *Merge
	structMerge *Merge
	stringMerge *Merge
}

func (suite *MergeSuite) BeforeTest(suiteName, testName string) {
	suite.intMerge = NewMerge()
	suite.structMerge = NewMerge()
	suite.stringMerge = NewMerge()
}

func (suite *MergeSuite) Test_IntMerge() {
	var _ = `["120","130","160","80","90"]`

	suite.intMerge.Append("80", "90")
	suite.intMerge.Append("90", "120")
	suite.intMerge.Append("130", "160")

	actual := suite.intMerge.Merged(HandlerStringFunc)
	assert.Len(suite.T(), actual.Value(), 5)
}

func (suite *MergeSuite) Test_StringMerge_Normal() {
	suite.stringMerge.Append("tom", "kitty")
	suite.stringMerge.Append("kitty", "darwin")
	suite.stringMerge.Append("tom", "windows", "linux")
	suite.stringMerge.Append("darwin", "windows")

	actual := suite.stringMerge.Merged(HandlerStringFunc)
	assert.Len(suite.T(), actual.Value(), 5)
}

func (suite *MergeSuite) Test_StringMerge_ContainsNil() {
	var nilSlice []interface{}

	suite.stringMerge.Append(nilSlice...)
	suite.stringMerge.Append("hello")

	actual := suite.stringMerge.Merged(HandlerStringFunc)
	assert.Len(suite.T(), actual.Value(), 1)
}

type _student struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

// HandlerStringFunc Default support string merge.
var _HandlerStructFunc HandlerFunc = func(val interface{}, sets *sync.Map) bool {
	var exists bool

	sets.Range(func(key, value interface{}) bool {
		if value.(*_student).Id == val.(*_student).Id {
			exists = true
		}

		return true
	})

	return exists
}

func (suite *MergeSuite) Test_StructMerge() {
	suite.structMerge.Append(
		&_student{
			Id:   1,
			Name: "tom",
		},
		&_student{
			Id:   2,
			Name: "kitty",
		},
	)

	suite.structMerge.Append(
		&_student{
			Id:   2,
			Name: "kitty",
		},
		&_student{
			Id:   4,
			Name: "helloshaohua",
		},
	)

	actual := suite.structMerge.Merged(_HandlerStructFunc)
	assert.Len(suite.T(), actual.Value(), 3)
}
