package merge

import "sync"

// HandlerFunc handler function structure definition.
type HandlerFunc func(val interface{}, sets *sync.Map) bool

// HandlerStringFunc Default support string merge.
var HandlerStringFunc HandlerFunc = func(val interface{}, sets *sync.Map) bool {
	var exists bool

	sets.Range(func(key, value interface{}) bool {
		if val.(string) == value.(string) {
			exists = true
		}

		return true
	})

	return exists
}
