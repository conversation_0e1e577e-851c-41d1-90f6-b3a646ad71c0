package queue

import (
	"fmt"
	"os"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"

	"git.gobies.org/foeye-dependencies/sidekip"
	sidekipconfig "git.gobies.org/foeye-dependencies/sidekip/config"
)

var (
	inst *sidekip.SideKip
	once sync.Once
)

func NewSidekipOr(config *config.Configure) (*sidekip.SideKip, error) {
	var err error
	var hostname string

	once.Do(func() {
		hostname, err = os.Hostname()
		conf := sidekipconfig.NewConfigDefault(
			sidekipconfig.WithProcessUnique(hostname),
			sidekipconfig.WithHost(config.Redis.Host),
			sidekipconfig.WithSelectDatabase(config.Redis.Database),
			sidekipconfig.WithPassword(config.Redis.Password),
		)
		inst = sidekip.NewSideKip(conf)
	})

	if err != nil {
		return nil, err
	}

	if inst == nil {
		return nil, fmt.Errorf("get queue instance to failed")
	}

	return inst, nil
}
