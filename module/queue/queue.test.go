package queue

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"testing"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/message"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/mosso"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestQueueSuite(t *testing.T) {
	suite.Run(t, &QueueSuite{})
}

type QueueSuite struct {
	suite.Suite
	config      *config.Configure
	message     *message.Message
	baseDir     string
	testDataDir string
}

func (suite *QueueSuite) BeforeTest(suiteName, testName string) {
	suite.config = config.GetConfigure(configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("conf.test"),
	)
	assert.NotNil(suite.T(), suite.config)

	suite.message = message.NewMessage()
	suite.baseDir = "./../../"
	suite.testDataDir = func() string {
		path, err := fsfire.GetFilePathWithFileSystemPath(
			suite.baseDir,
			fsfire.WithSpecificFileSystemPath("tmp/test/data/task"),
		)
		assert.NoError(suite.T(), err)
		return path
	}()
}

func (suite *QueueSuite) Test_NewSidekipOr() {
	actual, err := NewSidekipOr(suite.config)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)

	actual, err = NewSidekipOr(suite.config)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *QueueSuite) Test_TaskQueue() {
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "message_for_mock.json"))
	assert.NoError(suite.T(), err)

	message, err := suite.message.Unmarshal(data)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), message)
	mosso.DebugShowContentWithJSON(message)

	originalMessage := message.ConvertToOriginalMessage()
	mosso.DebugShowContentWithJSON(originalMessage)

	actual, err := NewSidekipOr(suite.config)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	enqueue, err := actual.Enqueue(
		suite.config.Redis.TaskQueue,
		suite.config.Redis.TaskQueueClass,
		originalMessage,
	)
	assert.NoError(suite.T(), err)
	fmt.Printf("enqueue: %+v\n", enqueue)
}
