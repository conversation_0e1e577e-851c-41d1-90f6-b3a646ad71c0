package location

import (
	"fmt"
	"sync"

	"git.gobies.org/foeye-dependencies/embedsfs"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/ipparser"
)

const (
	CountryDefaultOfChina     = "中国"
	CountryOfLocalAreaNetwork = "局域网"
)

var once sync.Once
var inst *Location

type Location struct {
	city     *ipparser.City
	embedsfs *embedsfs.EmbedsFS
}

func (l *Location) City() *ipparser.City {
	return l.city
}

func NewLocationOr(embedsfs *embedsfs.EmbedsFS) (*Location, error) {
	var e error

	once.Do(func() {
		if inst == nil {
			city, err := ipparser.NewCity(
				"embeds/address/mydata4vipweek2.datx",
				ipparser.WithEmbedsFS(embedsfs),
			)
			if err != nil {
				e = err
			}

			inst = &Location{
				city:     city,
				embedsfs: embedsfs,
			}
		}
	})

	if e != nil {
		return nil, e
	}

	if inst == nil {
		return nil, fmt.Errorf("cannot get location instance")
	}

	return inst, nil
}
