package location

import (
	"embed"
	"testing"

	"git.gobies.org/foeye-dependencies/embedsfs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

//go:embed embeds
var embeds embed.FS

func TestLocationSuite(t *testing.T) {
	suite.Run(t, &LocationSuite{})
}

type LocationSuite struct {
	suite.Suite
	location *Location
}

func (suite *LocationSuite) BeforeTest(suiteName, testName string) {
	var err error

	suite.location, err = NewLocationOr(embedsfs.NewEmbedsFS(embeds, embedsfs.WithPath("embeds")))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), suite.location)
}

func (suite *LocationSuite) Test_FindLocation() {
	loc, err := suite.location.City().FindLocation("42.3.19.172")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), loc.CityCode, "810000")
}

func (suite *LocationSuite) Test_FindLocation_USA() {
	loc, err := suite.location.City().FindLocation("40.77.167.46")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), loc.TimeZone, "America/New_York")
}

func (suite *LocationSuite) Test_FindLocation_Local() {
	loc, err := suite.location.City().FindLocation("127.0.0.1")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), loc.Country, "本机地址")
}
