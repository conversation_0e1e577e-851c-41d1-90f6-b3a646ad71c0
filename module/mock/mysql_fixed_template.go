package mock

import "git.gobies.org/foeye-dependencies/jsonfixed"

var PocMockDataConvertTemplate = jsonfixed.Template{
	"created_at":      timeStringToTime,
	"updated_at":      timeStringToTime,
	"disclosure_date": dateStringToTime,
	"last_scaned_at":  timeStringToTime,
	"begin_scan_time": timeStringToTime,
	"has_exp":         intToBool,
	"vulNum":          emptyStringToBool,
}

var IPRangeConditionMockDataConvertTemplate = jsonfixed.Template{
	"created_at": timeStringToTime,
	"updated_at": timeStringToTime,
}

var TaskPocMockDataConvertTemplate = jsonfixed.Template{
	"created_at": timeStringToTime,
	"updated_at": timeStringToTime,
}

var TaskMockDataConvertTemplate = jsonfixed.Template{
	"created_at":        timeStringToTime,
	"updated_at":        timeStringToTime,
	"started_at":        timeStringToTime,
	"real_begin_time":   timeStringToTime,
	"real_end_time":     timeStringToTime,
	"ready":             intToBool,
	"timer_task_switch": intToBool,
	"top":               intToBool,
}
