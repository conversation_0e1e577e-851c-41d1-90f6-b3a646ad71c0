package mock

import (
	"encoding/json"
	"fmt"
	"sync"

	"git.gobies.org/foeye-dependencies/jsonfixed"
)

var (
	timeStringToTime = jsonfixed.NewDestinationOfTime(
		jsonfixed.TimeFormatOfChina24,
		jsonfixed.TimeFormatOfRFC3339,
	)

	dateStringToTime = jsonfixed.NewDestinationOfTime(
		jsonfixed.TimeFormatOfChinaDate,
		jsonfixed.TimeFormatOfRFC3339,
	)

	intToBool         = jsonfixed.NewDestinationOfBool(1)
	emptyStringToBool = jsonfixed.NewDestinationOfNull("")

	DefaultTimeFieldsConvertTemplate = jsonfixed.Template{
		"created_at": timeStringToTime,
		"updated_at": timeStringToTime,
	}
)

type MySQLMockFixed struct {
	template jsonfixed.Template
	access   sync.RWMutex
}

func (mock *MySQLMockFixed) SetConvertTemplate(template jsonfixed.Template) {
	mock.access.Lock()
	defer mock.access.Unlock()

	mock.template = template
}

func NewMySQLFixedMock() *MySQLMockFixed {
	return &MySQLMockFixed{}
}

func (mock *MySQLMockFixed) FixedSingle(data []byte) ([]byte, error) {
	var r interface{}
	err := json.Unmarshal(data, &r)
	if err != nil {
		return nil, err
	}

	marshal, err := json.Marshal(r)
	if err != nil {
		return nil, err
	}

	parsed, err := mock.single(marshal)
	if err != nil {
		fmt.Printf("single processing has error: %+v\n", err)
	}

	marshal, err = json.Marshal(parsed)
	if err != nil {
		return nil, err
	}

	return marshal, err
}

func (mock *MySQLMockFixed) FixedSlice(data []byte) ([]byte, error) {
	var r []interface{}
	err := json.Unmarshal(data, &r)
	if err != nil {
		return nil, err
	}

	for idx, cur := range r {
		marshal, err := json.Marshal(cur)
		if err != nil {
			continue
		}

		parsed, err := mock.single(marshal)
		if err != nil {
			fmt.Printf("single processing has error: %+v\n", err)
		}

		r[idx] = parsed
	}

	marshal, err := json.Marshal(r)
	if err != nil {
		return nil, err
	}

	return marshal, err
}

func (mock *MySQLMockFixed) single(data []byte) (interface{}, error) {
	parse, err := jsonfixed.Convert(data, mock.template)
	if err != nil {
		return nil, err
	}

	var unmarshal interface{}
	if err = json.Unmarshal(parse, &unmarshal); err != nil {
		return nil, err
	}

	return unmarshal, nil
}
