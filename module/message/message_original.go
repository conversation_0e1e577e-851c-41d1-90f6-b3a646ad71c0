package message

import (
	"encoding/json"
	"strconv"
)

type OriginalMessageImpl interface {
	SetTaskID(taskId int) *OriginalMessage
	SetBandwidth(Bandwidth string) *OriginalMessage
}

// OriginalMessage original message information structure.
type OriginalMessage struct {
	*Base
	JobID     int    `json:"jobID"`
	TaskId    string `json:"task_id"`
	Bandwidth string `json:"bandwidth"`
}

func (message *OriginalMessage) String() string {
	marshal, _ := json.Marshal(&message)
	return string(marshal)
}

func (message *OriginalMessage) GetTaskIdOfInt() int {
	message.lock.Lock()
	defer message.lock.Unlock()

	atto, _ := strconv.Atoi(message.TaskId)
	return atto
}

func (message *OriginalMessage) GetBandwidthOfInt() int {
	message.lock.Lock()
	defer message.lock.Unlock()

	atto, _ := strconv.Atoi(message.Bandwidth)
	return atto
}

func (message *OriginalMessage) SetTaskID(taskId int) *OriginalMessage {
	message.lock.Lock()
	defer message.lock.Unlock()

	message.TaskId = strconv.Itoa(taskId)
	message.JobID = taskId
	return message
}

func (message *OriginalMessage) SetBandwidth(bandwidth interface{}) *OriginalMessage {
	message.lock.Lock()
	defer message.lock.Unlock()

	switch v := bandwidth.(type) {
	case int:
		message.Bandwidth = strconv.Itoa(v)
	case string:
		message.Bandwidth = v
	default:
		message.Bandwidth = "100"
	}

	return message
}

// NewOriginalMessage initialize original message instance.
func NewOriginalMessage(baseMessage *Base, taskID int, bandwidth int) *OriginalMessage {
	var taskId string
	var bandWidth string
	if taskID != 0 {
		taskId = strconv.Itoa(taskID)
	}
	if bandwidth != 0 {
		bandWidth = strconv.Itoa(bandwidth)
	}

	return &OriginalMessage{Base: baseMessage, TaskId: taskId, Bandwidth: bandWidth}
}

func NewDefaultOriginalMessage() *OriginalMessage {
	return &OriginalMessage{Base: &Base{}}
}
