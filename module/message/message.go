package message

import (
	"encoding/json"
	"strings"
	"sync"

	"git.gobies.org/foeye-dependencies/cidr"
	"git.gobies.org/foeye-dependencies/jsonfixed"
)

var access sync.RWMutex

// Message information structure.
type Message struct {
	*Base
	TaskID    int `json:"task_id"`
	Bandwidth int `json:"bandwidth"`
}

// NewMessage init message instance.
func NewMessage() *Message {
	return &Message{}
}

// Unmarshal parse message data to internal structure.
func (message *Message) Unmarshal(data []byte) (*Message, error) {
	convert, err := jsonfixed.Convert(
		data,
		jsonfixed.Template{
			"task_id":   jsonfixed.NewDestinationOfInteger(jsonfixed.IntegerTypeOfInt),
			"bandwidth": jsonfixed.NewDestinationOfInteger(jsonfixed.IntegerTypeOfInt),
		},
	)

	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(convert, &message)
	if err != nil {
		return nil, err
	}

	return message, nil
}

// ConvertToOriginalMessage Convert to OriginalMessage.
func (message *Message) ConvertToOriginalMessage() *OriginalMessage {
	return NewOriginalMessage(message.Base, message.TaskID, message.Bandwidth)
}

// IPInBlacklist check whether ip in blacklist.
func (message *Message) IPInBlacklist(ip string) bool {
	access.RLock()
	defer access.RUnlock()

	if message.Blacklist != "" {
		slice := strings.Split(message.Blacklist, ",")
		for _, item := range slice {
			container := strings.TrimSpace(item)
			if cidr.IsContainsIP(ip, container) {
				return true
			}
		}
	}

	return false
}
