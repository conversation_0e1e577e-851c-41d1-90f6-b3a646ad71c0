package message

import (
	"encoding/json"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

// Base message structure definition.
type Base struct {
	IPList              string            `json:"ip_list,omitempty"`
	Ports               string            `json:"ports"`
	RepeatTimes         int               `json:"repeat_times"`
	Blacklist           string            `json:"blacklist"`
	ProtocolUpdateCycle int               `json:"protocol_update_cycle"`
	UnknownProtocolIndb bool              `json:"unknown_protocol_indb"`
	PingScan            bool              `json:"ping_scan"`
	TreckScan           bool              `json:"treck_scan"`
	CycleProtocol       bool              `json:"cycle_protocol"` // 是否是全协议识别
	DeepGetOs           bool              `json:"deep_get_os"`
	DeepGetMac          bool              `json:"deep_get_mac"`
	CrackDns            bool              `json:"crack_dns"`           // 域名解析：对微内核没有用，对打标签有用
	VersionRecognition  bool              `json:"version_recognition"` // 版本号识别：对微内核没有用，对打标签有用
	GetwayMac           string            `json:"getway_mac"`
	TaskType            string            `json:"task_type"`
	HostInfos           []string          `json:"hostinfos"`
	IPListFilename      string            `json:"ip_list_filename"`
	ResumeFilename      string            `json:"resume_filename"`
	MaxAssetNum         int               `json:"max_asset_num"`
	IsIpv6              bool              `json:"is_ipv6"`
	Engine              models.EngineType `json:"engine"`

	lock sync.RWMutex
}

func (base *Base) String() string {
	marshal, _ := json.Marshal(&base)
	return string(marshal)
}

func (base *Base) SetIPList(ips string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.IPList = ips
	return base
}

func (base *Base) SetPorts(ports string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.Ports = ports
	return base
}

func (base *Base) SetRepeatTimes(isDeepScan bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	var repeat = 1
	if isDeepScan == true {
		repeat = 3
	}

	base.RepeatTimes = repeat
	return base
}

func (base *Base) SetBlacklist(blacklist string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.Blacklist = blacklist
	return base
}

func (base *Base) SetProtocolUpdateCycle(cycle int) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.ProtocolUpdateCycle = cycle
	return base
}

func (base *Base) SetUnknownProtocolIndb(storage bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.UnknownProtocolIndb = storage
	return base
}

func (base *Base) SetPingScan(ping bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.PingScan = ping
	return base
}

func (base *Base) SetTreckScan(treck bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.TreckScan = treck
	return base
}

func (base *Base) SetDeepGetOs(deep bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.DeepGetOs = deep
	return base
}

func (base *Base) SetCycleProtocol(cycle bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.CycleProtocol = cycle
	return base
}

func (base *Base) SetDeepGetMac(deepGetMac bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.DeepGetMac = deepGetMac
	return base
}

func (base *Base) SetCrackDns(crackDns bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.CrackDns = crackDns
	return base
}

func (base *Base) SetIsVersionRecognition(versionRecognition bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.VersionRecognition = versionRecognition
	return base
}

func (base *Base) SetGetwayMac(getwayMac string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.GetwayMac = getwayMac
	return base
}

func (base *Base) SetTaskType(taskType string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.TaskType = taskType
	return base
}

func (base *Base) SetHostInfos(hostinfos []string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.HostInfos = hostinfos
	return base
}

func (base *Base) SetIPListFilename(filename string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.IPListFilename = filename
	return base
}

func (base *Base) SetResumeFilename(filename string) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.ResumeFilename = filename
	return base
}

func (base *Base) SetMaxAssetNum(maxAssetNumber int) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.MaxAssetNum = maxAssetNumber
	return base
}

func (base *Base) SetIsIpv6(ipv6 bool) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.IsIpv6 = ipv6
	return base
}

func (base *Base) SetEngine(engine models.EngineType) *Base {
	base.lock.Lock()
	defer base.lock.Unlock()

	base.Engine = engine
	return base
}

func (base *Base) GetTaskType() string {
	base.lock.RLock()
	defer base.lock.RUnlock()

	return base.TaskType
}
