package message

import (
	"io/ioutil"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/mosso"
)

func TestMessageSuite(t *testing.T) {
	suite.Run(t, &MessageSuite{})
}

type MessageSuite struct {
	suite.Suite
	message     *Message
	baseDir     string
	testDataDir string
}

func (suite *MessageSuite) BeforeTest(suiteName, testName string) {
	suite.message = NewMessage()
	suite.baseDir = "./../../"
	suite.testDataDir = func() string {
		path, err := fsfire.GetFilePathWithFileSystemPath(
			suite.baseDir,
			fsfire.WithSpecificFileSystemPath("tmp/test/data/task"),
		)
		assert.NoError(suite.T(), err)
		return path
	}()
}

func (suite *MessageSuite) Test_Unmarshal() {
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "message_for_mock.json"))
	assert.NoError(suite.T(), err)

	message, err := suite.message.Unmarshal(data)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), message)

	//mosso.DebugShowContentWithJSON(message)
}

func (suite *MessageSuite) Test_ConvertToOriginalMessage() {
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "message_for_mock.json"))
	assert.NoError(suite.T(), err)

	message, err := suite.message.Unmarshal(data)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), message)
	//mosso.DebugShowContentWithJSON(message)

	originalMessage := message.ConvertToOriginalMessage()
	mosso.DebugShowContentWithJSON(originalMessage)
}

func (suite *MessageSuite) Test_IPInCIDR() {
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "message_for_mock.json"))
	assert.NoError(suite.T(), err)

	message, err := suite.message.Unmarshal(data)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), message)

	actual := message.IPInBlacklist("**********")
	assert.Equal(suite.T(), true, actual)

	actual = message.IPInBlacklist("************")
	assert.Equal(suite.T(), true, actual)

	actual = message.IPInBlacklist("***********")
	assert.Equal(suite.T(), false, actual)
}
