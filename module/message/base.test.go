package message

import (
	"testing"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestBaseSuite(t *testing.T) {
	suite.Run(t, &BaseSuite{})
}

type BaseSuite struct {
	suite.Suite
	base *Base
}

func (suite *BaseSuite) BeforeTest(suiteName, testName string) {
	suite.base = &Base{}
}

func (suite *BaseSuite) Test_Sets() {
	// TODO: 可以提示一个JSON可视化并且还非常优雅的转换工具.
	expected := `{"ip_list":"**********/24,**********/24","ports":"22,80","repeat_times":1,"blacklist":"127.0.0.1","protocol_update_cycle":0,"unknown_protocol_indb":true,"ping_scan":true,"treck_scan":true,"cycle_protocol":false,"deep_get_os":true,"deep_get_mac":true,"crack_dns":false,"version_recognition":false,"getway_mac":"ef:23:48:00:fd:22","task_type":"","hostinfos":null,"ip_list_filename":"./ipranges_100.txt","resume_filename":"","max_asset_num":50,"is_ipv6":true,"engine":"VulnerabilityScanEngine"}`

	actual := suite.base.SetBlacklist("127.0.0.1").
		SetPorts("22,80").
		SetIPList("**********/24,**********/24").
		SetHostInfos(nil).
		SetRepeatTimes(false).
		SetDeepGetMac(true).
		SetDeepGetOs(true).
		SetUnknownProtocolIndb(true).
		SetPingScan(true).
		SetTreckScan(true).
		SetIsIpv6(true).
		SetIPListFilename("./ipranges_100.txt").
		SetMaxAssetNum(50).
		SetGetwayMac("ef:23:48:00:fd:22").
		SetEngine(models.EngineTypeOfVulnerability)

	assert.Equal(suite.T(), expected, actual.String())
}
