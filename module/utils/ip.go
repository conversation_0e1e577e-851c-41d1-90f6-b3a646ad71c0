package utils

import (
	"net"
)

// IsIPv4 Check whether the IP address is IPv4.
func IsIPv4(addr string) bool {
	if !IsIP(addr) {
		return false
	}

	ip := net.ParseIP(addr)
	if ip.To4() != nil {
		return true
	}

	return false
}

// IsIPv6 Check whether the IP address is IPv6.
func IsIPv6(addr string) bool {
	if !IsIP(addr) {
		return false
	}

	ip := net.ParseIP(addr)
	if ip.To4() != nil {
		return false
	}
	return true
}

// IsIP Check whether the IP address is real ip.
func IsIP(addr string) bool {
	ip := net.ParseIP(addr)
	if ip != nil {
		return true
	}

	return false
}
