package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIntValueInSlice(t *testing.T) {
	grids := []struct {
		slice    []int
		find     int
		expected bool
	}{
		{
			slice:    []int{1, 2, 3},
			find:     3,
			expected: true,
		},
		{
			slice:    []int{1, 2, 3},
			find:     4,
			expected: false,
		},
	}

	for _, grid := range grids {
		actual := IntValueInSlice(grid.slice, grid.find)
		assert.Equal(t, grid.expected, actual)
	}
}

func TestIntValueRemoveInSlice(t *testing.T) {
	grids := []struct {
		slice    []int
		find     int
		expected []int
	}{
		{
			slice:    []int{1, 2, 3},
			find:     2,
			expected: []int{1, 3},
		},
		{
			slice:    []int{1, 2, 3},
			find:     1,
			expected: []int{2, 3},
		},
		{
			slice:    []int{1, 2, 3},
			find:     3,
			expected: []int{1, 2},
		},
		{
			slice:    []int{1, 2, 3},
			find:     4,
			expected: []int{1, 2, 3},
		},
	}

	for _, grid := range grids {
		actual := IntValueRemoveInSlice(grid.slice, grid.find)
		assert.Equal(t, grid.expected, actual)
	}
}
