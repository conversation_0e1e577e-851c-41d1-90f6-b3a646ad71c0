package system

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"git.gobies.org/foeye-dependencies/httpclient"
	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
)

const (
	MicroKernelServerHost             = "http://127.0.0.1:61234"
	MicroKernelServerURIForSystemInfo = "/api/v1/system/infos"
)

func GetMicroKernelRequestUrlForSystemInfo() string {
	return strings.Join([]string{MicroKernelServerHost, MicroKernelServerURIForSystemInfo}, "")
}

func GetSystemInfoWithUrl(url string, mock bool) (*exchange.SystemInfo, error) {
	var data []byte

	if mock {
		data = []byte(`{
			"status_code":200,
			"messages":"success",
			"data":{
				"version":"0.1.1",
				"license_state":true,
				"asset_limit_num":1000,
				"product_limit_date":"2022-05-27",
				"upgrade_limit_date":"2022-05-27"
			}
		}`)
	} else {
		resp, err := httpclient.Get(url).Timeout(time.Duration(3 * time.Second)).Do()
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()

		data, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
	}

	var r *exchange.SystemInfoResponse
	if err := json.Unmarshal(data, &r); err != nil {
		return nil, err
	}

	if r != nil && r.StatusCode == http.StatusOK && r.Data != nil {
		return r.Data, nil
	}

	return nil, fmt.Errorf("parse microkernel system info response data to failed")
}
