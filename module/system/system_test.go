package system

import (
	"testing"

	"git.gobies.org/foeye-dependencies/mosso"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestSystemSuite(t *testing.T) {
	suite.Run(t, &SystemSuite{})
}

type SystemSuite struct {
	suite.Suite
	url  string
	mock bool
}

func (suite *SystemSuite) BeforeTest(suiteName, testName string) {
	suite.url = "http://httpbin.org/get"
	suite.mock = true
}

func (suite *SystemSuite) Test_GetSystemInfoWithUrl() {
	data, err := GetSystemInfoWithUrl(suite.url, suite.mock)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	mosso.DebugShowContentWithJSON(data)
}

func (suite *SystemSuite) Test_GetSystemInfoWithUrl_Rea() {
	data, err := GetSystemInfoWithUrl(suite.url, false)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), data)
}

func (suite *SystemSuite) Test_GetMicroKernelRequestUrlForSystemInfo() {
	expected := `http://127.0.0.1:61234/api/v1/system/infos`
	actual := GetMicroKernelRequestUrlForSystemInfo()
	assert.Equal(suite.T(), expected, actual)
}
