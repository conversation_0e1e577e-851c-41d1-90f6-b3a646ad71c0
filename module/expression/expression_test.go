package expression

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsInternalIP(t *testing.T) {
	grids := []struct {
		ip       string
		expected bool
	}{
		{
			ip:       "127.0.0.1",
			expected: true,
		},
		{
			ip:       "********",
			expected: true,
		},
		{
			ip:       "**********",
			expected: true,
		},
		{
			ip:       "**********",
			expected: true,
		},
		{
			ip:       "**********",
			expected: true,
		},
		{
			ip:       "**********",
			expected: true,
		},
		{
			ip:       "**********",
			expected: false,
		},
		{
			ip:       "*********",
			expected: false,
		},
		{
			ip:       "*********",
			expected: false,
		},
	}

	for _, grid := range grids {
		actual := IsInternalIP(grid.ip)
		assert.Equal(t, grid.expected, actual)
	}
}
