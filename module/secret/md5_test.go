package secret

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_EncodeMD5(t *testing.T) {
	grids := []struct {
		val      string
		expected string
	}{
		{val: "HelloWorld", expected: "68e109f0f40ca72a15e05cc22786f8e6"},
		{val: "HelloShaohua", expected: "8f6faf6638eab379062ae326eea5cc19"},
		{val: "<EMAIL>", expected: "7a30ecc1aa47240670d49b240ddc17e8"},
		{val: "Hello China~", expected: "896c5c89d67ad964f6edb22e52a75bea"},
		{val: "Hello Beijing!", expected: "0c527e80bb5d92afe9d17f9795abb02f"},
		{val: "Hello Taiwan~~~", expected: "b466c7cdaf4774f6f3e3c4879d685354"},
		{val: "Hello Shandong!!!", expected: "0756a1ac77704b19ce01b24713a993a6"},
		{val: "Hello Shanghai...", expected: "77205ee9ac5e1e6f5196405e895f9186"},
	}

	for _, grid := range grids {
		actual := EncodeMD5(grid.val)
		assert.Equal(t, grid.expected, actual, "has error: want value of the %+v, but got value of the %+v, the origin value of %+v\n", grid.expected, actual, grid.val)
	}
}
