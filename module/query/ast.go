package query

import (
	"strings"

	parsec "github.com/prataprc/goparsec"
)

type OnParseFieldFunc func(field, value string) string

type ElasticQueryParser struct {
	AST             *parsec.AST
	RootParser      parsec.Parser
	OnParseField    OnParseFieldFunc //解析field时的回调函数，用户获取特定的字段值，比如taskid
	QueryForService bool             //是否从goby_task_xxx_service查询
}

func NewElasticQueryParser() *ElasticQueryParser {
	ast, rootParser := genAst()
	return &ElasticQueryParser{
		AST:        ast,
		RootParser: rootParser,
	}
}

func (fqp *ElasticQueryParser) ParseToES6(query string) string {
	r := fqp.parse(query)
	nodes := r.GetChildren()
	return fqp.parseAstToES6(nodes)
}

func (fqp *ElasticQueryParser) ParseToText(query string) string {
	r := fqp.parse(query)
	nodes := r.GetChildren()
	return fqp.parseAst(nodes)
}

func genAst() (*parsec.AST, parsec.Parser) {
	ast := parsec.NewAST("fofaquery", 100)

	space := parsec.Token(`\s`, "SPACE")
	spaceMaybe := parsec.Maybe(nil, space)
	//text := parsec.Token(`[a-zA-Z0-9\-_]*?`, "TEXT")
	left_parenthesis := parsec.Atom("(", "left_parenthesis")
	right_parenthesis := parsec.Atom(")", "right_parenthesis")

	//# Comparisonss
	fulleq := parsec.Atom("==", "fulleq")
	eq := parsec.Atom("=", "eq")
	not_eq := parsec.Atom("!=", "not_eq")
	matches := parsec.Atom("~=", "matches")
	lt := parsec.Atom("<", "lt")
	lteq := parsec.Atom("<=", "lteq")
	gt := parsec.Atom(">", "gt")
	gteq := parsec.Atom(">=", "gteq")
	notContains := parsec.Atom("-", "not_contains")
	notContainsMaybe := parsec.Maybe(nil, notContains)

	//# Operators
	and_operator := parsec.Atom("&&", "and_operator")
	or_operator := parsec.Atom("||", "or_operator")
	//operator := ast.OrdChoice("operator", func(name string, s parsec.Scanner, node parsec.Queryable) parsec.Queryable {
	//	return node
	//}, and_operator, or_operator)

	//# Operand
	null := parsec.Atom("null", "null")
	boolean := ast.OrdChoice("boolean", nil, parsec.Atom("true", "true"), parsec.Atom("false", "false"))
	number := parsec.Token(`[-+]?([0-9]*\.)?[0-9]+$`, "number")
	gantan := parsec.Atom("!", "null")
	//doubleQuote := parsec.Atom(`"`, "DOUBLEQUOTE")
	//doubleQuoteValue := parsec.Token(`(?:\\"|.)*`, "DOUBLEQUOTEVALUE")
	//doubleQuoteString := ast.And("DOUBLEQUOTESTRING", nil, doubleQuote, doubleQuoteValue, doubleQuote)
	doubleQuoteString := parsec.Token(`"(?:\\"|.)*?"`, "DOUBLEQUOTESTRING")
	literal := parsec.Token(`[a-zA-Z0-9\-_\.\p{L}]+`, "literal")
	identifier := ast.OrdChoice("identifier", nil, null, boolean, number, literal, doubleQuoteString, gantan)

	//# Grammar
	compare_fulleq := ast.And("compare_fulleq", nil, literal, spaceMaybe, fulleq, spaceMaybe, identifier)
	compare_eq := ast.And("compare_eq", nil, literal, spaceMaybe, eq, spaceMaybe, identifier)
	compare_not_eq := ast.And("compare_not_eq", nil, literal, spaceMaybe, not_eq, spaceMaybe, identifier)
	compare_matches := ast.And("compare_matches", nil, literal, spaceMaybe, matches, spaceMaybe, identifier)
	compare_lt := ast.And("compare_lt", nil, literal, spaceMaybe, lt, spaceMaybe, identifier)
	compare_lteq := ast.And("compare_lteq", nil, literal, spaceMaybe, lteq, spaceMaybe, identifier)
	compare_gt := ast.And("compare_gt", nil, literal, spaceMaybe, gt, spaceMaybe, identifier)
	compare_gteq := ast.And("compare_gteq", nil, literal, spaceMaybe, gteq, spaceMaybe, identifier)
	compare_contains := ast.And("compare_contains", nil, notContainsMaybe, identifier)

	//var or_operation parsec.Parser // forward declaration allows for recursive parsing
	//var and_operation parsec.Parser // forward declaration allows for recursive parsing

	//var primary parsec.Parser
	var or_operation parsec.Parser
	var and_operation parsec.Parser
	compare := ast.OrdChoice("compare", nil, compare_fulleq, compare_eq, compare_not_eq, compare_matches, compare_lteq, compare_lt, compare_gteq, compare_gt, compare_contains)
	parenthesis := ast.And("parenthesis", nil, left_parenthesis, spaceMaybe, &or_operation, spaceMaybe, right_parenthesis)
	primary := ast.OrdChoice("primary", nil, parenthesis, compare)
	and_operation_item := ast.And("and_operation_item", nil, primary, spaceMaybe, and_operator, spaceMaybe, &and_operation)
	and_operation = ast.OrdChoice("and_operation_item", nil, and_operation_item, primary)
	or_operation_item := ast.And("or_operation_item", nil, and_operation, spaceMaybe, or_operator, spaceMaybe, &or_operation)
	or_operation = ast.OrdChoice("or_operation", nil, or_operation_item, and_operation)
	//
	//primary = ast.OrdChoice("primary", nil, parenthesis, operation_item, compare)
	//root := ast.ManyUntil("root", nil, primary, ast.End("eof"))
	return ast, or_operation
}

// 一个字段是否keyword，如果是的话，一个eq =号也要转换成fulleq ==
func (fqp *ElasticQueryParser) keyIsKeyword(q parsec.Queryable) bool {
	fields := []string{"domain", "port", "protocol", "type"}
	for _, field := range fields {
		if field == q.GetValue() {
			return true
		}
	}
	return false
}

func (fqp *ElasticQueryParser) getTranslateField(q string) string {
	fieldsMap := map[string]string{
		"country":  "geoip.country_code2.raw",
		"city":     "geoip.city_name.raw",
		"region":   "geoip.real_region_name.raw",
		"province": "geoip.real_region_name.raw",
		"type":     "_type",
		"infotype": "_type",
		"after":    "lastupdatetime",
		"before":   "lastupdatetime",
		"isp":      "ipip.isp",
		"asn":      "geoip.country_code2.raw",
		"org":      "geoip.country_code2.raw",
	}
	if v, ok := fieldsMap[q]; ok {
		return v
	}
	return q
}

func (fqp *ElasticQueryParser) getRawField(q parsec.Queryable) string {
	name := q.GetValue()
	//if name == "protocol" {
	//	return name + ".raw"
	//}

	fields := []string{`header`, `server`, `host`, `ip`, `title`, `banner`}
	for _, field := range fields {
		if field == name {
			return field + "." + field + "_raw"
		}
	}
	return name
}

// 需要测试
// 1，中文
// 2，\\ 和 \"
func (fqp *ElasticQueryParser) parseValue(q parsec.Queryable) string {
	if q.GetName() == "DOUBLEQUOTESTRING" {
		//v := q.GetChildren()[1].GetValue()
		//return strings.Trim(v, " ")

		rv := q.GetValue()
		rv = rv[1 : len(rv)-1]
		v := ""
		for i, c := range rv {
			if c == '\\' {
				if rv[i+1] == '\\' || rv[i+1] == '"' {
					v += string(c)
				}
			} else {
				v += string(c)
			}
		}

		return v

		//v, err := strconv.Unquote(q.GetValue())
		//if err != nil {
		//	panic(err)
		//}
		//v = strconv.Quote(v)
		//v = v[1:len(v)-1]

		//return strings.Trim(v, " ")
	}
	return q.GetValue()
}

func (fqp *ElasticQueryParser) parseAst(q []parsec.Queryable) string {
	stringList := make([]string, 0)
	for _, node := range q {
		k := node.GetName()
		v := node.GetValue()

		if k == "missing" {
			continue
		}

		if node.GetName() == "DOUBLEQUOTESTRING" {
			k = node.GetName()
			//nv := node.GetChildren()[1].GetValue()
			v = fqp.parseValue(node)
		} else if len(node.GetChildren()) > 0 {
			v = "(" + fqp.parseAst(node.GetChildren()) + ")"
		}
		stringList = append(stringList, k+":"+v)
	}
	return strings.Join(stringList, " ")
}

func (fqp *ElasticQueryParser) parse(query string) parsec.Queryable {
	r, _ := fqp.AST.Parsewith(fqp.RootParser, parsec.NewScanner([]byte(query)))
	return r
}

// 解析出来left，operation，right
func parseLOR(q []parsec.Queryable) (left, operation, right parsec.Queryable) {
	state := 0 //left operation right
	lor := [3]parsec.Queryable{}

	for _, node := range q {
		k := node.GetName()
		//v := node.GetValue()

		if k == "left_parenthesis" {
			lor[state] = node
			state++
		} else if k == "right_parenthesis" {
			lor[state] = node
			state++
		} else if k == "missing" {

		} else {
			lor[state] = node
			state++
		}
	}
	left, operation, right = lor[0], lor[1], lor[2]
	return
}
