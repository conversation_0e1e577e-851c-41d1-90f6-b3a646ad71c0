package query

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/tdewolff/minify/v2"
	"github.com/tdewolff/minify/v2/json"
)

func MinifyJSON(jsonStr string) string {
	m := minify.New()
	m.AddFuncRegexp(regexp.MustCompile("[/+]json$"), json.Minify)
	v, err := m.String("application/json", jsonStr)
	if err != nil {
		panic(err)
	}
	return v
}

func assertPanic(t *testing.T, f func()) {
	defer func() {
		if r := recover(); r == nil {
			t.<PERSON>("The code did not panic")
		}
	}()
	f()
}

func escapeJsonStr(data string) string {
	data = strings.Replace(data, "\\u0026", "&", -1)
	data = strings.Replace(data, "\\u003c", "<", -1)
	data = strings.Replace(data, "\\u003e", ">", -1)
	data = strings.Replace(data, "\\u003d", "=", -1)
	return data
}

func TestElasticQueryParser_ParseToES6_doublequote(t *testing.T) {
	dqs := regexp.MustCompile(`"((?:\\"|.)*?)"`)

	testStr := `"test"`
	assert.True(t, dqs.MatchString(testStr))
	group := dqs.FindStringSubmatch(testStr)
	assert.Equal(t, "test", group[1])

	testStr = `"test\"test"`
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `test\"test`, group[1])

	testStr = `"test\" \"test"`
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `test\" \"test`, group[1])

	testStr = `"phpBB\>"` //这种情况不允许
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `phpBB\>`, group[1])
}

func TestElasticQueryParser_ParseToES6(t *testing.T) {
	fqp := NewElasticQueryParser()
	assert.Equal(t, "{\"bool\":{\"should\":[{\"match_phrase\":{\"body\":{\"query\":\"test\"}}},{\"match_phrase\":{\"appserver\":{\"query\":\"test\"}}},{\"match_phrase\":{\"cert\":{\"query\":\"test\"}}},{\"match_phrase\":{\"domain\":{\"query\":\"test\"}}},{\"match_phrase\":{\"server\":{\"query\":\"test\"}}},{\"match_phrase\":{\"title\":{\"query\":\"test\"}}},{\"match_phrase\":{\"banner\":{\"query\":\"test\"}}},{\"match_phrase\":{\"header\":{\"query\":\"test\"}}},{\"match_phrase\":{\"host\":{\"query\":\"test\"}}}]}}", MinifyJSON(fqp.ParseToES6("test")))
	assert.Equal(t, `{"bool":{"filter":{"term":{"domain":"huawei.com"}}}}`, MinifyJSON(fqp.ParseToES6(`domain="huawei.com"`)))
	assert.Equal(t, `{"bool":{"filter":{"term":{"port":"80"}}}}`, MinifyJSON(fqp.ParseToES6(`port=80`)))
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"host":{"query":"huawei.com"}}}}}`, MinifyJSON(fqp.ParseToES6(`host="huawei.com"`)))
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"huawei.com"}}}}}`, MinifyJSON(fqp.ParseToES6(`title="huawei.com"`)))
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"header":{"query":"huawei.com"}}}}}`, MinifyJSON(fqp.ParseToES6(`header="huawei.com"`)))
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"huawei.com"}}}}}`, MinifyJSON(fqp.ParseToES6(`body="huawei.com"`)))
	assert.Equal(t, `{"bool":{"must":[{"bool":{"filter":{"match_phrase":{"body":{"query":"sina.com"}}}}},{"bool":{"filter":{"term":{"domain":"baidu.com"}}}}]}}`, MinifyJSON(fqp.ParseToES6(`body="sina.com" && domain="baidu.com"`)))
	assert.Equal(t, `{"bool":{"should":[{"bool":{"filter":{"match_phrase":{"body":{"query":"sina.com"}}}}},{"bool":{"filter":{"term":{"domain":"baidu.com"}}}}]}}`, MinifyJSON(fqp.ParseToES6(`body="sina.com" || domain="baidu.com"`)))
	assert.Equal(t, `{"bool":{"must_not":{"term":{"port":"80"}}}}`, MinifyJSON(fqp.ParseToES6(`port!=80"`)))
	assert.Equal(t, `{"bool":{"must":[{"bool":{"filter":{"term":{"domain":"huawei.com"}}}},{"bool":{"must_not":{"match_phrase":{"host":{"query":"www"}}}}}]}}`, MinifyJSON(fqp.ParseToES6(`domain="huawei.com" && host!="www"`)))

	assert.Equal(t, `{"bool":{"should":[{"bool":{"must":[{"bool":{"filter":{"term":{"domain":"huawei.com"}}}},{"bool":{"must_not":{"match_phrase":{"host":{"query":"www"}}}}}]}},{"bool":{"must":[{"bool":{"filter":{"match_phrase":{"body":{"query":"baidu.com"}}}}},{"bool":{"must_not":{"term":{"domain":"baidu.com"}}}}]}}]}}`,
		MinifyJSON(fqp.ParseToES6(`(domain="huawei.com" && host!="www") || (body="baidu.com" && domain!="baidu.com")`)))

	//引号测试
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"test\"abc"}}}}}`, MinifyJSON(fqp.ParseToES6(`title="test\"abc"`)))

	//-符号测试
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"test abc - "}}}}}`, MinifyJSON(fqp.ParseToES6(`title="test abc - "`)))

	//空格测试，去掉前后空格
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"GpsGate Server  "}}}}}`, MinifyJSON(fqp.ParseToES6(`title="GpsGate Server  "`)))

	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"window.location.href=\"main.html?sid="}}}}}`, MinifyJSON(fqp.ParseToES6(`body="window.location.href=\"main.html?sid="`)))

	//转义只允许\\和\",其他的不允许
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":">"}}}}}`, MinifyJSON(fqp.ParseToES6(`body="\>"`)))

	// \" 还是 \\\" 原来的ruby代码为什么要替换？ 应该是之前入库没考虑转义，后来入规则的时候都考虑了

	// < 还是 \u003c 原来的ruby代码为什么要替换？直接的to_json没有问题，但是rails的to_json会进行替换
}

func Test_ParseToES62_1(t *testing.T) {
	fqp := NewElasticQueryParser()
	es6 := fqp.ParseToES6("body=\"ExtWebModels\" || body=\"var vdomainname=\\\"http://\\\"+aUrls[2]+\\\"\"")
	fmt.Printf("%s\n", es6)
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"phpBB>"}}}}}`, MinifyJSON(fqp.ParseToES6(`body="phpBB\>"`)))
}

func TestElasticQueryParser_ParseToES62_add(t *testing.T) {
	fqp := NewElasticQueryParser()
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"a + b"}}}}}`, MinifyJSON(fqp.ParseToES6(`body="a + b"`)))
}

func TestElasticQueryParser_ParseToES62_Chinese(t *testing.T) {
	fqp := NewElasticQueryParser()
	assert.Equal(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"测试\"\abc"}}}}}`, MinifyJSON(fqp.ParseToES6(`body="测试\"\\abc"`)))
}

func TestElasticQueryParser_ParseToES62_Type(t *testing.T) {
	fqp := NewElasticQueryParser()
	assert.Equal(t, "{\"bool\":{\"filter\":{\"term\":{\"_type\":\"subdomain\"}}}}", MinifyJSON(fqp.ParseToES6(`type="subdomain"`)))
}

/*
```bash
rails c
> File.write "a.txt", RuleCount.taged.map{|r| %Q|#{r.rule} ^^^ #{JSON.parse(ElasticFilterProcessor.parse(r.rule)).to_json}| }.join("\n")

```
*/
func TestElasticQueryParser_ParseToES6_FromFile(t *testing.T) {
	file, err := os.Open("a.txt")
	if err != nil {
		log.Println(err)
		return
	}
	defer file.Close()

	fqp := NewElasticQueryParser()
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		kv := strings.SplitN(scanner.Text(), " ^^^ ", 2)
		//fmt.Println(kv[0])
		assert.Equal(t, escapeJsonStr(kv[1]), MinifyJSON(fqp.ParseToES6(kv[0])))
	}

	if err := scanner.Err(); err != nil {
		log.Fatal(err)
	}
}
