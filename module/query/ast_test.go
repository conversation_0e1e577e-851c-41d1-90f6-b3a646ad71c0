package query

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestElasticQueryParser_ParseToText(t *testing.T) {
	fqp := NewElasticQueryParser()
	assert.Equal(t, "literal:domain eq:= null:null", fqp.ParseToText("domain=null"))
	assert.Equal(t, "literal:domain eq:= true:true", fqp.ParseToText("domain=true"))
	assert.Equal(t, "literal:domain eq:= false:false", fqp.ParseToText("domain=false"))
	assert.Equal(t, "literal:domain eq:= number:1.0", fqp.ParseToText("domain=1.0"))
	assert.Equal(t, "literal:domain eq:= number:-2", fqp.ParseToText("domain=-2"))
	assert.Equal(t, "literal:domain eq:= number:0.1", fqp.ParseToText("domain=0.1"))
	assert.Equal(t, "literal:domain eq:= literal:a.com", fqp.ParseToText("domain=a.com"))
	assert.Equal(t, "literal:domain eq:= DOUBLEQUOTESTRING:a.com", fqp.ParseToText(`domain="a.com"`))
	assert.Equal(t, `literal:domain eq:= DOUBLEQUOTESTRING:a\"com`, fqp.ParseToText(`domain="a\"com"`))
	assert.Equal(t, `literal:domain eq:= DOUBLEQUOTESTRING:a\\\"com`, fqp.ParseToText(`domain="a\\\"com"`))
	assert.Equal(t, "literal:domain eq:= literal:baidu.com", fqp.ParseToText("domain= baidu.com"))                  //空格测试
	assert.Equal(t, "literal:domain eq:= DOUBLEQUOTESTRING:baidu.com", fqp.ParseToText(`domain= "baidu.com"`))      //空格测试
	assert.Equal(t, "literal:domain not_eq:!= DOUBLEQUOTESTRING:baidu.com", fqp.ParseToText(`domain!="baidu.com"`)) //!=测试
	assert.Equal(t, "compare_eq:(literal:domain eq:= DOUBLEQUOTESTRING:a.com) and_operator:&& compare_not_eq:(literal:host not_eq:!= DOUBLEQUOTESTRING:a.a.com)", fqp.ParseToText(`domain="a.com" && host!="a.a.com"`))
}
