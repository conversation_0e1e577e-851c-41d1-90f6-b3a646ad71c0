package main

import (
	"git.gobies.org/foeye-dependencies/logger"
	versions "git.gobies.org/foeye-dependencies/version"
	flag "github.com/spf13/pflag"
)

var (
	AppName    string
	Version    string
	GoVersion  string
	GitCommit  string
	GitBranch  string
	DestOSARCH string
	FromOS<PERSON><PERSON> string
	BuildTime  string
)

var version *versions.Version
var flagRawVersion bool
var flagJSONVersion bool

func init() {
	version = &versions.Version{
		AppName:    AppName,
		Version:    Version,
		GoVersion:  GoVersion,
		GitCommit:  GitCommit,
		GitBranch:  GitBranch,
		DestOSARCH: DestOSARCH,
		FromOSARCH: FromOSARCH,
		BuildTime:  BuildTime,
	}

	flag.BoolVar(&flagRawVersion, "version", false, "Show the Docker version information")
	flag.BoolVar(&flagJSONVersion, "json", false, "Show the Docker version information, use json format")

	logger.Configuration(logger.WithOutput("app.log"))
}
