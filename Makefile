Go ?= go

branch=$(shell git rev-parse --abbrev-ref HEAD | grep -v HEAD || git describe --exact-match HEAD || git rev-parse HEAD)
project=foeye-engine-syncasset

dependence:
	$(Go) mod tidy
	$(Go) mod vendor

# Cli application build.
.PHONY: test
test: dependence
	$(Go) test -gcflags=all=-l -race -p 1 ./... -coverprofile=coverage.out -v 2>&1 | tee source.out

.PHONY: coverage
coverage:
	cat source.out | go-junit-report -set-exit-code > junit.xml
	$(Go) tool cover -func=coverage.out
	gocover-cobertura < coverage.out > cobertura.xml
	go tool cover -html=coverage.out -o coverage-report.html


# Cli application install.
.PHONY: install
install: dependence
	$(Go) install .

# Cli application build.
.PHONY: build
build: dependence
	$(Go) build .

build-amd64: dependence
	GOOS=linux GOARCH=amd64 go build -o $(project)

build-arm64: dependence
	GOOS=linux GOARCH=arm64 go build -o $(project)

deploy: build-amd64
	cp $(project) $(project).linux.amd64 && scp ./$(project).linux.amd64 root@10.11.12.38:/root/helloshaohua/hot/$(project)/

deploy-amd64: build-amd64
	/bin/sh ./deploy.sh $(project) $(branch) amd64

deploy-arm64: build-arm64
	/bin/sh ./deploy.sh $(project) $(branch) arm64
