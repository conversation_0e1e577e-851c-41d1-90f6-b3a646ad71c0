package exchange

type SystemInfoResponseData struct {
	Version          string `json:"version"`
	LicenseState     bool   `json:"license_state"`
	AssetLimitNum    int    `json:"asset_limit_num"`
	ProductLimitDate string `json:"product_limit_date"`
	UpgradeLimitDate string `json:"upgrade_limit_date"`
}

type SystemInfoResponse struct {
	StatusCode int         `json:"status_code"`
	Messages   string      `json:"messages"`
	Data       *SystemInfo `json:"data"`
}
