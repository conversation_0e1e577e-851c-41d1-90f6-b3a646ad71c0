package exchange

// SystemInfo system information structure.
type SystemInfo struct {
	Version          string `json:"version"`            //版本号
	LicenseState     bool   `json:"license_state"`      //license激活状态（true为激活）
	AssetLimitNum    int    `json:"asset_limit_num"`    //资产限制数
	ProductLimitDate string `json:"product_limit_date"` //产品试用截止时间
	UpgradeLimitDate string `json:"upgrade_limit_date"` //升级截止时间
}
