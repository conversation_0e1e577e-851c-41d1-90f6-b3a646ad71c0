package exchange

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestTaskScanInfoSuite(t *testing.T) {
	suite.Run(t, &TaskScanInfoSuite{})
}

type TaskScanInfoSuite struct {
	suite.Suite
	info *TaskScanInfo
}

func (suite *TaskScanInfoSuite) BeforeTest(suiteName, testName string) {
	suite.info = NewTaskScanInfo(1)
}

func (suite *TaskScanInfoSuite) Test_Info_Detect_TaskId() {
	assert.Equal(suite.T(), 1, suite.info.TaskId)
}

func (suite *TaskScanInfoSuite) Test_Info_ToMap() {
	expected := "{\"percent\":0,\"progress\":0,\"tagging_assets_progress\":0,\"task_id\":1}"
	toMap := suite.info.ToMap()
	data, err := json.Marshal(&toMap)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), expected, string(data))
}
