package exchange

import (
	"fmt"
	"strconv"
)

type Port struct {
	Id int `json:"id"`

	ScanPortId int `json:"scan_port_id"`
	ProtocolId int `json:"protocol_id"`
	Port       int `json:"port"`

	Name     string `json:"name"`
	Category string `json:"category"`
	AddWay   string `json:"add_way"`
}

func (tsp *Port) CustomUserPort(category string) string {
	return fmt.Sprintf("%s:%s:%s", category, strconv.Itoa(tsp.Port), tsp.Name)
}
