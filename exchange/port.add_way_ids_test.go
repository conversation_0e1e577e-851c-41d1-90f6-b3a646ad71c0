package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestAddWayPortIdsSuite(t *testing.T) {
	suite.Run(t, new(AddWayPortIdsSuite))
}

type AddWayPortIdsSuite struct {
	suite.Suite

	ids *AddWayPortIds
}

func (suite *AddWayPortIdsSuite) BeforeTest(suiteName, testName string) {
	suite.ids = NewPortIds()
	suite.ids.SetSystem([]int{1, 2, 3})
	suite.ids.SetOther([]int{4, 5, 6})
}

func (suite *AddWayPortIdsSuite) Test_NotNil() {
	assert.NotNil(suite.T(), suite.ids)
}
