package exchange

import (
	"encoding/json"
	"strings"

	"git.gobies.org/foeye-dependencies/address"
)

type ParsedIPRange struct {
	IPRangeCIDRs address.IPRangeCIDRs `json:"ip_range_cidrs"`
	Hosts        Hosts                `json:"hosts"`
}

func (parsed *ParsedIPRange) String() string {
	marshal, _ := json.Marshal(&parsed)
	return string(marshal)
}

func (parsed *ParsedIPRange) IsEmpty() bool {
	if parsed != nil {
		return parsed.IPRangeCIDRs.IsEmpty()
	}
	return true
}

type IPRanges []string

func (ipranges IPRanges) String() string {
	return strings.Join(ipranges, ",")
}
