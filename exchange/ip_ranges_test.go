package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestIPRangesSuite(t *testing.T) {
	suite.Run(t, &IPRangesSuite{})
}

type IPRangesSuite struct {
	suite.Suite
	iprange  *ParsedIPRange
	ipranges IPRanges
}

func (suite *IPRangesSuite) BeforeTest(suiteName, testName string) {
	suite.iprange = &ParsedIPRange{IPRangeCIDRs: []string{"**********/24"}}
	suite.ipranges = IPRanges{"**********/24", "**********/24"}
}

func (suite *IPRangesSuite) Test_ParsedIPRange() {
	actual := suite.iprange.IsEmpty()
	assert.Equal(suite.T(), false, actual)

	suite.iprange = &ParsedIPRange{IPRangeCIDRs: []string{}}
	actual = suite.iprange.IsEmpty()
	assert.Equal(suite.T(), true, actual)
}

func (suite *IPRangesSuite) Test_ParsedIPRange_String() {
	actual := suite.iprange.String()
	assert.Equal(suite.T(), "{\"ip_range_cidrs\":[\"**********/24\"],\"hosts\":null}", actual)
}

func (suite *IPRangesSuite) Test_IPRanges_String() {
	actual := suite.ipranges.String()
	assert.Equal(suite.T(), "**********/24,**********/24", actual)
}
