package exchange

import (
	"encoding/json"

	"github.com/olivere/elastic"
)

type AggregationBucket struct {
	Key      int `json:"key"`
	DocCount int `json:"doc_count"`
}

type AggregationBuckets []AggregationBucket

func (agg AggregationBuckets) TotalDocCount() int {
	var total int
	for _, ag := range agg {
		total += ag.DocCount
	}
	return total
}

type Aggregation struct {
	DocCountErrorUpperBound int                `json:"doc_count_error_upper_bound"`
	SumOtherDocCount        int                `json:"sum_other_doc_count"`
	Buckets                 AggregationBuckets `json:"buckets"`
}

func GetAggregation(aggregations elastic.Aggregations, name string) *Aggregation {
	if aggregation, exists := aggregations[name]; exists {
		data, err := aggregation.MarshalJSON()
		if err != nil {
			return nil
		}

		var r *Aggregation
		if err = json.Unmarshal(data, &r); err != nil {
			return nil
		}

		return r
	}
	return nil
}
