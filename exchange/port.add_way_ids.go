package exchange

import "sync"

// AddWayPortIds system ports structure.
type AddWayPortIds struct {
	System []int `json:"system"` // System preset ports.
	Other  []int `json:"other"`  // Other ports.
	access sync.RWMutex
}

func (p *AddWayPortIds) SetSystem(ports []int) {
	p.access.Lock()
	defer p.access.Unlock()

	p.System = ports
}

func (p *AddWayPortIds) SetOther(ports []int) {
	p.access.Lock()
	defer p.access.Unlock()

	p.Other = ports
}

func NewPortIds() *AddWayPortIds {
	return &AddWayPortIds{}
}
