package exchange

import "encoding/json"

// TaskScanInfo task scan information.
type TaskScanInfo struct {
	TaskId                int     `json:"task_id"`
	Percent               float64 `json:"percent"`
	Progress              float64 `json:"progress"`
	TaggingAssetsProgress float64 `json:"tagging_assets_progress"`
}

// NewTaskScanInfo return a new TaskScanInfo instance.
func NewTaskScanInfo(taskId int) *TaskScanInfo {
	return &TaskScanInfo{TaskId: taskId}
}

// ToMap convert task scan info to map.
func (info *TaskScanInfo) ToMap() map[string]interface{} {
	data, err := json.Marshal(&info)
	if err != nil {
		return nil
	}

	var r map[string]interface{}
	err = json.Unmarshal(data, &r)
	if err != nil {
		return nil
	}

	return r
}
