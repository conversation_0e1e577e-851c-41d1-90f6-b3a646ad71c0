package main

import (
	"context"
	"embed"
	"fmt"
	"os"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/server"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/logger"
	flag "github.com/spf13/pflag"
)

//go:embed embeds
var embeds embed.FS

func main() {
	flag.Parse()
	if flagRawVersion && !flagJSONVersion {
		fmt.Println(version.RawString())
		os.Exit(0)
	}

	if flagRawVersion && flagJSONVersion {
		fmt.Println(version.JSONString())
		os.Exit(0)
	}

	conf := config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("."),
		configure.WithSpecificConfigName("conf"),
	)

	logger.Infow("configure info", "detail", conf)
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	ops := []server.Option{
		server.WithContext(ctx),
		server.WithCancelFunc(cancel),
	}
	server.NewServer(".", conf, embeds, ops...).Run()
}
