package server

import (
	"context"
	"embed"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"

	"git.gobies.org/foeye-dependencies/configure"
	"github.com/stretchr/testify/suite"
)

//go:embed embeds
var embeds embed.FS

func TestServerSuite(t *testing.T) {
	suite.Run(t, &ServerSuite{})
}

type ServerSuite struct {
	suite.Suite
	server    *Server
	configure *config.Configure
	baseDir   string
}

func (suite *ServerSuite) BeforeTest(suiteName, testName string) {
	suite.baseDir = "./../"
	suite.configure = config.GetConfigure(configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath(suite.baseDir),
		configure.WithSpecificConfigName("conf.test"),
	)

	cancel, cancelFunc := context.WithDeadline(context.Background(), time.Now().Add(5*time.Second))
	defer cancelFunc()

	ops := []Option{
		WithContext(cancel),
		WithCancelFunc(cancelFunc),
	}

	suite.server = NewServer(suite.baseDir, suite.configure, embeds, ops...)
	assert.NotNil(suite.T(), suite.server)
}

func (suite *ServerSuite) Test_NotNil() {
	assert.NotNil(suite.T(), suite.server)
	suite.server.Run()
}
