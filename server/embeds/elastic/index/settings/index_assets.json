{"settings": {"index": {"mapping": {"total_fields": {"limit": "1000"}}, "number_of_shards": "5", "blocks": {"read_only_allow_delete": "false"}, "max_result_window": "10000000", "analysis": {"analyzer": {"fofa_dot_analyzer": {"type": "custom", "char_filter": ["fofa_dot_to_space"], "tokenizer": "fofa_dot_tokenizer"}, "case_sensitive": {"filter": "lowercase", "type": "custom", "tokenizer": "keyword"}}, "char_filter": {"fofa_dot_to_space": {"pattern": "[.:/]", "type": "pattern_replace", "replacement": " "}}, "tokenizer": {"fofa_dot_tokenizer": {"type": "whitespace"}}}, "number_of_replicas": "1"}}, "mappings": {"ips": {"dynamic_templates": [{"string_fields": {"match": "*", "match_mapping_type": "string", "mapping": {"index": true, "norms": false, "type": "keyword"}}}], "properties": {"DNS": {"type": "keyword"}, "ID": {"type": "integer"}, "add_way": {"type": "keyword"}, "asset_no": {"type": "keyword"}, "belong_user_id": {"type": "integer"}, "business_app": {"type": "keyword"}, "cat_tags": {"type": "keyword"}, "city": {"type": "keyword"}, "company": {"type": "keyword"}, "company_tags": {"type": "keyword"}, "computer_room": {"type": "keyword"}, "country": {"type": "keyword"}, "createtime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "descriptions": {"type": "keyword"}, "domain": {"type": "keyword"}, "first_tag_num": {"type": "nested", "properties": {"first_cat_tag": {"type": "keyword"}, "hardware_num": {"type": "long"}, "num": {"type": "long"}, "software_num": {"type": "long"}}}, "gid": {"type": "keyword"}, "group_name": {"type": "keyword"}, "hardware_num": {"type": "integer"}, "host": {"type": "keyword"}, "hosts": {"type": "keyword"}, "ip": {"type": "ip", "fields": {"ip_raw": {"type": "keyword"}, "ipstr": {"type": "text", "analyzer": "fofa_dot_analyzer"}}}, "is_ipv6": {"type": "boolean"}, "lastchecktime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "lastupdatetime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "mac": {"type": "keyword"}, "manager_email": {"type": "keyword"}, "manager_mobile": {"type": "keyword"}, "name": {"type": "keyword"}, "net_bios": {"type": "keyword"}, "netbios_name": {"type": "keyword"}, "operating_company": {"type": "keyword"}, "operator": {"type": "keyword"}, "os": {"type": "keyword"}, "port_list": {"properties": {"banner": {"type": "keyword"}, "certs": {"properties": {"domain": {"type": "keyword"}, "issuer_cn": {"type": "keyword"}, "issuer_org": {"type": "keyword"}, "not_after": {"type": "keyword"}, "not_before": {"type": "keyword"}, "sig_alth": {"type": "keyword"}, "sn": {"type": "keyword"}, "subject_cn": {"type": "keyword"}, "subject_org": {"type": "keyword"}, "v": {"type": "keyword"}}}, "port": {"type": "long"}, "protocol": {"type": "keyword"}}}, "port_size": {"type": "long"}, "ports": {"type": "keyword"}, "protocols": {"type": "keyword"}, "province": {"type": "keyword"}, "rule_infos": {"properties": {"belong_level": {"type": "long"}, "company": {"type": "keyword"}, "country_code": {"type": "long"}, "first_cat_tag": {"type": "keyword"}, "level_code": {"type": "long"}, "ports": {"type": "long"}, "price": {"type": "keyword"}, "release_date": {"type": "long"}, "rule_id": {"type": "long"}, "second_cat_tag": {"type": "keyword"}, "soft_hard_code": {"type": "long"}, "title": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "case_sensitive", "fielddata": true}}}, "rule_tags": {"type": "keyword"}, "second_tag_num": {"type": "nested", "properties": {"hardware_num": {"type": "long"}, "num": {"type": "long"}, "second_cat_tag": {"type": "keyword"}, "software_num": {"type": "long"}}}, "snmp_ips": {"type": "keyword"}, "software_num": {"type": "integer"}, "state": {"type": "keyword"}, "tags": {"type": "text"}, "title_list": {"properties": {"host": {"type": "keyword"}, "port": {"type": "long"}, "title": {"type": "keyword"}}}, "username": {"type": "keyword"}, "versions": {"type": "keyword"}, "vlan": {"type": "keyword"}, "vuls": {"properties": {"filename": {"type": "keyword"}, "level": {"type": "long"}, "name": {"type": "keyword"}, "type": {"type": "keyword"}}}, "vuls_count": {"type": "long"}}}}}