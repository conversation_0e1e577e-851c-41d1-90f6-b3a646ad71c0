package server

import (
	"context"
	"time"
)

type Option interface {
	apply(*options)
}

type optionFunc func(*options)

func (o optionFunc) apply(ops *options) {
	o(ops)
}

type options struct {
	wait   time.Duration
	ctx    context.Context
	cancel context.CancelFunc
}

func WithContext(ctx context.Context) Option {
	return optionFunc(func(ops *options) {
		ops.ctx = ctx
	})
}

func WithCancelFunc(cancel context.CancelFunc) Option {
	return optionFunc(func(ops *options) {
		ops.cancel = cancel
	})
}

func WithWaitTimeout(wait time.Duration) Option {
	return optionFunc(func(ops *options) {
		ops.wait = wait
	})
}
