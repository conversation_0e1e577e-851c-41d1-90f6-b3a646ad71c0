package server

import (
	"context"
	"os"
	"os/signal"

	"git.gobies.org/foeye-dependencies/logger"
)

func (server *Server) Run() {
	server.prepare()

	signal.Notify(server.signal, server.signals...)

	// Context control monitor.
	go func(server *Server) {
		called := <-server.signal
		logger.Infof("system called [%s] application will exit", called)
		server.options.cancel()
	}(server)

	logger.Infof("application run pid [%d]", os.Getpid())
	<-server.options.ctx.Done()

	// Wait
	_, cancel := context.WithTimeout(context.Background(), server.options.wait)
	defer func() {
		cancel()
	}()

	logger.Infof("server exited properly~")
}
