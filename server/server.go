package server

import (
	"context"
	"embed"
	"fmt"
	"os"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/queue"
	"git.gobies.org/foeye/foeye-engine-syncasset/store/storage"
	"git.gobies.org/foeye/foeye-engine-syncasset/task"

	"git.gobies.org/foeye-dependencies/embedsfs"
	"git.gobies.org/foeye-dependencies/logger"
)

type Server struct {
	task    *task.Task
	ctx     context.Context
	mutex   sync.Mutex
	options *options
	signal  chan os.Signal
	signals []os.Signal
}

func (server *Server) prepare() *Server {
	server.task.SyncAssetStart(server.options.ctx)

	logger.Infow("[TASKQUEUE]be ready")
	go server.task.Queue().Run()
	return server
}

func NewServer(baseDir string, configure *config.Configure, embeds embed.FS, ops ...Option) *Server {
	options := &options{}

	for _, o := range ops {
		o.apply(options)
	}

	if options.ctx == nil && options.cancel == nil {
		options.ctx, options.cancel = context.WithCancel(context.Background())
	}

	sidekip, err := queue.NewSidekipOr(configure)
	if err != nil {
		panic(any(err))
	}

	fs := embedsfs.NewEmbedsFS(embeds, embedsfs.WithPath("embeds"))
	if fs == nil {
		panic(any(fmt.Errorf("loading embeds filesystem file tailed")))
	}

	store := storage.NewStorage(configure, embeds)
	tasker := task.NewTask(baseDir, configure, store, sidekip, fs)
	tasker.Queue().SetContext(options.ctx)
	tasker.Queue().SetCancel(options.cancel)

	s := &Server{
		task:    tasker,
		options: options,
		signal:  make(chan os.Signal, 1),
		signals: signals,
	}

	return s
}
