# Editor files.
.idea

# Binaries for programs and plugins
*.tmp*
*.tmp.*
*.exe
*.exe~
*.dll
*.so
*.dylib
bu*.*
cm*
mosso.*
foeye-engine-syncasset

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/
.vscode/

# Build and compile files.
build/compile/*
build/archive/*
!build/compile/.keep
!build/archive/.keep

# Configure file.
conf.toml

# Local scripts.
test/data/task/val/*
test/create

# Log files.
logs
app.log

# Test files.
test/logs
test/task
test/scripts

# Backup files.
*.bak
conf.test.toml
