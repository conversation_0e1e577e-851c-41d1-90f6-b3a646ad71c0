package config

import (
	"testing"

	"git.gobies.org/foeye-dependencies/configure"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestConfigSuite(t *testing.T) {
	suite.Run(t, &ConfigSuite{})
}

type ConfigSuite struct {
	suite.Suite
	configure *Configure
	runMode   RunMode
}

func (suite *ConfigSuite) BeforeTest(suiteName, testName string) {
	suite.configure = GetConfigure(configure.WithSpecificConfigure(&Configure{}),
		configure.WithSpecificConfigPath("./../"),
		configure.WithSpecificConfigName("conf.test"),
	)

	assert.NotNil(suite.T(), conf)
}

func (suite *ConfigSuite) Test_Configure_Default() {
	assert.NotNil(suite.T(), suite.configure.Default())
}

func (suite *ConfigSuite) Test_RunMode_Default() {
	suite.runMode = RunModeOfDevelop
	assert.Equal(suite.T(), suite.runMode.String(), "develop")
}
