package config

import "time"

type (
	Configure struct {
		System  *System
		Redis   *Redis
		MYSQL   *MYSQL
		Elastic *Elastic
	}

	System struct {
		RunMode        RunMode
		LogOut         []string
		BatchSize      int
		DataMigrateDir string
	}

	Redis struct {
		Host           string
		Password       string
		Database       int
		TaskQueue      string
		TaskQueueClass string
	}

	Elastic struct {
		Host         string
		AutoMigrate  bool
		ShowTraceLog bool
	}

	MYSQL struct {
		Host                  string
		Username              string
		Password              string
		Database              string
		MaxIdleConnections    int
		MaxOpenConnections    int
		MaxConnectionLifeTime time.Duration
		LogLevel              int
		DriverName            string
		AutoMigrate           bool
	}
)

func (configure *Configure) Default() *Configure {
	if configure != nil {
		if configure.System.BatchSize == 0 {
			configure.System.BatchSize = 20
		}

		if configure.System.DataMigrateDir == "" {
			configure.System.DataMigrateDir = "data/trace"
		}
	}

	return configure
}
