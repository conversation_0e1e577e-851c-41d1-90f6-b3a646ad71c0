package store

import "gorm.io/gorm"

var mysqlClient MySQLFactory

type MySQLFactory interface {
	Task() TaskStore
	ScanPortTemplate() ScanPortTemplateStore
	IPRange() IPRangeStore
	IPRangeTag() IPRangeTagStore
	Tag() TagStore
	ProtocolScanPort() ProtocolScanPortStore
	TaskIPRangeCondition() TaskIPRangeConditionStore
	Gorm() *gorm.DB
	Close() error
}

// MySQLClient return the store mysqlClient instance.
func MySQLClient() MySQLFactory {
	return mysqlClient
}

// SetMySQLClient return the store mysqlClient instance.
func SetMySQLClient(factory MySQLFactory) {
	mysqlClient = factory
}
