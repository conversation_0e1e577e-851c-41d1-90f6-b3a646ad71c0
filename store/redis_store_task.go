package store

import (
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
)

type TaskRedisStore interface {
	SetStringValue(key string, value interface{}, expiration time.Duration) (string, error)
	DeleteKey(key string) (bool, error)
	KeyExists(key string) bool

	GetTaskVulnerabilityKeyWithTaskId(taskId int) string
	DelTaskVulnerabilityKeyWithTaskId(taskId int) bool
	SetHashValueOfTaskVulnerability(taskId int, fields map[string]interface{}) error

	SetForceUpdateProgress(taskId int, progress float64) (bool, error)
	GetTaskQuickTransferStateValue() int
	// SetHashScanTaskValue() error
	SetStringTaskIncreaseValue() (string, error)
	SetStringCurrentScanInfoValue() (string, error)
	DelStringCurrentScanInfoValue() (bool, error)
	SetHashValue(key string, fields map[string]interface{}) error
	GetHashFieldValue(key string, field string) (string, error)
	// Deprecated
	// GetHashScanTaskFieldValueOfOldProgress() float32

	GetSingleIPAddressKey(taskId int) string
	DeleteSingleIPAddressKey(taskId int) (bool, error)
	SetSingleIPAddressKey(taskId int, isSingle bool) error
	SingleIPAddressKeyExists(taskId int) bool

	TaskScanInfoKey(taskId int) string
	InitTaskScanInfo(taskId int) error
	SetTaskScanInfo(taskId int, field string, value interface{}) error
	SetTaskScanInfoOfPercent(taskId int, value interface{}) error
	SetTaskScanInfoOfProgress(taskId int, value interface{}) error
	SetTaskScanInfoOfTaggingAssetsProgress(taskId int, value interface{}) error
	TaskScanInfo(taskId int) (*exchange.TaskScanInfo, error)
	GetTaskScanInfoProgress(taskId int) float64
	GetTaskScanInfoPercent(taskId int) float64
	GetTaskScanInfoTaggingAssetsProgress(taskId int) float64

	SetForbiddenDispatchTimeWindow(duration time.Duration) error
	ForbiddenDispatchTimeWindowExists() bool
}
