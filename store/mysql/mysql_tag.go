package mysql

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"gorm.io/gorm"
)

type tag struct {
	db *gorm.DB
}

func newTag(store *datastore) *tag {
	return &tag{db: store.db}
}

func (store *tag) Create(ctx context.Context, data *models.Tag) error {
	return store.db.Create(&data).Error
}

func (store *tag) CreateBatch(ctx context.Context, data []*models.Tag) error {
	return store.db.CreateInBatches(&data, 100).Error
}
