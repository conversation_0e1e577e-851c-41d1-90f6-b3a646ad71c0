package mysql

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"

	"github.com/stretchr/testify/assert"
)

func (suite *MySQLSuite) Test_Task_Create() {
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "task.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	var r *models.Task
	err = json.Unmarshal(data, &r)
	assert.NoError(suite.T(), err)
	r.Id = 10000
	err = suite.store.Task().Create(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_Task_38_CreateBatch() {
	suite.mockFixed.SetConvertTemplate(mock.TaskMockDataConvertTemplate)

	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "38/tasks.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.Task
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.Task().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_Task_List() {
	actual, err := suite.store.Task().List(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.Len(suite.T(), actual, 5)
}

func (suite *MySQLSuite) Test_Task_Detail() {

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *MySQLSuite) Test_Task_UpdateThreatNumber() {

	err := suite.store.Task().UpdateThreatNumber(suite.ctx, 3, 55)
	assert.NoError(suite.T(), err)

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.Equal(suite.T(), 55, actual.ThreatNum)
}

func (suite *MySQLSuite) Test_Task_Update() {

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)

	err = suite.store.Task().Update(suite.ctx, 3, map[string]interface{}{
		"state":         models.TaskStateOfFinished,
		"real_end_time": time.Now(),
		"use_seconds":   int(time.Now().Sub(*actual.RealBeginTime).Seconds()),
		"asset_num":     80,
		"rule_num":      345,
	})
	assert.NoError(suite.T(), err)

	actual, err = suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *MySQLSuite) Test_Task_UpdateScanStep() {

	err := suite.store.Task().UpdateScanStep(suite.ctx, 3, models.ScanStepOfReport)
	assert.NoError(suite.T(), err)

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.Equal(suite.T(), models.ScanStep(`report`), actual.ScanStep)
}

func (suite *MySQLSuite) Test_Task_UpdateUseSeconds() {

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)

	sub := actual.RealEndTime.Sub(*actual.RealBeginTime)
	err = suite.store.Task().UpdateUseSeconds(suite.ctx, 3, sub)
	assert.NoError(suite.T(), err)

	actual, err = suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.NotEmpty(suite.T(), actual.UseSeconds)
}

func (suite *MySQLSuite) Test_Task_UpdateState() {

	err := suite.store.Task().UpdateState(suite.ctx, 3, models.TaskStateOfAutoPausing)
	assert.NoError(suite.T(), err)

	actual, err := suite.store.Task().Detail(suite.ctx, 3)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.Equal(suite.T(), models.TaskState(4), actual.State)
}

func (suite *MySQLSuite) Test_Task_CurrentTask() {
	task, err := suite.store.Task().CurrentTask(suite.ctx)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), task)
}

func (suite *MySQLSuite) Test_Task_Count_NoData() {
	total, err := suite.store.Task().Count(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, total)
}

func (suite *MySQLSuite) Test_Task_Count_HasData() {
	total, err := suite.store.Task().Count(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, total)
}

func (suite *MySQLSuite) Test_Task_Delete() {

	total, err := suite.store.Task().Count(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 5, total)

	err = suite.store.Task().Delete(suite.ctx, 14)
	assert.NoError(suite.T(), err)

	total, err = suite.store.Task().Count(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 5, total)
}

func (suite *MySQLSuite) Test_Task_GetTaskIPRangeTags() {
	_, err := suite.store.Task().GetTaskIPRangeTags(suite.ctx, []int{1, 2, 3, 4, 5})
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_Task_GetIPRangeIdByTaskId() {
	tags, err := suite.store.Task().GetIPRangeIdByTaskId(suite.ctx, 2)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), tags, 1)

	tags, err = suite.store.Task().GetIPRangeIdByTaskId(suite.ctx, 4)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), tags, 2)
}
