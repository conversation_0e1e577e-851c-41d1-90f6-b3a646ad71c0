package mysql

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"

	"github.com/stretchr/testify/assert"
)

func (suite *MySQLSuite) Test_ProtocolScanPort_CreateBatch() {
	// suite.drop = false
	suite.mockFixed.SetConvertTemplate(mock.DefaultTimeFieldsConvertTemplate)

	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "protocols_scan_ports.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.ProtocolScanPort
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.ProtocolScanPort().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}
