package mysql

import (
	"context"
	"testing"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
)

func TestMySQLSuite(t *testing.T) {
	suite.Run(t, &MySQLSuite{})
}

type MySQLSuite struct {
	suite.Suite
	store       store.MySQLFactory
	drop        bool
	ctx         context.Context
	cancel      context.CancelFunc
	baseDir     string
	testDataDir string
	mockFixed   *mock.MySQLMockFixed
}

func (suite *MySQLSuite) SetupSuite() {
	suite.drop = true
	suite.baseDir = "./../../"
	suite.mockFixed = mock.NewMySQLFixedMock()

	ops := []configure.Option{
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("conf.test"),
	}

	conf := config.GetConfigure(ops...)
	assert.NotNil(suite.T(), conf)

	var err error
	suite.store, err = GetMySQLFactoryOr(conf)
	assert.NoError(suite.T(), err)

	suite.ctx, suite.cancel = context.WithCancel(context.Background())
	assert.NotNil(suite.T(), suite.ctx)
	assert.NotNil(suite.T(), suite.cancel)

	suite.testDataDir = fsfire.MustGetFilePathWithFileSystemPath(
		suite.baseDir, fsfire.WithSpecificFileSystemPath("tmp/test/data/mysql"),
	)
}

func (suite *MySQLSuite) TearDownSuite() {
	if suite.drop {
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.Task{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.Poc{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.TaskPoc{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.HostUrls{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.IPRange{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.IPRangeTag{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.IPRangeConditions{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.Protocol{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.ProtocolScanPort{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.ScanPort{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.PortTemplate{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.System{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.Blacklist{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.ScanPortTemplate{}))
		suite.NoError(suite.store.Gorm().Migrator().DropTable(&models.Tag{}))
	}
}
