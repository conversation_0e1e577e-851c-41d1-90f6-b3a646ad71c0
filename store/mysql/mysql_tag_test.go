package mysql

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"

	"github.com/stretchr/testify/assert"
)

func (suite *MySQLSuite) Test_Tag_CreateBatch() {
	suite.mockFixed.SetConvertTemplate(mock.DefaultTimeFieldsConvertTemplate)

	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "tags_38.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.Tag
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.Tag().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_Tag_38_CreateBatch() {
	suite.mockFixed.SetConvertTemplate(mock.DefaultTimeFieldsConvertTemplate)

	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "38/tags.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.Tag
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.Tag().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}
