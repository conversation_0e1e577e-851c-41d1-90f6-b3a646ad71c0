package mysql

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"
)

func (suite *MySQLSuite) Test_IPRangeCondition_CreateBatch() {
	suite.mockFixed.SetConvertTemplate(mock.IPRangeConditionMockDataConvertTemplate)
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "ip_range_condition.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.IPRangeConditions
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.TaskIPRangeCondition().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_Create_Single() {
	suite.mockFixed.SetConvertTemplate(mock.IPRangeConditionMockDataConvertTemplate)
	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "ip_range_condition_single.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSingle(data)
	assert.NoError(suite.T(), err)

	var r *models.IPRangeConditions
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.TaskIPRangeCondition().Create(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_List_NoData() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	_, err := suite.store.TaskIPRangeCondition().List(suite.ctx, nil)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_List_HasData() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	_, err := suite.store.TaskIPRangeCondition().List(suite.ctx, nil)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_List_HasData_WithCondition() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	condition := map[string]interface{}{
		"category":                    "special",
		"ip_range_conditionable_type": models.IPRangeConditionableTypeOfTask,
		"ip_range_conditionable_id":   3,
	}
	_, err := suite.store.TaskIPRangeCondition().List(suite.ctx, condition)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_List_HasData_WithCondition_GetConditionsID() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	condition := map[string]interface{}{
		"category":                    "special",
		"ip_range_conditionable_type": models.IPRangeConditionableTypeOfTask,
		"ip_range_conditionable_id":   3,
	}
	_, err := suite.store.TaskIPRangeCondition().List(suite.ctx, condition)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_List_HasData_WithTaskId() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	actual, err := suite.store.TaskIPRangeCondition().ListWithTaskId(suite.ctx, 3)
	assert.NoError(suite.T(), err)

	slice := actual.ConditionListID()
	assert.Len(suite.T(), slice, 0)
}

func (suite *MySQLSuite) TestIPRangeCondition_Detail_NotFound() {
	db := &gorm.DB{Error: gorm.ErrRecordNotFound}
	defer gomonkey.ApplyMethodReturn(db, "Last", db).Reset()

	actual, err := suite.store.TaskIPRangeCondition().Detail(suite.ctx, 200)
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), gorm.ErrRecordNotFound, err)
	assert.Nil(suite.T(), actual)
}

func (suite *MySQLSuite) TestIPRangeCondition_Detail_Founded() {
	//suite.Test_IPRangeCondition_CreateBatch()
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Last", db).Reset()

	_, err := suite.store.TaskIPRangeCondition().Detail(suite.ctx, 4)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_GetTagListWithTaskId() {
	db := &gorm.DB{Error: nil}
	defer gomonkey.ApplyMethodReturn(db, "Find", db).Reset()

	_, err := suite.store.TaskIPRangeCondition().GetTagListWithTaskId(suite.ctx, 11)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) TestIPRangeCondition_GetTagListWithTaskId_NoData() {
	actual, err := suite.store.TaskIPRangeCondition().GetTagListWithTaskId(suite.ctx, 1)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)

	vals := actual.ConditionListID()
	assert.Nil(suite.T(), vals)
}
