package mysql

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"

	"gorm.io/gorm"
)

type scanPortTemplate struct {
	db *gorm.DB
}

func newScanPortTemplate(store *datastore) *scanPortTemplate {
	return &scanPortTemplate{db: store.db}
}

// Create table data for record.
func (store *scanPortTemplate) Create(ctx context.Context, data *models.ScanPortTemplate) error {
	return store.db.Create(&data).Error
}

func (store *scanPortTemplate) CreateBatch(ctx context.Context, data []*models.ScanPortTemplate) error {
	return store.db.CreateInBatches(&data, 100).Error
}

func (store *scanPortTemplate) IsAllPorts(ctx context.Context, id int) bool {
	var r *models.ScanPortTemplate

	err := store.db.Last(&r, map[string]interface{}{"id": id}).Error
	if err != nil {
		return false
	}

	if r == nil {
		return false
	}

	if r.Name == "0-65535" {
		return true
	}

	return false
}
