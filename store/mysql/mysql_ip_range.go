package mysql

import (
	"context"
	"fmt"
	"net"
	"strings"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/searcher"

	"gorm.io/gorm"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye-dependencies/fireness"
	"git.gobies.org/foeye-dependencies/interside"
	"git.gobies.org/foeye-dependencies/logger"
)

type ipRange struct {
	db *gorm.DB
}

func newIPRange(store *datastore) *ipRange {
	return &ipRange{db: store.db}
}

func (store *ipRange) Create(ctx context.Context, data *models.IPRange) error {
	return store.db.Create(&data).Error
}

func (store *ipRange) CreateBatch(ctx context.Context, data []*models.IPRange) error {
	return store.db.CreateInBatches(&data, 100).Error
}

func (store *ipRange) Detail(ctx context.Context, expr *searcher.Expression) (*models.IPRange, error) {
	var r *models.IPRange

	err := store.db.Model(&models.IPRange{}).Where(expr.Query, expr.Args...).Last(&r).Error
	if err != nil {
		return nil, err
	}

	if r.IpRange == "" {
		return nil, fmt.Errorf("invalid iprange data")
	}

	return r, nil
}

func (store *ipRange) GetIPRangeIdByTaskId(ctx context.Context, taskId int) ([]int, error) {
	var ids []int
	var expr = &searcher.Expression{
		Query: "ip_range_conditionable_id = ? AND category != ?",
		Args:  []interface{}{taskId, models.IPRangeConditionableCategoryOfSpecial},
	}

	tx := store.db.Model(&models.IPRangeConditions{}).
		Where(expr.Query, expr.Args...).
		Pluck("val_id", &ids)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return ids, nil
}

func (store *ipRange) GetIPRangeCIDR(ipType int, domainIps map[string][]string) ([]*models.IPRangeCidr, error) {
	var ipRangeCidrs []*models.IPRangeCidr
	var ipRanges []*models.IPRange

	tx := store.db.Model(&models.IPRange{}).
		Where("ip_type = ?", ipType).
		Find(&ipRanges)

	if tx.Error != nil {
		return nil, tx.Error
	}

	for _, ipRange := range ipRanges {
		if address.IsDomain(ipRange.IpRange, false) {
			if v, ok := domainIps[ipRange.IpRange]; ok {
				ips := make([]string, 0)
				for _, ip := range v {
					if !address.IsDomain(ip, false) {
						ips = append(ips, ip)
					}
				}
				if len(ips) > 0 {
					// 获取域名ip的对应的cidr，供应
					ipCidrs := address.ConvertIPsToCIDR(ips)
					ipCidr := &models.IPRangeCidr{
						ID:      ipRange.ID,
						IpRange: ipRange.IpRange,
						IpCount: ipCidrs.IPSCount(),
						Cidrs:   ipCidrs.ToCidrs(),
						Type:    "domain",
					}
					ipRangeCidrs = append(ipRangeCidrs, ipCidr)
				} else {
					// 没有ip，记录域名
					ipCidr := &models.IPRangeCidr{
						ID:      ipRange.ID,
						IpRange: ipRange.IpRange,
						IpCount: 1,
						Cidrs:   nil,
						Type:    "domain",
					}
					ipRangeCidrs = append(ipRangeCidrs, ipCidr)
				}
			}
		} else {
			ipCidrs := address.ConvertIPsToCIDR([]string{ipRange.IpRange})
			ipCidr := &models.IPRangeCidr{
				ID:      ipRange.ID,
				IpRange: ipRange.IpRange,
				IpCount: ipCidrs.IPSCount(),
				Cidrs:   ipCidrs.ToCidrs(),
				Type:    "ip_range",
			}
			ipRangeCidrs = append(ipRangeCidrs, ipCidr)
		}

	}

	return ipRangeCidrs, nil
}

func (store *ipRange) GetIpTags(ips []string, ipRangeCidrs []*models.IPRangeCidr) (map[string]*models.IPRangeTags, error) {
	ipMap := GetIp2IpRange(ipRangeCidrs, ips)
	ipRangeIds := GetIpRangeIds(ipMap)
	logger.Warnw("[START@SYNC]GetIpTags ", "ipRangeIds", ipRangeIds)
	logger.Warnw("[START@SYNC]GetIpTags ", "ipMap", ipMap)

	var ctx context.Context
	tags, err := store.GetTaskIPRangeTags(ctx, ipRangeIds)
	if err != nil {
		logger.Warnw("[START@SYNC]GetTaskIPRangeTags ", "err", err.Error())
		return nil, err
	}
	ipTag := store.SetIpTags(tags, ipMap)

	return ipTag, nil
}

func (store *ipRange) GetDomainTags(domains []interface{}) map[string]*models.IPRangeTags {
	tags, err := store.GetTaskDomainTags(domains)
	if err != nil {
		logger.Warnw("[START@SYNC]GetTaskDomainTags ", "err", err.Error())
		return nil
	}

	ipTag := make(map[string]*models.IPRangeTags)
	ipRangeTags := make(map[int][]*models.IPRangeReferenceTags)
	for _, item := range tags {
		ipRangeTags[item.Id] = append(ipRangeTags[item.Id], item)
	}

	for _, item := range tags {
		if ipRangeTagArr, ok := ipRangeTags[int(item.Id)]; ok {
			r := &models.IPRangeTags{
				AddWay:    "self_defined",
				IpRange:   item.IpRange,
				IpRangeId: uint(item.Id),
			}

			var names = interside.NewMap()
			var fields = interside.NewMap()

			for _, tagVal := range ipRangeTagArr {
				store.tagAssign(tagVal, r, names, fields)
			}
			r.CustomNames = names.ToMap()
			r.CustomFields = fields.ToMap()

			ipTag[item.IpRange] = r
		}
	}

	return ipTag
}

// SetDomainTags 组合ip和tag的关系：{"fofa.info": {"BusinessApp":"test"}}
// tags 所有的ip段、域名对应的tags，tag.Id就是ip段、域名的Id
func (store *ipRange) SetDomainTags(tags []*models.IPRangeReferenceTags, domainMap map[string]*models.IPRangeCidr) map[string]*models.IPRangeTags {
	ipTag := make(map[string]*models.IPRangeTags)

	ipRangeTags := make(map[int][]*models.IPRangeReferenceTags)
	for _, tag := range tags {
		ipRangeTags[tag.Id] = append(ipRangeTags[tag.Id], tag)
	}
	for ip, ipRange := range domainMap {
		if ipRangeTagArr, ok := ipRangeTags[int(ipRange.ID)]; ok {
			var r = &models.IPRangeTags{
				AddWay:    "self_defined",
				IpRange:   ipRange.IpRange,
				IpRangeId: ipRange.ID,
			}
			var names = interside.NewMap()
			var fields = interside.NewMap()

			for _, tagVal := range ipRangeTagArr {
				store.tagAssign(tagVal, r, names, fields)
			}
			r.CustomNames = names.ToMap()
			r.CustomFields = fields.ToMap()
			ipTag[ip] = r
		}
	}
	return ipTag
}

// GetIpRangeIds 获取对应上关系的ip段id
func GetIpRangeIds(ipMap map[string]*models.IPRangeCidr) []int {
	ipRangeIdMap := make(map[int]int)
	ipRangeIds := []int{}
	for _, ipRange := range ipMap {
		id := int(ipRange.ID)
		if _, ok := ipRangeIdMap[id]; !ok {
			ipRangeIdMap[id] = id
			ipRangeIds = append(ipRangeIds, id)
		}
	}
	return ipRangeIds
}

// GetIp2IpRange 根据取小原则,获取ip对应ip段
func GetIp2IpRange(ipRangeCidrs []*models.IPRangeCidr, ips []string) map[string]*models.IPRangeCidr {
	ipMap := make(map[string]*models.IPRangeCidr)
	for _, ip := range ips {
		for _, ipRange := range ipRangeCidrs {
			if address.IsDomain(ip, false) { // 是域名
				if address.IsDomain(ipRange.IpRange, false) {
					if ip == ipRange.IpRange {
						ipMap[ip] = ipRange
						break
					}
					continue
				}
				continue
			}
			if ipRange.Cidrs != nil {
				for _, cidr := range ipRange.Cidrs {
					if cidr.Contains(ip) {
						if v, ok := ipMap[ip]; ok {
							if v.IpCount > ipRange.IpCount {
								ipMap[ip] = ipRange
							}
						} else {
							ipMap[ip] = ipRange
						}
					}
				}
			}
		}
	}
	return ipMap
}

// SetIpTags 组合ip和tag的关系：{"************": {"BusinessApp":"test"}}
// tags 所有的ip段、域名对应的tags，tag.Id就是ip段、域名的Id
func (store *ipRange) SetIpTags(tags []*models.IPRangeReferenceTags, ipMap map[string]*models.IPRangeCidr) map[string]*models.IPRangeTags {
	ipTag := make(map[string]*models.IPRangeTags)

	ipRangeTags := make(map[int][]*models.IPRangeReferenceTags)
	for _, tag := range tags {
		ipRangeTags[tag.Id] = append(ipRangeTags[tag.Id], tag)
	}
	for ip, ipRange := range ipMap {
		if ipRangeTagArr, ok := ipRangeTags[int(ipRange.ID)]; ok {
			var r = &models.IPRangeTags{
				AddWay:    "self_defined",
				IpRange:   ipRange.IpRange,
				IpRangeId: ipRange.ID,
			}
			var names = interside.NewMap()
			var fields = interside.NewMap()

			for _, tagVal := range ipRangeTagArr {
				store.tagAssign(tagVal, r, names, fields)
			}
			r.CustomNames = names.ToMap()
			r.CustomFields = fields.ToMap()
			ipTag[ip] = r
		}
	}
	return ipTag
}

func (store *ipRange) GetIPRangeIdsAllForIPType(ctx context.Context, iptype int) ([]int, error) {
	var ids []int
	var expr = &searcher.Expression{
		Query: "ip_type = ?",
		Args:  []interface{}{iptype},
	}

	tx := store.db.Model(&models.IPRange{}).Where(expr.Query, expr.Args...).Pluck("id", &ids)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return ids, nil
}

// Tags get IPRange tags.
func (store *ipRange) Tags(ctx context.Context, taskId int, ip string) (*models.IPRangeTags, error) {
	task, err := store.GetTaskWithId(ctx, taskId)
	if err != nil {
		return nil, err
	}

	var ids []int
	ids, err = store.GetIPRangeIdsAllForIPType(ctx, int(task.IPType))
	if err != nil {
		return nil, err
	}

	tags, err := store.GetTaskIPRangeTags(ctx, ids)
	if err != nil {
		return nil, err
	}

	contains, success := store.IpRangeContainsRefactor(ctx, tags, ip)
	if success {
		return contains, nil
	}

	return nil, nil
}

func (store *ipRange) IpRangeContainsRefactor(ctx context.Context, tags []*models.IPRangeReferenceTags, taskIp string) (*models.IPRangeTags, bool) {
	var r = &models.IPRangeTags{
		AddWay: "self_defined",
	}
	var names = interside.NewMap()
	var fields = interside.NewMap()

	if fireness.IsIPv4(taskIp) {
		ipRangeTags := store.ipRangeContainsRefactorFilterIpv4(tags, taskIp)
		for _, tagVal := range ipRangeTags {
			store.tagAssign(tagVal, r, names, fields)
		}
	} else if fireness.IsIPv6(taskIp) {
		ipRangeTags := store.ipRangeContainsRefactorFilterIpv6(tags, taskIp)
		for _, tagVal := range ipRangeTags {
			store.tagAssign(tagVal, r, names, fields)
		}
	}

	r.CustomNames = names.ToMap()
	r.CustomFields = fields.ToMap()

	return r, true
}

func (store *ipRange) tagAssign(tag *models.IPRangeReferenceTags, r *models.IPRangeTags, names *interside.Map, fields *interside.Map) {
	if tag.ParentRealName == "computer_room" {
		r.ComputerRoom = tag.Name
	} else if tag.ParentRealName == "username" {
		r.UserName = tag.Name
		r.ManagerEmail = tag.ManagerEmail
		r.ManagerMobile = tag.ManagerMobile
	} else if tag.ParentRealName == "company" {
		r.Company = tag.Name
	} else if tag.ParentRealName == "business_app" {
		r.BusinessApp = tag.Name
	} else if tag.ParentRealName == "asset_level" {
		r.AssetLevel = tag.Name
	} else {
		names.Append(tag.ParentRealName, tag.ParentName)
		fields.Append(tag.ParentRealName, tag.Name)
	}
}

func (store *ipRange) GetTaskWithId(ctx context.Context, taskId int) (*models.Task, error) {
	var r *models.Task

	err := store.db.Model(&models.Task{}).Where("id = ?", taskId).Last(&r).Error
	if err != nil {
		return nil, err
	}

	return r, nil
}

// GetTagsIdsWithTaskId
func (store *ipRange) GetTagsIdsWithTaskId(ctx context.Context, taskId int) ([]int, error) {
	var ids []int
	var expr = &searcher.Expression{
		Query: "ip_range_conditionable_id = ?  AND category != ?",
		Args:  []interface{}{taskId, models.IPRangeConditionableCategoryOfSpecial},
	}

	err := store.db.Model(&models.IPRangeConditions{}).
		Where(expr.Query, expr.Args...).Pluck("val_id", &ids).Error
	if err != nil {
		return nil, err
	}

	return ids, nil
}

// GetTaskIPRangeTags 获取ip段对应的tags
func (store *ipRange) GetTaskIPRangeTags(ctx context.Context, ids []int) ([]*models.IPRangeReferenceTags, error) {
	var r []*models.IPRangeReferenceTags

	selected := []string{
		"IR.id",
		"T.name",
		"T.realname",
		"T.ancestry",
		"TP.name as parentname",
		"TP.realname as parentrealname",
		"T.email as manager_email",
		"T.phone as manager_mobile",
		"IR.city",
		"IR.province",
		"IR.ip_range",
		"IR.add_way",
	}

	err := store.db.Table("tags T").
		Select(strings.Join(selected, ",")).
		Joins("inner join tags TP on TP.id=T.ancestry").
		Joins("inner join ip_range_tags IRT on IRT.tag_id=T.id").
		Joins("inner join ip_ranges IR on IR.id=IRT.ip_range_id").
		Where("IR.id IN (?) ", ids).
		Scan(&r).Error

	if err != nil {
		return nil, err
	}

	return r, nil
}

// GetTaskDomainTags
// @Summary 获取domain tags
func (store *ipRange) GetTaskDomainTags(domains []interface{}) ([]*models.IPRangeReferenceTags, error) {
	var r []*models.IPRangeReferenceTags

	selected := []string{
		"IR.id",
		"T.name",
		"T.realname",
		"T.ancestry",
		"TP.name as parentname",
		"TP.realname as parentrealname",
		"T.email as manager_email",
		"T.phone as manager_mobile",
		"IR.city",
		"IR.province",
		"IR.ip_range",
		"IR.add_way",
	}

	const batchSize = 1000 // 每批 1000 个
	for i := 0; i < len(domains); i += batchSize {
		end := i + batchSize
		if end > len(domains) {
			end = len(domains)
		}
		batch := domains[i:end]

		var batchResult []*models.IPRangeReferenceTags
		err := store.db.Table("tags T").
			Select(strings.Join(selected, ",")).
			Joins("inner join tags TP on TP.id=T.ancestry").
			Joins("inner join ip_range_tags IRT on IRT.tag_id=T.id").
			Joins("inner join ip_ranges IR on IR.id=IRT.ip_range_id").
			Where("IR.ip_range IN (?)", batch).
			Scan(&batchResult).Error

		if err != nil {
			return nil, err
		}

		r = append(r, batchResult...)
	}

	return r, nil
}

func (store *ipRange) GetTaskIPRangeTag(ctx context.Context, iprangeId int) (*models.IPRangeReferenceTags, error) {
	var r *models.IPRangeReferenceTags

	selected := []string{
		"IR.id",
		"T.name",
		"T.realname",
		"T.ancestry",
		"TP.name as parentname",
		"TP.realname as parentrealname",
		"IR.city",
		"IR.province",
		"IR.ip_range",
		"IR.add_way",
	}

	err := store.db.Table("tags T").
		Select(strings.Join(selected, ",")).
		Joins("inner join tags TP on TP.id=T.ancestry").
		Joins("inner join ip_range_tags IRT on IRT.tag_id=T.id").
		Joins("inner join ip_ranges IR on IR.id=IRT.ip_range_id").
		Where("IR.id = ?", iprangeId).
		Scan(&r).Error

	if err != nil {
		return nil, err
	}

	return r, nil
}

func (store *ipRange) ipRangeContainsRefactorFilterIpv4(tags []*models.IPRangeReferenceTags, taskIp string) []*models.IPRangeReferenceTags {
	data := make(map[string][]*models.IPRangeReferenceTags)
	for _, tag := range tags {
		iprange := tag.IpRange
		cider := address.ConvertIPsToCIDR([]string{iprange})
		for _, ip := range cider {
			c, err := address.ParseCIDR(ip)
			if err != nil {
				logger.Infof("[Ipv4ParseCIDR] Failed to parse CIDR: %v", err)
				continue
			}
			// 如果存在提取MySQL中的城市地址和标签
			if c.Contains(taskIp) {
				data[iprange] = append(data[iprange], tag)
				continue
			}
		}
	}
	resultIp := ""
	resultid := 0
	for key, val := range data {
		if len(val) == 0 {
			continue
		}
		if resultIp == "" {
			resultIp = key
			resultid = val[0].Id
			continue
		}
		resultIpIns := address.ConvertIPsToCIDR([]string{resultIp})
		keyIns := address.ConvertIPsToCIDR([]string{key})
		resultIpLen := len(resultIpIns.ToIPRanges())
		keyLen := len(keyIns.ToIPRanges())
		if resultIpLen > keyLen {
			resultIp = key
			resultid = val[0].Id
		} else if resultIpLen == keyLen {
			if val[0].Id > resultid {
				resultIp = key
				resultid = val[0].Id
			}
		}
	}
	return data[resultIp]
}

func (store *ipRange) ipRangeContainsRefactorFilterIpv6(tags []*models.IPRangeReferenceTags, taskIp string) []*models.IPRangeReferenceTags {
	data := make(map[string][]*models.IPRangeReferenceTags)
	for _, tag := range tags {
		iprange := tag.IpRange
		// 单个ipv6
		ip := net.ParseIP(iprange)
		if ip != nil {
			if ip.String() == taskIp {
				data[iprange] = append(data[iprange], tag)
				continue
			}
		}
		// ipv6段
		_, ipNet, err := net.ParseCIDR(iprange)
		if err != nil {
			logger.Infof("[Ipv6ParseCIDR] Failed to parse CIDR: %v", err)
			continue
		}
		ip = net.ParseIP(taskIp)
		if ip == nil {
			logger.Infof("[Ipv6ParseIP] Failed to parse IP: %v", err)
			continue
		}
		if ipNet.Contains(ip) {
			data[iprange] = append(data[iprange], tag)
			continue
		}
	}
	resultIp := ""
	resultId := 0
	for key, val := range data {
		if len(val) == 0 {
			continue
		}
		if resultIp == "" {
			resultIp = key
			resultId = val[0].Id
			continue
		}
		_, resultIpIns, err := net.ParseCIDR(resultIp)
		if err != nil {
			logger.Infof("[Ipv6ParseIP] Failed to parse IP: %v", err)
			continue
		}
		_, keyIns, err := net.ParseCIDR(key)
		if err != nil {
			logger.Infof("[Ipv6ParseIP] Failed to parse IP: %v", err)
			continue
		}
		resultIpLen, _ := resultIpIns.Mask.Size()
		keyLen, _ := keyIns.Mask.Size()
		if resultIpLen < keyLen {
			resultIp = key
			resultId = val[0].Id
		} else if resultIpLen == keyLen {
			if val[0].Id > resultId {
				resultIp = key
				resultId = val[0].Id
			}
		}
	}
	return data[resultIp]
}
