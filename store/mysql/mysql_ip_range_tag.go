package mysql

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"gorm.io/gorm"
)

type ipRangeTag struct {
	db *gorm.DB
}

func newIPRangeTag(store *datastore) *ipRangeTag {
	return &ipRangeTag{db: store.db}
}

func (store *ipRangeTag) Create(ctx context.Context, data *models.IPRangeTag) error {
	return store.db.Create(&data).Error
}

func (store *ipRangeTag) CreateBatch(ctx context.Context, data []*models.IPRangeTag) error {
	return store.db.CreateInBatches(&data, 100).Error
}
