package mysql

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"git.gobies.org/foeye-dependencies/connecter"
	"git.gobies.org/foeye-dependencies/connecter/kingbase"
	"git.gobies.org/foeye-dependencies/connecter/mysql"
	"git.gobies.org/foeye-dependencies/connecter/shentong"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"
)

// Database related constant definition.
const (
	_DatabaseBatchInsertOrder = 100
)

type datastore struct {
	db *gorm.DB
}

// GetMySQLFactoryOr create mysql factory with the given dccConfig.
func GetMySQLFactoryOr(config *config.Configure) (store.MySQLFactory, error) {
	if config == nil {
		return nil, fmt.Errorf("failed to get mysql store factory, config can't empty")
	}

	var err error

	once.Do(func() {
		l := logger.New(log.New(os.Stdout, "\r\n", log.LstdFlags), logger.Config{
			SlowThreshold:             200 * time.Millisecond,
			LogLevel:                  logger.LogLevel(config.MYSQL.LogLevel),
			IgnoreRecordNotFoundError: false,
			Colorful:                  true,
		})

		options := &mysql.Config{
			Host:                  config.MYSQL.Host,
			Username:              config.MYSQL.Username,
			Password:              config.MYSQL.Password,
			Database:              config.MYSQL.Database,
			MaxIdleConnections:    config.MYSQL.MaxIdleConnections,
			MaxOpenConnections:    config.MYSQL.MaxOpenConnections,
			MaxConnectionLifeTime: config.MYSQL.MaxConnectionLifeTime,
			LogLevel:              config.MYSQL.LogLevel,
			Logger:                l,
			DriverName:            connecter.DriverName(config.MYSQL.DriverName),
		}

		if config.MYSQL.DriverName == "OSCAR" {
			orm, err = shentong.NewConnection(options)
		} else if config.MYSQL.DriverName == "KingBase" {
			orm, err = kingbase.NewConnection(options)
		} else {
			orm, err = mysql.NewConnection(options)
		}
		if err != nil {
			return
		}

		factory = &datastore{db: orm}
	})

	if err != nil {
		return nil, errors.Wrapf(err, "failed to initialize database orm")
	}

	if factory == nil {
		return nil, fmt.Errorf("failed to get mysql store factory, factory: %+v, error: %w", factory, err)
	}

	// Resolve test drop database table issue.
	if config.MYSQL.AutoMigrate {
		// Use auto migrate.
		err = autoMigrate(orm)
	}

	return factory, nil
}

func (store *datastore) TaskIPRangeCondition() store.TaskIPRangeConditionStore {
	return newIpRangeCondition(store)
}

func (store *datastore) Task() store.TaskStore {
	return newTask(store)
}

func (store *datastore) ProtocolScanPort() store.ProtocolScanPortStore {
	return newProtocolScanPort(store)
}

func (store *datastore) ScanPortTemplate() store.ScanPortTemplateStore {
	return newScanPortTemplate(store)
}

// func (store *datastore) System() store.SystemStore {
//	return newSystem(store)
// }

// func (store *datastore) Blacklist() store.BlacklistStore {
//	return newBlacklist(store)
// }

func (store *datastore) IPRange() store.IPRangeStore {
	return newIPRange(store)
}

func (store *datastore) IPRangeTag() store.IPRangeTagStore {
	return newIPRangeTag(store)
}

func (store *datastore) Tag() store.TagStore {
	return newTag(store)
}

func (store *datastore) Gorm() *gorm.DB {
	return store.db
}

func (store *datastore) Close() error {
	instance, err := store.db.DB()
	if err != nil {
		return errors.Wrap(err, "get gorm database instance failed")
	}
	return instance.Close()
}

var (
	factory store.MySQLFactory
	orm     *gorm.DB
	once    sync.Once
)

func autoMigrate(instance *gorm.DB) error {
	// Gorm set of key.
	key := "gorm:table_options"

	tables := []struct {
		name  string
		desc  string
		table interface{}
	}{
		{
			name:  models.TableNameOfTask,
			desc:  models.TableDescOfTask,
			table: &models.Task{},
		},
		{
			name:  models.TableNameOfPoc,
			desc:  models.TableDescOfPoc,
			table: &models.Poc{},
		},
		{
			name:  models.TableNameOfRule,
			desc:  models.TableDescOfRule,
			table: &models.Rule{},
		},
		{
			name:  models.TableNameOfTaskPoc,
			desc:  models.TableDescOfTaskPoc,
			table: &models.TaskPoc{},
		},
		{
			name:  models.TableNameOfHostUrls,
			desc:  models.TableDescOfHostUrls,
			table: &models.HostUrls{},
		},
		{
			name:  models.TableNameOfIPRangeTag,
			desc:  models.TableDescOfIPRangeTag,
			table: &models.IPRangeTag{},
		},
		{
			name:  models.TableNameOfIPRange,
			desc:  models.TableDescOfIPRange,
			table: &models.IPRange{},
		},
		{
			name:  models.TableNameOfIPRangeCondition,
			desc:  models.TableDescOfIPRangeCondition,
			table: &models.IPRangeConditions{},
		},
		{
			name:  models.TableNameOfProtocolScanPort,
			desc:  models.TableDescOfProtocolScanPort,
			table: &models.ProtocolScanPort{},
		},
		{
			name:  models.TableNameOfProtocols,
			desc:  models.TableDescOfProtocols,
			table: &models.Protocol{},
		},
		{
			name:  models.TableNameOfScanPort,
			desc:  models.TableDescOfScanPort,
			table: &models.ScanPort{},
		},
		{
			name:  models.TableNameOfPortTemplate,
			desc:  models.TableDescOfPortTemplate,
			table: &models.PortTemplate{},
		},
		{
			name:  models.TableNameOfSystem,
			desc:  models.TableDescOfSystem,
			table: &models.System{},
		},
		{
			name:  models.TableNameOfBlackList,
			desc:  models.TableDescOfBlackList,
			table: &models.Blacklist{},
		},
		{
			name:  models.TableNameOfScanPortTemplate,
			desc:  models.TableDescOfScanPortTemplate,
			table: &models.ScanPortTemplate{},
		},
		{
			name:  models.TableNameOfTag,
			desc:  models.TableDescOfTag,
			table: &models.Tag{},
		},
	}

	for _, table := range tables {
		err := instance.Set(key, mysql.GormComment(table.desc)+" ROW_FORMAT=DYNAMIC;").AutoMigrate(table.table)
		if err != nil {
			fmt.Println("failed to create table: ", table.name, err)
			return errors.Wrapf(err, "AutoMigrate table %s to failed", table.name)
		}
	}

	return nil
}
