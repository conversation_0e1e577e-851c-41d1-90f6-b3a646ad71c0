package mysql

import (
	"context"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/searcher"

	"gorm.io/gorm"
)

type task struct {
	db *gorm.DB
}

func newTask(store *datastore) *task {
	return &task{db: store.db}
}

// Create table data for record.
func (store *task) Create(ctx context.Context, data *models.Task) error {
	return store.db.Create(&data).Error
}

// Count table data for total number.
func (store *task) Count(ctx context.Context) (int, error) {
	var count int64
	err := store.db.Model(&models.Task{}).Count(&count).Error
	if err != nil {
		return int(count), err
	}
	return int(count), nil
}

func (store *task) CreateBatch(ctx context.Context, data []*models.Task) error {
	return store.db.CreateInBatches(&data, 100).Error
}

// List get database table records.
func (store *task) List(ctx context.Context, conditions ...interface{}) ([]*models.Task, error) {
	var r []*models.Task

	err := store.db.Find(&r, conditions...).Error
	if err != nil {
		return nil, err
	}

	return r, nil
}

// Detail get table data for detail by id.
func (store *task) Detail(ctx context.Context, id int) (*models.Task, error) {
	var r *models.Task
	if err := store.db.Last(&r, id).Error; err != nil {
		return nil, err
	}
	return r, nil
}

// UpdateThreatNumber update table data for threat number by id.
func (store *task) UpdateThreatNumber(ctx context.Context, id int, threatNumber int) error {
	err := store.db.Model(&models.Task{}).
		Where(id).
		Update("threat_num", gorm.Expr("threat_num + ?", threatNumber)).Error
	if err != nil {
		return err
	}
	return nil
}

// UpdateIPCount update table data for ip count number by id.
func (store *task) UpdateIPCount(ctx context.Context, id int, count int) error {
	if err := store.db.Model(&models.Task{}).Where(id).Update("ip_count", count).Error; err != nil {
		return err
	}
	return nil
}

// Update database table record.
func (store *task) Update(ctx context.Context, id int, values interface{}) error {
	if err := store.db.Model(&models.Task{}).Where(id).Updates(values).Error; err != nil {
		return err
	}
	return nil
}

// UpdateScanStep Update database table record single field.
func (store *task) UpdateScanStep(ctx context.Context, id int, step models.ScanStep) error {
	return store.Update(ctx, id, map[string]interface{}{"scan_step": step})
}

// UpdateRealBeginTime Update database table record single field.
func (store *task) UpdateRealBeginTime(ctx context.Context, id int, time time.Time) error {
	return store.Update(ctx, id, map[string]interface{}{"real_begin_time": time})
}

// UpdateRealBeginEnd Update database table record single field.
func (store *task) UpdateRealBeginEnd(ctx context.Context, id int, time time.Time) error {
	return store.Update(ctx, id, map[string]interface{}{"real_end_time": time})
}

// UpdateUseSeconds Update database table record single field.
func (store *task) UpdateUseSeconds(ctx context.Context, id int, duration time.Duration) error {
	return store.Update(ctx, id, map[string]interface{}{"use_seconds": duration.Seconds()})
}

// UpdateState Update database table record single field.
func (store *task) UpdateState(ctx context.Context, id int, state models.TaskState) error {
	return store.Update(ctx, id, map[string]interface{}{"state": state})
}

// CurrentTask Find database table record detail.
func (store *task) CurrentTask(ctx context.Context) (*models.Task, error) {
	var r *models.Task
	where := map[string]interface{}{
		"top": 1,
		"state": []models.TaskState{
			models.TaskStateOfExecuting,
			models.TaskStateOfManualPausing,
			models.TaskStateOfAutoPausing,
		},
	}

	err := store.db.Last(&r, where).Order(`id DESC`).Error
	if err != nil {
		return nil, err
	}

	return r, nil
}

// Delete delete database table record.
func (store *task) Delete(ctx context.Context, id int) error {
	return store.db.Delete(&models.Task{}, id).Error
}

// GetTaskIPRangeTags Get task iprange tags.
// TODO: 需要删除，本应该不属于这个类
func (store *task) GetTaskIPRangeTags(ctx context.Context, ids []int) ([]*models.IPRangeReferenceTags, error) {
	var r []*models.IPRangeReferenceTags

	selected := []string{
		"IR.id",
		"T.name",
		"T.realname",
		"T.ancestry",
		"TP.name as parentname",
		"TP.realname as parentrealname",
		"IR.city",
		"IR.province",
		"IR.ip_range",
		"IR.add_way",
	}

	err := store.db.Table("tags T").
		Select(strings.Join(selected, ",")).
		Joins("inner join tags TP on TP.id=T.ancestry").
		Joins("inner join ip_range_tags IRT on IRT.tag_id=T.id").
		Joins("inner join ip_ranges IR on IR.id=IRT.ip_range_id").
		Where("IR.id IN (?) ", ids).
		Scan(&r).Error

	if err != nil {
		return nil, err
	}

	return r, nil
}

// GetIPRangeIdByTaskId Get IPRange IDs by task ID.
// TODO: 需要删除，本应该不属于这个类
func (store *task) GetIPRangeIdByTaskId(ctx context.Context, taskId int) ([]int, error) {
	var ids []int
	var expr = &searcher.Expression{
		Query: "ip_range_conditionable_id = ? AND category != ?",
		Args:  []interface{}{taskId, models.IPRangeConditionableCategoryOfSpecial},
	}

	tx := store.db.Model(&models.IPRangeConditions{}).
		Where(expr.Query, expr.Args...).
		Pluck("val_id", &ids)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return ids, nil
}

// GetIPRangeTagsByTaskId Get city address and label in IP segment.
// TODO: un-todo-done
func (store *task) GetIPRangeTagsByTaskId(ctx context.Context, taskId int) ([]int, error) {
	var ids []int
	var expr = &searcher.Expression{
		Query: "ip_range_conditionable_id = ? AND category != ?",
		Args:  []interface{}{taskId, models.IPRangeConditionableCategoryOfSpecial},
	}

	tx := store.db.Model(&models.IPRangeConditions{}).
		Where(expr.Query, expr.Args...).
		Pluck("vals", &ids)

	if tx.Error != nil {
		return nil, tx.Error
	}

	return ids, nil
}
