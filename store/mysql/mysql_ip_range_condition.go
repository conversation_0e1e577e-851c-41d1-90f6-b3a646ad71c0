package mysql

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"

	"gorm.io/gorm"
)

type ipRangeCondition struct {
	db *gorm.DB
}

func newIpRangeCondition(store *datastore) *ipRangeCondition {
	return &ipRangeCondition{db: store.db}
}

func (store *ipRangeCondition) List(ctx context.Context, condition map[string]interface{}) (models.IPRangeConditionsList, error) {
	var r models.IPRangeConditionsList

	err := store.db.Find(&r, condition).Error
	if err != nil {
		return nil, err
	}

	return r, nil
}

func (store *ipRangeCondition) ListWithTaskId(ctx context.Context, taskId int) (models.IPRangeConditionsList, error) {
	condition := map[string]interface{}{
		"category":                    "special",
		"ip_range_conditionable_type": models.IPRangeConditionableTypeOfTask,
		"ip_range_conditionable_id":   taskId,
	}

	return store.List(ctx, condition)
}

func (store *ipRangeCondition) GetTagListWithTaskId(ctx context.Context, taskId int) (models.IPRangeConditionsList, error) {
	strings := []string{
		models.IPRangeTypeOfCompany,
		models.IPRangeTypeOfBusinessApp,
		models.IPRangeTypeOfUsername,
		models.IPRangeTypeOfComputerRoom,
	}

	condition := map[string]interface{}{
		"category":                    strings,
		"ip_range_conditionable_type": models.IPRangeConditionableTypeOfTask,
		"ip_range_conditionable_id":   taskId,
	}

	return store.List(ctx, condition)
}

func (store *ipRangeCondition) Create(ctx context.Context, data *models.IPRangeConditions) error {
	return store.db.Create(&data).Error
}

func (store *ipRangeCondition) CreateBatch(ctx context.Context, data models.IPRangeConditionsList) error {
	return store.db.CreateInBatches(&data, 100).Error
}

func (store *ipRangeCondition) Detail(ctx context.Context, id int) (*models.IPRangeConditions, error) {
	var r *models.IPRangeConditions

	err := store.db.Last(&r, id).Error
	if err != nil {
		return nil, err
	}

	return r, nil
}
