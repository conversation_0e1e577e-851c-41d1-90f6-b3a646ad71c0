package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye-dependencies/mosso"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/mock"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/searcher"
)

func (suite *MySQLSuite) Test_IPRange_CreateBatch() {
	suite.mockFixed.SetConvertTemplate(mock.DefaultTimeFieldsConvertTemplate)

	data, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "ip_ranges.json"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)

	fixed, err := suite.mockFixed.FixedSlice(data)
	assert.NoError(suite.T(), err)

	var r []*models.IPRange
	err = json.Unmarshal(fixed, &r)
	assert.NoError(suite.T(), err)

	err = suite.store.IPRange().CreateBatch(suite.ctx, r)
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_IPRange_Detail() {
	detail, err := suite.store.IPRange().Detail(suite.ctx, &searcher.Expression{
		Query: "id = ?",
		Args:  []interface{}{1},
	})

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), detail)
	mosso.DebugShowContentWithJSON(detail)
}

func (suite *MySQLSuite) Test_IPRange_GetIPRangeIdsAllForIPType() {

	actual, err := suite.store.IPRange().GetIPRangeIdsAllForIPType(context.Background(), models.IpTypeOfIPV4)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), actual, 7)
	mosso.DebugShowContentWithJSON(actual)
}

func (suite *MySQLSuite) Test_IPRange_GetIPRangeCIDR() {
	t := suite.T()
	t.Run("No domains", func(t *testing.T) {
		defer gomonkey.ApplyMethod(reflect.TypeOf(&gorm.DB{}), "Find", func(_ *gorm.DB, out interface{}) *gorm.DB {
			var ipRanges []*models.IPRange
			ipRanges = append(ipRanges, &models.IPRange{ID: 1, IpRange: "************-185"})
			reflect.ValueOf(out).Elem().Set(reflect.ValueOf(ipRanges))
			return &gorm.DB{Error: nil}
		}).Reset()
		actual, err := suite.store.IPRange().GetIPRangeCIDR(models.IpTypeOfIPV4, nil)
		assert.NoError(suite.T(), err)
		assert.Len(suite.T(), actual, 1)
		assert.Equal(suite.T(), "************-185", actual[0].IpRange)
		assert.Equal(suite.T(), 3, actual[0].IpCount)

		ipRanges := address.IPRangeCIDRs{"************/32", "************/31"}
		assert.Equal(suite.T(), ipRanges.ToCidrs(), actual[0].Cidrs)
	})

	t.Run("Have domains", func(t *testing.T) {
		defer gomonkey.ApplyMethod(reflect.TypeOf(&gorm.DB{}), "Find", func(_ *gorm.DB, out interface{}) *gorm.DB {
			var ipRanges []*models.IPRange
			ipRanges = append(ipRanges, &models.IPRange{ID: 1, IpRange: "************-185"}, &models.IPRange{ID: 2, IpRange: "fofa.info"})
			reflect.ValueOf(out).Elem().Set(reflect.ValueOf(ipRanges))
			return &gorm.DB{Error: nil}
		}).Reset()
		actual, err := suite.store.IPRange().GetIPRangeCIDR(models.IpTypeOfIPV4, map[string][]string{"fofa.info": []string{"**********"}})
		assert.NoError(suite.T(), err)
		assert.Len(suite.T(), actual, 2)
		assert.Equal(suite.T(), "************-185", actual[0].IpRange)
		assert.Equal(suite.T(), 3, actual[0].IpCount)

		ipRanges := address.IPRangeCIDRs{"************/32", "************/31"}
		assert.Equal(suite.T(), ipRanges.ToCidrs(), actual[0].Cidrs)

		ipRanges = address.IPRangeCIDRs{"**********/32"}
		assert.Equal(suite.T(), ipRanges.ToCidrs(), actual[1].Cidrs)
	})

	t.Run("Domains no ips", func(t *testing.T) {
		defer gomonkey.ApplyMethod(reflect.TypeOf(&gorm.DB{}), "Find", func(_ *gorm.DB, out interface{}) *gorm.DB {
			var ipRanges []*models.IPRange
			ipRanges = append(ipRanges, &models.IPRange{ID: 1, IpRange: "************-185"}, &models.IPRange{ID: 2, IpRange: "fofa.info"})
			reflect.ValueOf(out).Elem().Set(reflect.ValueOf(ipRanges))
			return &gorm.DB{Error: nil}
		}).Reset()
		actual, err := suite.store.IPRange().GetIPRangeCIDR(models.IpTypeOfIPV4, map[string][]string{"fofa.info": []string{"tt.fofa.info"}})
		assert.NoError(suite.T(), err)
		assert.Len(suite.T(), actual, 2)
		assert.Equal(suite.T(), "fofa.info", actual[1].IpRange)
		assert.Equal(suite.T(), 1, actual[1].IpCount)

		assert.Nil(suite.T(), actual[1].Cidrs)
		assert.Equal(suite.T(), "domain", actual[1].Type)

	})
}

func (suite *MySQLSuite) Test_GetIp2IpRange() {
	t := suite.T()

	t.Run("Ipranges", func(t *testing.T) {
		var ipRangeCidrs []*models.IPRangeCidr
		cidrs := address.IPRangeCIDRs{"************/32", "************/31"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "************-185", IpCount: 3, Cidrs: cidrs.ToCidrs()})
		actual := GetIp2IpRange(ipRangeCidrs, []string{"************"})
		assert.Equal(t, "************-185", actual["************"].IpRange)
		assert.Equal(t, 3, actual["************"].IpCount)

		ipRanges := address.IPRangeCIDRs{"************/32", "************/31"}
		assert.Equal(t, uint(1), actual["************"].ID)
		assert.Equal(t, ipRanges.ToCidrs(), actual["************"].Cidrs)

		cidrs2 := address.IPRangeCIDRs{"************/32", "************/32"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "************-183", IpCount: 2, Cidrs: cidrs2.ToCidrs()})

		actual = GetIp2IpRange(ipRangeCidrs, []string{"************"})

		assert.Equal(t, "************-183", actual["************"].IpRange)
		assert.Equal(t, 2, actual["************"].IpCount)

		ipRanges = address.IPRangeCIDRs{"************/32", "************/32"}
		assert.Equal(t, uint(2), actual["************"].ID)
		assert.Equal(t, ipRanges.ToCidrs(), actual["************"].Cidrs)
	})

	t.Run("Domains", func(t *testing.T) {
		var ipRangeCidrs []*models.IPRangeCidr
		cidrs := address.IPRangeCIDRs{"************/32", "************/31"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "************-185", IpCount: 3, Cidrs: cidrs.ToCidrs()})
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "fofa.info", IpCount: 1, Cidrs: nil})

		actual := GetIp2IpRange(ipRangeCidrs, []string{"************", "fofa.info"})
		assert.Equal(t, "************-185", actual["************"].IpRange)
		assert.Equal(t, 3, actual["************"].IpCount)

		ipRanges := address.IPRangeCIDRs{"************/32", "************/31"}
		assert.Equal(t, uint(1), actual["************"].ID)
		assert.Equal(t, ipRanges.ToCidrs(), actual["************"].Cidrs)

		assert.Nil(t, actual["fofa.info"].Cidrs)
		assert.Equal(t, 1, actual["fofa.info"].IpCount)
		assert.Equal(t, uint(2), actual["fofa.info"].ID)
		assert.Equal(t, "fofa.info", actual["fofa.info"].IpRange)
	})

	t.Run("Domains2", func(t *testing.T) {
		var ipRangeCidrs []*models.IPRangeCidr
		ips := address.IPRangeCIDRs{"************/32"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "fofa.info", IpCount: 1, Cidrs: ips.ToCidrs()})

		actual := GetIp2IpRange(ipRangeCidrs, []string{"************", "fofa.info"})
		assert.Equal(t, "fofa.info", actual["************"].IpRange)
		assert.Equal(t, 1, actual["************"].IpCount)

		assert.Equal(t, 1, actual["fofa.info"].IpCount)
		assert.Equal(t, uint(1), actual["fofa.info"].ID)
		assert.Equal(t, "fofa.info", actual["fofa.info"].IpRange)
	})
}

func (suite *MySQLSuite) Test_GetIpRangeIds() {
	var ipRangeCidrs []*models.IPRangeCidr
	cidrs := address.IPRangeCIDRs{"************/32", "************/31"}
	ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "************-185", IpCount: 3, Cidrs: cidrs.ToCidrs()})

	cidrs2 := address.IPRangeCIDRs{"************/32", "************/32"}
	ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "************-183", IpCount: 2, Cidrs: cidrs2.ToCidrs()})

	ipMap := GetIp2IpRange(ipRangeCidrs, []string{"************"})
	ipRangeIds := GetIpRangeIds(ipMap)

	assert.Equal(suite.T(), []int{2}, ipRangeIds)

}

func (suite *MySQLSuite) Test_SetIpTags() {
	var ipRangeCidrs []*models.IPRangeCidr
	cidrs := address.IPRangeCIDRs{"************/32", "************/31"}
	ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "************-185", IpCount: 3, Cidrs: cidrs.ToCidrs()})

	cidrs2 := address.IPRangeCIDRs{"************/32", "************/32"}
	ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "************-183", IpCount: 2, Cidrs: cidrs2.ToCidrs()})

	ipMap := GetIp2IpRange(ipRangeCidrs, []string{"************"})

	var tags []*models.IPRangeReferenceTags

	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "业务1", ParentName: "业务系统", ParentRealName: "business_app"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "机房1", ParentName: "机房信息", ParentRealName: "computer_room"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "管理单元1", ParentName: "管理单元", ParentRealName: "company"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "资产等级1", ParentName: "资产等级", ParentRealName: "asset_level"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "自定义aa1", ParentName: "自定义1", ParentRealName: "t_zidingyi1"})

	ipTag := suite.store.IPRange().SetIpTags(tags, ipMap)
	mosso.DebugShowContentWithJSON(ipTag)

	assert.Equal(suite.T(), "self_defined", ipTag["************"].AddWay)
	assert.Equal(suite.T(), "机房1", ipTag["************"].ComputerRoom)
	assert.Equal(suite.T(), "负责人1", ipTag["************"].UserName)
	assert.Equal(suite.T(), "<EMAIL>", ipTag["************"].ManagerEmail)
	assert.Equal(suite.T(), "1313133434", ipTag["************"].ManagerMobile)
	assert.Equal(suite.T(), "管理单元1", ipTag["************"].Company)
	assert.Equal(suite.T(), "业务1", ipTag["************"].BusinessApp)
	assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义1"}, ipTag["************"].CustomNames)
	assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义aa1"}, ipTag["************"].CustomFields)
}

func (suite *MySQLSuite) Test_GetIpTags() {
	t := suite.T()
	t.Run("Get ip tags", func(t *testing.T) {
		var ipRangeCidrs []*models.IPRangeCidr
		cidrs := address.IPRangeCIDRs{"************/32", "************/31"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 1, IpRange: "************-185", IpCount: 3, Cidrs: cidrs.ToCidrs()})

		cidrs2 := address.IPRangeCIDRs{"************/32", "************/32"}
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "************-183", IpCount: 2, Cidrs: cidrs2.ToCidrs()})

		var tags []*models.IPRangeReferenceTags

		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "业务1", ParentName: "业务系统", ParentRealName: "business_app"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "机房1", ParentName: "机房信息", ParentRealName: "computer_room"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "管理单元1", ParentName: "管理单元", ParentRealName: "company"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "资产等级1", ParentName: "资产等级", ParentRealName: "asset_level"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "自定义aa1", ParentName: "自定义1", ParentRealName: "t_zidingyi1"})

		defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskIPRangeTags", tags, nil).Reset()

		ipTag, _ := suite.store.IPRange().GetIpTags([]string{"************"}, ipRangeCidrs)

		assert.Equal(suite.T(), "self_defined", ipTag["************"].AddWay)
		assert.Equal(suite.T(), "机房1", ipTag["************"].ComputerRoom)
		assert.Equal(suite.T(), "负责人1", ipTag["************"].UserName)
		assert.Equal(suite.T(), "<EMAIL>", ipTag["************"].ManagerEmail)
		assert.Equal(suite.T(), "1313133434", ipTag["************"].ManagerMobile)
		assert.Equal(suite.T(), "管理单元1", ipTag["************"].Company)
		assert.Equal(suite.T(), "业务1", ipTag["************"].BusinessApp)
		assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义1"}, ipTag["************"].CustomNames)
		assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义aa1"}, ipTag["************"].CustomFields)
	})

	t.Run("Get domain tags", func(t *testing.T) {
		var ipRangeCidrs []*models.IPRangeCidr
		ipRangeCidrs = append(ipRangeCidrs, &models.IPRangeCidr{ID: 2, IpRange: "fofa.info", IpCount: 1, Cidrs: nil})

		var tags []*models.IPRangeReferenceTags

		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "业务1", ParentName: "业务系统", ParentRealName: "business_app"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "机房1", ParentName: "机房信息", ParentRealName: "computer_room"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "管理单元1", ParentName: "管理单元", ParentRealName: "company"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "资产等级1", ParentName: "资产等级", ParentRealName: "asset_level"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
		tags = append(tags, &models.IPRangeReferenceTags{Id: 2, Name: "自定义aa1", ParentName: "自定义1", ParentRealName: "t_zidingyi1"})

		defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskIPRangeTags", tags, nil).Reset()

		ipTag, _ := suite.store.IPRange().GetIpTags([]string{"fofa.info"}, ipRangeCidrs)

		ip := "fofa.info"
		assert.Equal(suite.T(), "self_defined", ipTag[ip].AddWay)
		assert.Equal(suite.T(), "机房1", ipTag[ip].ComputerRoom)
		assert.Equal(suite.T(), "负责人1", ipTag[ip].UserName)
		assert.Equal(suite.T(), "<EMAIL>", ipTag[ip].ManagerEmail)
		assert.Equal(suite.T(), "1313133434", ipTag[ip].ManagerMobile)
		assert.Equal(suite.T(), "管理单元1", ipTag[ip].Company)
		assert.Equal(suite.T(), "业务1", ipTag[ip].BusinessApp)
		assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义1"}, ipTag[ip].CustomNames)
		assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义aa1"}, ipTag[ip].CustomFields)
	})
}

func (suite *MySQLSuite) Test_IPRange_GetIPRangeIdByTaskId() {
	actual, err := suite.store.IPRange().GetIPRangeIdByTaskId(context.Background(), 14)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), actual, 2)
}

func (suite *MySQLSuite) Test_IPRange_GetTaskIPRangeTags() {
	_, err := suite.store.IPRange().GetTaskIPRangeTags(suite.ctx, []int{1, 2, 3, 4, 5})
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_IPRange_GetTaskIPRangeTag_Nil() {

	actual, err := suite.store.IPRange().GetTaskIPRangeTag(suite.ctx, 1)
	assert.NoError(suite.T(), err)
	assert.Nil(suite.T(), actual)
}

func (suite *MySQLSuite) Test_IPRange_GetTagsIdsWithTaskId() {
	actual, err := suite.store.IPRange().GetTagsIdsWithTaskId(suite.ctx, 1)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	assert.Len(suite.T(), actual, 1)
}

func (suite *MySQLSuite) Test_IPRange_38_Tags_BusinessApp_Condition1() {
	task := models.Task{IPType: 1}
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskWithId", &task, nil).Reset()
	var ids []int
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetIPRangeIdsAllForIPType", ids, nil).Reset()

	var r []*models.IPRangeReferenceTags
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskIPRangeTags", r, nil).Reset()

	_, err := suite.store.IPRange().Tags(suite.ctx, 4, "**********")
	assert.NoError(suite.T(), err)
}

func (suite *MySQLSuite) Test_IPRange_38_Tags_BusinessApp_Condition4() {
	task := models.Task{IPType: 1}
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskWithId", &task, nil).Reset()
	var ids []int
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetIPRangeIdsAllForIPType", ids, nil).Reset()

	var r []*models.IPRangeReferenceTags
	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskIPRangeTags", r, nil).Reset()

	actual, err := suite.store.IPRange().Tags(suite.ctx, 4, "2000::1:2345:6789:abcd")
	assert.NoError(suite.T(), err)
	assert.Nil(suite.T(), actual.CustomFields)
	assert.Nil(suite.T(), actual.CustomNames)
}

func (suite *MySQLSuite) Test_GetDomainTags() {
	suite.store.IPRange().GetDomainTags([]interface{}{"fofa.info"})

	var tags []*models.IPRangeReferenceTags

	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "业务1", ParentName: "业务系统", ParentRealName: "business_app"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "机房1", ParentName: "机房信息", ParentRealName: "computer_room"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "管理单元1", ParentName: "管理单元", ParentRealName: "company"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "资产等级1", ParentName: "资产等级", ParentRealName: "asset_level"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "负责人1", ParentName: "负责人", ParentRealName: "username", ManagerEmail: "<EMAIL>", ManagerMobile: "1313133434"})
	tags = append(tags, &models.IPRangeReferenceTags{Id: 2, IpRange: "fofa.info", Name: "自定义aa1", ParentName: "自定义1", ParentRealName: "t_zidingyi1"})

	defer gomonkey.ApplyMethodReturn(suite.store.IPRange(), "GetTaskDomainTags", tags, nil).Reset()
	ipTag := suite.store.IPRange().GetDomainTags([]interface{}{"fofa.info"})
	t, _ := json.Marshal(ipTag)
	fmt.Print(string(t))

	ip := "fofa.info"
	assert.Equal(suite.T(), "self_defined", ipTag[ip].AddWay)
	assert.Equal(suite.T(), "机房1", ipTag[ip].ComputerRoom)
	assert.Equal(suite.T(), "负责人1", ipTag[ip].UserName)
	assert.Equal(suite.T(), "<EMAIL>", ipTag[ip].ManagerEmail)
	assert.Equal(suite.T(), "1313133434", ipTag[ip].ManagerMobile)
	assert.Equal(suite.T(), "管理单元1", ipTag[ip].Company)
	assert.Equal(suite.T(), "业务1", ipTag[ip].BusinessApp)
	assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义1"}, ipTag[ip].CustomNames)
	assert.Equal(suite.T(), map[string]interface{}{"t_zidingyi1": "自定义aa1"}, ipTag[ip].CustomFields)
}
