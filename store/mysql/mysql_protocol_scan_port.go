package mysql

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"

	"gorm.io/gorm"
)

type protocolScanPort struct {
	db *gorm.DB
}

func newProtocolScanPort(store *datastore) *protocolScanPort {
	return &protocolScanPort{db: store.db}
}

// Create table data for record.
func (store *protocolScanPort) Create(ctx context.Context, data *models.ProtocolScanPort) error {
	return store.db.Create(&data).Error
}

func (store *protocolScanPort) CreateBatch(ctx context.Context, data []*models.ProtocolScanPort) error {
	return store.db.CreateInBatches(&data, 100).Error
}
