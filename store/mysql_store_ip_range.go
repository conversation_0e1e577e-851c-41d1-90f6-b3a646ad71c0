package store

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"git.gobies.org/foeye/foeye-engine-syncasset/module/searcher"
)

type IPRangeStore interface {
	Create(ctx context.Context, data *models.IPRange) error
	CreateBatch(ctx context.Context, data []*models.IPRange) error
	Detail(ctx context.Context, expr *searcher.Expression) (*models.IPRange, error)
	GetIPRangeIdByTaskId(ctx context.Context, taskId int) ([]int, error)
	GetIPRangeIdsAllForIPType(ctx context.Context, iptype int) ([]int, error)
	GetTaskIPRangeTag(ctx context.Context, iprangeId int) (*models.IPRangeReferenceTags, error)
	GetTaskIPRangeTags(ctx context.Context, ids []int) ([]*models.IPRangeReferenceTags, error)
	GetTagsIdsWithTaskId(ctx context.Context, taskId int) ([]int, error)
	Tags(ctx context.Context, taskId int, ip string) (*models.IPRangeTags, error)
	GetIpTags(ips []string, ipRangeCidrs []*models.IPRangeCidr) (map[string]*models.IPRangeTags, error)
	GetDomainTags(domains []interface{}) map[string]*models.IPRangeTags
	GetTaskWithId(ctx context.Context, taskId int) (*models.Task, error)
	GetIPRangeCIDR(ipType int, domainIps map[string][]string) ([]*models.IPRangeCidr, error)
	SetIpTags(tags []*models.IPRangeReferenceTags, ipMap map[string]*models.IPRangeCidr) map[string]*models.IPRangeTags
}
