package storage

import (
	"embed"
	"fmt"
	"testing"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"
	"github.com/stretchr/testify/suite"
)

//go:embed embeds
var embeds embed.FS

func TestStorageSuite(t *testing.T) {
	suite.Run(t, &StorageSuite{})
}

type StorageSuite struct {
	suite.Suite
	factory         store.Factory
	dropMSYQLTables bool
}

func (suite *StorageSuite) BeforeTest(suiteName, testName string) {
	ops := []configure.Option{
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("conf.test"),
	}

	conf := config.GetConfigure(ops...)
	suite.factory = NewStorage(conf, embeds)
}

func (suite *StorageSuite) AfterTest(suiteName, testName string) {
	if suite.dropMSYQLTables {
		fmt.Println("drop mysql tables")
	}
}

func (suite *StorageSuite) Test_MySQLFactory() {
	suite.NotNil(suite.T(), suite.factory)
	suite.NotNil(suite.T(), suite.factory.MYSQL())
}

func (suite *StorageSuite) Test_ElasticFactory() {
	suite.NotNil(suite.T(), suite.factory)
	suite.NotNil(suite.T(), suite.factory.Elastic())
}

func (suite *StorageSuite) Test_RedisFactory() {
	suite.NotNil(suite.T(), suite.factory)
	suite.NotNil(suite.T(), suite.factory.Redis())
}
