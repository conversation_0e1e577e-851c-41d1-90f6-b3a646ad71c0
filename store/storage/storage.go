package storage

import (
	"embed"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"
	"git.gobies.org/foeye/foeye-engine-syncasset/store/elastic"
	"git.gobies.org/foeye/foeye-engine-syncasset/store/mysql"
	"git.gobies.org/foeye/foeye-engine-syncasset/store/redis"
)

type storage struct {
	config *config.Configure
	embeds embed.FS
}

func NewStorage(config *config.Configure, embeds embed.FS) store.Factory {
	return &storage{config: config, embeds: embeds}
}

func (s *storage) Redis() store.RedisFactory {
	or, err := redis.NewRedisClientOr(
		redis.NewDefaultSimpleConfig(
			s.config.Redis.Host,
			s.config.Redis.Password,
			s.config.Redis.Database,
		),
	)
	if err != nil {
		panic(any(err))
	}
	return or
}

func (s *storage) MYSQL() store.MySQLFactory {
	or, err := mysql.GetMySQLFactoryOr(s.config)
	if err != nil {
		panic(any(err))
	}
	return or
}

func (s *storage) Elastic() store.ElasticFactory {
	or, err := elastic.GetElasticFactoryOr(s.config, s.embeds)
	if err != nil {
		panic(any(err))
	}
	return or
}
