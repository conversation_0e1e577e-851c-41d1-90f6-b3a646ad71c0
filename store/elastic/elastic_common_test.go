package elastic

import (
	"path/filepath"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticSuite) TestMustOpenFile() {
	grids := []struct {
		filename string
	}{
		{
			filename: filepath.Join(suite.baseDir, "tmp/test/create/elastic/w/a.txt"),
		},
		{
			filename: filepath.Join(suite.baseDir, "tmp/test/create/elastic/o/a.txt"),
		},
		{
			filename: filepath.Join(suite.baseDir, "tmp/test/create/elastic/r/a.txt"),
		},
		{
			filename: filepath.Join(suite.baseDir, "tmp/test/create/elastic/l/a.txt"),
		},
		{
			filename: filepath.Join(suite.baseDir, "tmp/test/create/elastic/d/a.txt"),
		},
	}

	for _, grid := range grids {
		file, err := MustOpenFile(grid.filename)
		assert.NoError(suite.T(), err)
		assert.NotNil(suite.T(), file)

		err = file.Close()
		assert.NoError(suite.T(), err)
	}
}
