package elastic

import (
	"context"
	"reflect"
	"sync"

	"git.gobies.org/foeye-dependencies/interside"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

const (
	IndexNameOfDomainAssets = "fofaee_domain_assets"
	TypeNameOfDomainAssets  = "ips"
)

type domainAssetsStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newDomainAssetsStore(datastore *datastore) *domainAssetsStore {
	return &domainAssetsStore{
		client: datastore.client,
		index:  IndexNameOfDomainAssets,
		typ:    TypeNameOfDomainAssets,
	}
}

// Count index total data number.
func (t *domainAssetsStore) Count(ctx context.Context) (int, error) {
	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		SearchSource(FetchSourceOfFalse).
		Do(ctx)
	if err != nil {
		return 0, err
	}

	return int(resp.Hits.TotalHits), nil
}

// List Get asset list data.
func (t *domainAssetsStore) List(ctx context.Context, ips interside.Container) (*sync.Map, error) {
	var set = &sync.Map{}

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("_id", ips.InterfaceSlice()...),
	)

	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		Query(query).Sort("ip", true).
		Size(ips.Length()).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	if resp.TotalHits() > 0 {
		var doc models.AssetDocument
		for _, cur := range resp.Each(reflect.TypeOf(doc)) {
			if tmp, ok := cur.(models.AssetDocument); ok {
				set.Store(tmp.IP, tmp)
			}
		}
	}

	return set, nil
}
