package elastic

import (
	"path/filepath"
	"time"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
	"github.com/stretchr/testify/assert"
)

func (suite *ElasticSuite) Test_TaskDomainAssets_BulkInsert() {
	filename := filepath.Join(suite.testDataDir, "index_task_assets_182.json")
	slice, err := fsfire.GetFileContentStringSliceWithFilename(filename)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	resp, err := elasticsearch.BulkInsert(suite.ctx, suite.store.Client(), slice)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
}

func (suite *ElasticSuite) Test_TaskDomainAssets_CountByTaskId() {
	suite.Test_TaskAssets_BulkInsert()

	time.Sleep(1 * time.Second)
	assert.NotNil(suite.T(), suite.store.TaskDomainAssets())
}
