package elastic

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/olivere/elastic"
)

// Search response data pretty related constants' definition.
const (
	IsPrettyOfTrue  = true
	IsPrettyOfFalse = false
)

// Pagination related constants definition.
const (
	DefaultPaginationNumber = 1
	DefaultPaginationSize   = 10
)

// Document related constants definition.
const (
	DefaultDocumentSource = "_source"
	DefaultResultNotFound = "not_found"
)

// DocumentNotFound Client custom error type related constants' definition.
var (
	DocumentNotFound = fmt.Errorf("document not found")
)

const (
	// DefaultQueryBulkSize is the default size a scroll termQueryForIPRange bulk.
	DefaultQueryBulkSize = 100
)

const (
	// DefaultScrollKeepAlive is the default time a scroll cursor will be kept alive.
	DefaultScrollKeepAlive = "5m"
)

// FetchSourceOfFalse fetch source of false.
var FetchSourceOfFalse = elastic.NewSearchSource().FetchSource(false)

// MustOpenFile Must open file, if already exists will clear file content.
func MustOpenFile(filename string) (*os.File, error) {
	dir := filepath.Dir(filename)

	if _, err := os.Stat(dir); err != nil && os.IsNotExist(err) {
		if err := os.MkdirAll(dir, os.ModePerm); err != nil {
			return nil, err
		}
	}

	info, err := os.Stat(filename)
	if err != nil && os.IsNotExist(err) {
		f, err := os.Create(filename)
		if err != nil {
			return nil, err
		}
		return f, nil
	}

	file, err := os.OpenFile(filename, os.O_RDWR, os.ModePerm)
	if err != nil {
		return nil, err
	}

	if info.Size() > 0 {
		if err = file.Truncate(0); err != nil {
			return nil, err
		}
	}

	return file, nil
}
