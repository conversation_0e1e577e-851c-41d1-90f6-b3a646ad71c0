package elastic

import (
	"context"
	"testing"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/logger"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"
)

func TestElasticSuite(t *testing.T) {
	suite.Run(t, &ElasticSuite{})
}

type ElasticSuite struct {
	suite.Suite
	store       store.ElasticFactory
	drop        bool
	ctx         context.Context
	cancel      context.CancelFunc
	baseDir     string
	testDataDir string

	datastore *datastore
}

func (suite *ElasticSuite) SetupSuite() {
	logger.Configuration()
	suite.baseDir = "./../../"
	// suite.drop = true

	ops := []configure.Option{
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("conf.test"),
	}

	conf := config.GetConfigure(ops...)
	assert.NotNil(suite.T(), conf)

	var err error
	suite.store, err = GetElasticFactoryOr(conf, embeds)
	assert.NoError(suite.T(), err)

	suite.ctx, suite.cancel = context.WithCancel(context.Background())
	assert.NotNil(suite.T(), suite.ctx)
	assert.NotNil(suite.T(), suite.cancel)

	suite.testDataDir = fsfire.MustGetFilePathWithFileSystemPath(
		suite.baseDir, fsfire.WithSpecificFileSystemPath("tmp/test/data/elastic"),
	)

	suite.datastore = &datastore{client: suite.store.Client()}
	assert.NotNil(suite.T(), suite.datastore)
}

func (suite *ElasticSuite) TearDownSuite() {
	indices := []string{IndexNameOfAssets, IndexNameOfTaskAssets, IndexNameOfService}
	if suite.drop {
		for _, index := range indices {
			resp, err := elasticsearch.DeleteIndexIfExists(suite.ctx, suite.store.Client(), index)
			assert.NoError(suite.T(), err)
			assert.NotNil(suite.T(), resp)
		}
	}
}

func (suite *ElasticSuite) Test_TaskAssets() {
	assets := suite.datastore.TaskAssets()
	assert.NotNil(suite.T(), assets)
}

func (suite *ElasticSuite) Test_Assets() {
	assets := suite.datastore.Assets()
	assert.NotNil(suite.T(), assets)
}
