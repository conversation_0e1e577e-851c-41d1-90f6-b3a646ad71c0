package elastic

import (
	"encoding/json"
	"io"
	"path/filepath"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
)

func (suite *ElasticSuite) Test_Subdomain_BulkInsert() {
	filename := filepath.Join(suite.testDataDir, "index_subdomain.json")
	slice, err := fsfire.GetFileContentStringSliceWithFilename(filename)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	resp, err := elasticsearch.BulkInsert(suite.ctx, suite.store.Client(), slice)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
}

func (suite *ElasticSuite) Test_GetFraudAndCharsetByIps() {
	suite.Test_Subdomain_BulkInsert()

	time.Sleep(1 * time.Second)
	res, err := suite.store.Subdomain().GetFraudAndCharsetByIps([]string{"**************"})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *ElasticSuite) Test_GetDomainIpsByDomains() {
	rawdata := `{
		"_index": "fofaee_subdomain",
		"_type": "subdomain",
		"_id": "fofa.info",
		"_source": {			
			"ip": "************",			
			"host": "fofa.info"
		}
	}`
	ss := &elastic.ScrollService{}
	var hit *elastic.SearchHit
	err := json.Unmarshal([]byte(rawdata), &hit)
	assert.NoError(suite.T(), err)
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(1), Hits: []*elastic.SearchHit{hit}}

	outputs := []gomonkey.OutputCell{
		{Values: gomonkey.Params{&searchResult, nil}},
		{Values: gomonkey.Params{nil, io.EOF}},
	}
	defer gomonkey.ApplyMethodSeq(ss.Scroll("a"), "Do", outputs).Reset()

	res, err := suite.store.Subdomain().GetDomainIpsByDomains([]string{"fofa.info"})
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), []string{"************"}, res["fofa.info"])
}
