package elastic

import (
	"path/filepath"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/interside"
)

func (suite *ElasticSuite) Test_Assets_BulkInsert() {
	filename := filepath.Join(suite.testDataDir, "index_assets.json")
	slice, err := fsfire.GetFileContentStringSliceWithFilename(filename)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	bulk := suite.store.Client().Bulk()
	defer gomonkey.ApplyMethodReturn(bulk.Add(), "Do", nil, nil).Reset()

	_, err = elasticsearch.BulkInsert(suite.ctx, suite.store.Client(), slice)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticSuite) Test_Asset_Count() {
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(10)}
	defer gomonkey.ApplyMethodReturn(suite.store.Client().Search().Index(""), "Do", &searchResult, nil).Reset()

	actual, err := suite.store.Assets().Count(suite.ctx)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 10, actual)
}

func (suite *ElasticSuite) Test_Asset_List() {
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(10)}
	defer gomonkey.ApplyMethodReturn(suite.store.Client().Search().Index(""), "Do", &searchResult, nil).Reset()

	var container = interside.NewContainer()
	container.Append(
		"************",
		"************",
		"************",
		"************",
		"************",
		"************",
		"*************",
		"*************",
		"************",
		"*************",
	)

	actual, err := suite.store.Assets().List(suite.ctx, container)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)

	var ips = make([]string, 0)

	actual.Range(func(key, value interface{}) bool {
		ips = append(ips, key.(string))
		return true
	})

	assert.Len(suite.T(), ips, 0)
}
