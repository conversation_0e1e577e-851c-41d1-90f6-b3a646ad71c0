package elastic

import (
	"context"
	"reflect"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye-dependencies/interside"
)

const (
	IndexNameOfAssets = "fofaee_assets"
	TypeNameOfAssets  = "ips"
)

type assetsStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newAssetsStore(datastore *datastore) *assetsStore {
	return &assetsStore{
		client: datastore.client,
		index:  IndexNameOfAssets,
		typ:    TypeNameOfAssets,
	}
}

// Count index total data number.
func (t *assetsStore) Count(ctx context.Context) (int, error) {
	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		SearchSource(FetchSourceOfFalse).
		Do(ctx)
	if err != nil {
		return 0, err
	}

	return int(resp.Hits.TotalHits), nil
}

// RulesCount rules count data number.
func (t *assetsStore) RulesCount(ctx context.Context, taskId int) (int, error) {

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("task_id", taskId),
	)

	aggregation := elastic.NewTermsAggregation().Field("rule_infos.title")

	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		Query(query).
		Aggregation("rule_infos", aggregation).
		Do(ctx)

	if err != nil {
		return 0, err
	}

	return int(resp.Hits.TotalHits), nil
}

// List Get asset list data.
func (t *assetsStore) List(ctx context.Context, ips interside.Container) (*sync.Map, error) {
	var set = &sync.Map{}

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermsQuery("_id", ips.InterfaceSlice()...),
	)

	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		Query(query).Sort("ip", true).
		Size(ips.Length()).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	if resp.TotalHits() > 0 {
		var doc models.AssetDocument
		for _, cur := range resp.Each(reflect.TypeOf(doc)) {
			if tmp, ok := cur.(models.AssetDocument); ok {
				set.Store(tmp.IP, tmp)
			}
		}
	}

	return set, nil
}
