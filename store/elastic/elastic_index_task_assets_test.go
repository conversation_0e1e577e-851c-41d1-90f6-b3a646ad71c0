package elastic

import (
	"encoding/json"
	"io"
	"path/filepath"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticSuite) Test_TaskAssets_BulkInsert() {
	filename := filepath.Join(suite.testDataDir, "index_task_assets_182.json")
	slice, err := fsfire.GetFileContentStringSliceWithFilename(filename)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	resp, err := elasticsearch.BulkInsert(suite.ctx, suite.store.Client(), slice)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
}

func (suite *ElasticSuite) Test_TaskAssets_CountByTaskId() {
	suite.Test_TaskAssets_BulkInsert()

	time.Sleep(1 * time.Second)
	actual, err := suite.store.TaskAssets().CountByTaskId(suite.ctx, 1)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 101, actual)
}

func (suite *ElasticSuite) Test_GetIpsByDomains() {
	rawdata := `{
		"_index": "fofaee_task_assets",
		"_type": "ips",
		"_id": "22_************",
		"_source": {			
			"ip": "************",			
			"hosts": ["fofa.info", "te.fofa.info"]
		}
	}`
	ss := &elastic.ScrollService{}
	var hit *elastic.SearchHit
	err := json.Unmarshal([]byte(rawdata), &hit)
	assert.NoError(suite.T(), err)
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(1), Hits: []*elastic.SearchHit{hit}}

	outputs := []gomonkey.OutputCell{
		{Values: gomonkey.Params{&searchResult, nil}},
		{Values: gomonkey.Params{nil, io.EOF}},
	}
	defer gomonkey.ApplyMethodSeq(ss.Scroll("a"), "Do", outputs).Reset()

	res, err := suite.store.TaskAssets().GetIpsByDomains([]string{"fofa.info"}, 11)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), []string{"************"}, res["fofa.info"])
	assert.Equal(suite.T(), []string{"************"}, res["te.fofa.info"])
}
