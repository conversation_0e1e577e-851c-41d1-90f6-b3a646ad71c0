package elastic

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"sync"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

const (
	IndexNameOfTaskAssets = "fofaee_task_assets"
	TypeNameOfTaskAssets  = "ips"
)

type taskAssetsStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newTaskAssetsStore(datastore *datastore) *taskAssetsStore {
	return &taskAssetsStore{
		client: datastore.client,
		index:  IndexNameOfTaskAssets,
		typ:    TypeNameOfTaskAssets,
	}
}

// GetDetailForIP Get detail for ip.
//func (t *taskAssetsStore) GetDetailForIP(ctx context.Context, ip string, ops ...store.Option) (*elastic.SearchHit, error) {
//	if ip == "" {
//		return nil, fmt.Errorf("ip parameter can't empty")
//	}
//
//	resp, err := t.client.Search().Index(t.index).Type(t.typ).
//		Query(elastic.NewTermsQuery("ip", ip)).
//		Size(DefaultQueryBulkSize).
//		Do(ctx)
//
//	if err != nil {
//		return nil, err
//	}
//
//	if resp.Hits.TotalHits < 1 {
//		return nil, fmt.Errorf("ip %s doucment is not found", ip)
//	}
//
//	return resp.Hits.Hits[0], nil
//}

func (t *taskAssetsStore) GetListAssignToAssets(ctx context.Context, taskId, from,
	size int) (models.AssetDocuments, error) {
	termQuery := elastic.NewTermQuery("task_id", taskId)

	r, err := t.client.Search().Index(t.index).Type(t.typ).
		Query(termQuery).
		Sort("ip", true).
		From(from).
		Size(size).
		Do(ctx)
	if err != nil {
		return nil, err
	}

	var assets = make([]*models.AssetDocument, 0, size)
	logger.Infow(fmt.Sprintf("[TASK_ASSET]Get task asset TotalHits: %d", r.TotalHits()))
	if r.TotalHits() > 0 {
		for _, hit := range r.Hits.Hits {
			data, err := hit.Source.MarshalJSON()
			if err != nil {
				logger.Infow("[TASK_ASSET]Get task asset MarshalJSON to failed",
					"error", err)
				continue
			}

			var datum *models.AssetDocument
			if err = json.Unmarshal(data, &datum); err != nil {
				logger.Infow("[TASK_ASSET]Get task asset Unmarshal to AssetDocument to failed",
					"error", err)
				continue
			}
			for i, item := range datum.PortList {
				switch item.IsHoneypot.(type) {
				case string:
					datum.PortList[i].IsHoneypot = item.IsHoneypot.(string) == "true"
				}
			}

			assets = append(assets, datum)
		}
	}

	return assets, nil
}

//func (t *taskAssetsStore) GetListForIPRange(ctx context.Context, ops ...store.Option) (interface{}, error) {
//	options := &store.Options{}
//
//	for _, o := range ops {
//		o.Apply(options)
//	}
//
//	if options.IPRange == nil {
//		return nil, fmt.Errorf("ip range can't empty")
//	}
//
//	source := elastic.NewSearchSource().DocvalueFields("ip")
//	finals := make([]map[string]string, 0)
//	query, err := t.termQueryForIPRange(options)
//	if err != nil {
//		return nil, err
//	}
//
//	search := t.client.Scroll().
//		Scroll(elastic.DefaultScrollKeepAlive).
//		Index(t.index).
//		Type(t.typ).
//		SearchSource(source).
//		Query(query).
//		Size(DefaultQueryBulkSize)
//	result, err := search.Do(ctx)
//
//	var numbers int
//	for {
//		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
//			for _, hit := range result.Hits.Hits {
//				numbers++
//				if hit.Fields != nil {
//					values, err := t.fixedMapValues(hit.Fields)
//					if err != nil {
//						fmt.Printf("fixed hit.Fields map to failed: %+v\n", err)
//						continue
//					}
//					finals = append(finals, values)
//				}
//			}
//
//			// Proceed to the next read.
//			result, err = search.ScrollId(result.ScrollId).Do(ctx)
//			if err != nil {
//				break
//			}
//		} else {
//			break
//		}
//	}
//
//	if err != nil && err != io.EOF {
//		return nil, err
//	}
//
//	fmt.Printf("numbers: %+v\n", numbers)
//
//	return finals, nil
//}

//func (t *taskAssetsStore) termQueryForIPRange(options *store.Options) (*elastic.TermsQuery, error) {
//	query := &elastic.TermsQuery{}
//
//	switch val := options.IPRange.(type) {
//	case []interface{}:
//		query = elastic.NewTermsQuery("ip", val...)
//	case []string:
//		converted := make([]interface{}, 0, len(val))
//		for _, item := range val {
//			converted = append(converted, item)
//		}
//		query = elastic.NewTermsQuery("ip", converted...)
//	case string:
//		query = elastic.NewTermsQuery("ip", val)
//	default:
//		return nil, fmt.Errorf("ip range type unsupported: %s", options.IPRange)
//	}
//	return query, nil
//}

//func (t *taskAssetsStore) fixedMapValues(fields map[string]interface{}) (map[string]string, error) {
//	var r = make(map[string]string)
//	for key, value := range fields {
//		if value != nil {
//			if parse, ok := value.([]interface{}); ok && len(parse) != 0 {
//				r[key] = parse[0].(string)
//			}
//		}
//	}
//	return r, nil
//}

// Count index total data number.
//func (t *taskAssetsStore) Count(ctx context.Context) (int, error) {
//	resp, err := t.client.
//		Search().
//		Index(t.index).
//		Type(t.typ).
//		SearchSource(FetchSourceOfFalse).
//		Do(ctx)
//	if err != nil {
//		return 0, err
//	}
//
//	return int(resp.Hits.TotalHits), nil
//}

// CountByTaskId count by task id.
func (t *taskAssetsStore) CountByTaskId(ctx context.Context, taskId int) (int, error) {
	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("task_id", taskId),
	)
	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		SearchSource(FetchSourceOfFalse).
		Query(query).
		Do(ctx)
	if err != nil {
		return 0, err
	}

	return int(resp.TotalHits()), nil
}

// IPCount index total data number for task id.
func (t *taskAssetsStore) IPCount(ctx context.Context, taskId int) (int, error) {
	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("task_id", taskId),
	)

	aggregation := elastic.NewTermsAggregation().Field("task_id").Size(10000000)

	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		SearchSource(FetchSourceOfFalse).
		Query(query).
		Aggregation("task_id", aggregation).
		Do(ctx)
	if err != nil {
		return 0, err
	}

	return int(resp.Hits.TotalHits), nil
}

// RuleCount RulesCount rules count data number.
func (t *taskAssetsStore) RuleCount(ctx context.Context, taskId int) (int, error) {

	query := elastic.NewBoolQuery().Must(
		elastic.NewTermQuery("task_id", taskId),
	)

	aggregation := elastic.NewTermsAggregation().Field("rule_infos.rule_id").Size(10000000)
	name := "rules"

	resp, err := t.client.
		Search().
		Index(t.index).
		Type(t.typ).
		SearchSource(FetchSourceOfFalse).
		Query(query).
		Aggregation(name, aggregation).
		Do(ctx)

	if err != nil {
		return 0, err
	}

	rules := exchange.GetAggregation(
		resp.Aggregations,
		name,
	)

	return rules.Buckets.TotalDocCount(), nil
}

// Delete delete index documents.
func (t *taskAssetsStore) Delete(ctx context.Context, taskId int) (int, error) {
	query := elastic.NewTermQuery("task_id", taskId)
	resp, err := t.client.DeleteByQuery(t.index).Refresh("true").Query(query).Do(ctx)
	if err != nil {
		return 0, err
	}
	return int(resp.Deleted), nil
}

// GetIpsByDomains 通过域名获取ip.
func (t *taskAssetsStore) GetIpsByDomains(hosts []string, taskId int) (map[string][]string, error) {
	ipsQuery := make([]interface{}, 0)
	for _, tmp := range hosts {
		ipsQuery = append(ipsQuery, tmp)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("task_id", taskId))
	boolQuery.Must(elastic.NewTermsQuery("hosts", ipsQuery...))

	domainIps := make(map[string][]string, 0)
	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("ip", "hosts")
	ss := t.client.Scroll().
		Index(t.index).
		Type(t.typ).
		FetchSourceContext(fetchSourceContext).
		Query(boolQuery).
		Size(5000)
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if elastic.IsNotFound(err) {
			break
		}
		if err != nil {
			break
		}

		for _, hit := range sr.Hits.Hits {
			tmp := new(models.HostIpDocument)
			err := json.Unmarshal(*hit.Source, &tmp)
			if err != nil {
				continue
			}
			for _, host := range tmp.Hosts {
				domainIps[host] = append(domainIps[host], tmp.Ip)
			}
		}
	}

	return domainIps, nil
}
