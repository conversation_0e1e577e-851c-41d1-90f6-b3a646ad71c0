package elastic

import (
	"sync"

	"github.com/olivere/elastic"
)

const (
	IndexNameOfTaskDomainAssets = "fofaee_task_domain_assets"
	TypeNameOfTaskDomainAssets  = "ips"
)

type taskDomainAssetsStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newTaskDomainAssetsStore(datastore *datastore) *taskDomainAssetsStore {
	return &taskDomainAssetsStore{
		client: datastore.client,
		index:  IndexNameOfTaskDomainAssets,
		typ:    TypeNameOfTaskDomainAssets,
	}
}
