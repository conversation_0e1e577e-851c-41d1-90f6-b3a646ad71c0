{"settings": {"index": {"number_of_shards": "5", "blocks": {"read_only_allow_delete": "false"}, "max_result_window": "10000000", "analysis": {"analyzer": {"fofa_dot_analyzer": {"type": "custom", "char_filter": ["fofa_dot_to_space"], "tokenizer": "fofa_dot_tokenizer"}}, "char_filter": {"fofa_dot_to_space": {"pattern": "[.:/]", "type": "pattern_replace", "replacement": " "}}, "tokenizer": {"fofa_dot_tokenizer": {"type": "whitespace"}}}, "number_of_replicas": "1"}}, "mappings": {"subdomain": {"dynamic_templates": [{"string_fields": {"match": "*", "match_mapping_type": "string", "mapping": {"fields": {"raw": {"ignore_above": 256, "index": true, "type": "keyword"}}, "index": true, "norms": false, "type": "text"}}}], "properties": {"LocUrl": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "appserver": {"type": "keyword"}, "body": {"type": "text"}, "cert": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "domain": {"type": "keyword"}, "geoip": {"dynamic": "true", "properties": {"city_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "continent_code": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_code2": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_code3": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "country_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "latitude": {"type": "long"}, "location": {"type": "geo_point"}, "longitude": {"type": "long"}, "postal_code": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "real_region_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "region_name": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "timezone": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}, "gid": {"type": "keyword"}, "header": {"type": "text", "fields": {"header_raw": {"type": "keyword"}}}, "host": {"type": "text", "fields": {"host": {"type": "text", "analyzer": "fofa_dot_analyzer"}, "host_raw": {"type": "keyword"}}}, "ip": {"type": "ip", "fields": {"ip_raw": {"type": "keyword"}, "ipstr": {"type": "text", "analyzer": "fofa_dot_analyzer"}}}, "ipcnet": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "is_ipv6": {"type": "boolean"}, "language": {"type": "keyword"}, "lastchecktime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "lastupdatetime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "middleware": {"type": "keyword"}, "os": {"type": "keyword"}, "port": {"type": "keyword", "fields": {"port": {"type": "integer"}, "port_raw": {"type": "keyword"}}}, "product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "protocol": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "rule_tags": {"properties": {"category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_company": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_parent_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "cn_product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "company": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "level": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "parent_category": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "product": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "rule_id": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}, "softhard": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}, "server": {"type": "text", "fields": {"server_raw": {"type": "keyword"}}}, "status_code": {"type": "long"}, "subdomain": {"type": "keyword"}, "tags": {"type": "keyword"}, "title": {"type": "text", "fields": {"title_raw": {"type": "keyword"}}}, "user_tags": {"type": "keyword"}, "v": {"type": "long"}, "version": {"type": "text", "norms": false, "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}}}