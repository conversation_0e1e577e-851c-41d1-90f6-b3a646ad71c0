{"settings": {"index": {"mapping": {"total_fields": {"limit": "1000"}}, "number_of_shards": "5", "max_result_window": "10000000", "analysis": {"analyzer": {"fofa_dot_analyzer": {"type": "custom", "char_filter": ["fofa_dot_to_space"], "tokenizer": "fofa_dot_tokenizer"}, "case_sensitive": {"filter": "lowercase", "type": "custom", "tokenizer": "keyword"}}, "char_filter": {"fofa_dot_to_space": {"pattern": "[.:/]", "type": "pattern_replace", "replacement": " "}}, "tokenizer": {"fofa_dot_tokenizer": {"type": "whitespace"}}}, "number_of_replicas": "1"}}, "mappings": {"threats": {"dynamic_templates": [{"string_fields": {"match": "*", "match_mapping_type": "string", "mapping": {"index": true, "norms": false, "type": "keyword"}}}], "properties": {"add_way": {"type": "keyword"}, "addition": {"type": "keyword"}, "belong_user_id": {"type": "integer"}, "business_app": {"type": "keyword"}, "cat_tags": {"type": "keyword"}, "city": {"type": "keyword"}, "common_description": {"type": "keyword"}, "common_impact": {"type": "keyword"}, "common_title": {"type": "text", "fields": {"raw": {"type": "keyword"}}, "analyzer": "case_sensitive", "fielddata": true}, "company": {"type": "keyword"}, "company_tags": {"type": "keyword"}, "computer_room": {"type": "keyword"}, "country": {"type": "keyword"}, "createtime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "cveId": {"type": "keyword"}, "descriptions": {"type": "keyword"}, "gid": {"type": "keyword"}, "has_exp": {"type": "long"}, "has_response": {"type": "long"}, "hostinfo": {"type": "keyword"}, "intranet_ip": {"type": "keyword"}, "ip": {"type": "ip", "fields": {"ip_raw": {"type": "keyword"}, "ipstr": {"type": "text", "analyzer": "fofa_dot_analyzer"}}}, "is_ipv6": {"type": "boolean"}, "last_response": {"type": "keyword"}, "lastchecktime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "lastupdatetime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "level": {"type": "integer"}, "mac": {"type": "keyword"}, "manager_email": {"type": "keyword"}, "manager_mobile": {"type": "keyword"}, "merge_md5": {"type": "keyword"}, "name": {"type": "keyword"}, "net_bios": {"type": "keyword"}, "noticetime": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "obj_type": {"type": "keyword"}, "object": {"type": "keyword"}, "operating_company": {"type": "keyword"}, "operator": {"type": "keyword"}, "os": {"type": "keyword"}, "port": {"type": "keyword"}, "port_list": {"properties": {"banner": {"type": "keyword"}, "certs": {"properties": {"domain": {"type": "keyword"}, "issuer_cn": {"type": "keyword"}, "issuer_org": {"type": "keyword"}, "not_after": {"type": "keyword"}, "not_before": {"type": "keyword"}, "sig_alth": {"type": "keyword"}, "sn": {"type": "keyword"}, "subject_cn": {"type": "keyword"}, "subject_org": {"type": "keyword"}, "v": {"type": "keyword"}}}, "port": {"type": "long"}, "protocol": {"type": "keyword"}}}, "province": {"type": "keyword"}, "recommandation": {"type": "keyword"}, "repair_usetime": {"type": "integer"}, "repaired_time": {"type": "date", "format": "YYYY-MM-dd HH:mm:ss"}, "rule_infos": {"properties": {"belong_level": {"type": "long"}, "company": {"type": "keyword"}, "first_cat_tag": {"type": "keyword"}, "level_code": {"type": "long"}, "ports": {"type": "long"}, "rule_id": {"type": "long"}, "second_cat_tag": {"type": "keyword"}, "soft_hard_code": {"type": "long"}, "title": {"type": "keyword"}}}, "rule_tags": {"type": "keyword"}, "scan_engine": {"type": "keyword"}, "state": {"type": "integer"}, "task_ids": {"type": "keyword"}, "uploaded": {"type": "integer"}, "url": {"type": "keyword"}, "username": {"type": "keyword"}, "vulType": {"type": "keyword"}, "vulfile": {"type": "keyword"}}}}}