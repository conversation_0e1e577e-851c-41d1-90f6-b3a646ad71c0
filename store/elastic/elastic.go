package elastic

import (
	"context"
	"embed"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"

	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
	"git.gobies.org/foeye-dependencies/logger"
	"github.com/olivere/elastic"
	"github.com/pkg/errors"
)

type datastore struct {
	client *elastic.Client
}

func (d *datastore) Assets() store.AssetsStore {
	return newAssetsStore(d)
}

func (d *datastore) TaskAssets() store.TaskAssetsStore {
	return newTaskAssetsStore(d)
}

func (d *datastore) DomainAssets() store.DomainAssetsStore {
	return newDomainAssetsStore(d)
}

func (d *datastore) TaskDomainAssets() store.TaskDomainAssetsStore {
	return newTaskDomainAssetsStore(d)
}

func (d *datastore) Service() store.ServiceStore {
	return newServiceStore(d)
}

//func (d *datastore) Threats() store.ThreatsStore {
//	return newThreatsStore(d)
//}

func (d *datastore) Subdomain() store.SubdomainStore {
	return newSubdomainStore(d)
}

func (d *datastore) Client() *elastic.Client {
	return d.client
}

var (
	elasticclient *elastic.Client
	factory       store.ElasticFactory
	once          sync.Once
)

//go:embed embeds
var embeds embed.FS

func GetElasticFactoryOr(config *config.Configure, embeds embed.FS) (store.ElasticFactory, error) {
	var err error

	once.Do(func() {

		ops := []elasticsearch.Option{
			elasticsearch.WithSetURL(config.Elastic.Host),
			elasticsearch.WithSniff(false),
			elasticsearch.WithHealthCheckInterval(10 * time.Second),
			elasticsearch.WithErrorLog(log.New(os.Stderr, "ELASTIC ", log.LstdFlags)),
			elasticsearch.WithInfoLog(log.New(os.Stdout, "", log.LstdFlags)),
		}

		if config.Elastic.ShowTraceLog {
			ops = append(ops, elasticsearch.WithTraceLog(log.New(os.Stdout, "", log.LstdFlags)))
		}

		// Initialize elastic client instance.
		elasticclient, err = elasticsearch.NewConnection(ops...)

		if err != nil {
			return
		}

		factory = &datastore{client: elasticclient}
	})

	if err != nil {
		return nil, errors.Wrapf(err, "failed to initialize elastic client")
	}

	if factory == nil {
		return nil, fmt.Errorf("failed to get elastic store factory, factory: %+v, error: %w", factory, err)
	}

	// Resolve test drop database table issue.
	if config.Elastic.AutoMigrate {
		// Use auto migrate.
		err = autoMigrate(elasticclient, embeds)
		if err != nil {
			fmt.Printf("has error\n")
			return nil, err
		}
	}

	return factory, err
}

func autoMigrate(client *elastic.Client, embeds embed.FS) error {
	grids := []struct {
		filename string
		index    string
	}{
		{
			filename: "index_assets.json",
			index:    IndexNameOfAssets,
		},
		{
			filename: "index_task_assets.json",
			index:    IndexNameOfTaskAssets,
		},
	}

	for _, grid := range grids {
		fmt.Printf("AutoMigrate: %+v\n", grid.filename)
		err := migration(client, embeds, grid.filename, grid.index)
		if err != nil {
			return err
		}
	}

	return nil
}

func migration(client *elastic.Client, embeds embed.FS, filename, index string) error {
	data, err := fsfire.GetFileContentBytesWithEmbedFS(embeds, fmt.Sprintf("embeds/elastic/index/settings/%s", filename))
	if err != nil {
		return errors.Wrapf(err, "get elastic setting to failed")
	}

	reps, err := elasticsearch.CreateIndexIfNotExists(context.TODO(), client, index, string(data))
	if err != nil {
		return errors.Wrapf(err, "index not exists create to failed")
	}

	logger.Infow("elastic auto migrate", "response", reps)
	return nil
}
