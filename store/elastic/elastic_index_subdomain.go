package elastic

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"sync"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

const (
	IndexNameOfSubdomain = "fofaee_subdomain"
	TypeNameOfSubdomain  = "subdomain"
)

type subdomainStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newSubdomainStore(datastore *datastore) *subdomainStore {
	return &subdomainStore{
		client: datastore.client,
		index:  IndexNameOfSubdomain,
		typ:    TypeNameOfSubdomain,
	}
}

func (t *subdomainStore) GetFraudAndCharsetByIps(ips []string) (map[string]*models.SubdomainFraudAndCharsetDocument, error) {
	data := make(map[string]*models.SubdomainFraudAndCharsetDocument)

	ipsQuery := make([]interface{}, 0)
	for _, tmp := range ips {
		ipsQuery = append(ipsQuery, tmp)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ipsQuery...))

	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("ip", "fraud_name", "is_fraud", "charset", "port")
	ss := t.client.Scroll().
		Index(t.index).
		Type(t.typ).
		FetchSourceContext(fetchSourceContext).
		Query(boolQuery).
		Size(5000)
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if elastic.IsNotFound(err) {
			break
		}
		if err != nil {
			break
		}
		for _, hit := range sr.Hits.Hits {
			tmp := new(models.SubdomainFraudAndCharsetDocument)
			err := json.Unmarshal(*hit.Source, &tmp)
			if err != nil {
				continue
			}
			if _, ok := data[fmt.Sprintf("%s:%d", tmp.Ip, tmp.Port)]; !ok {
				data[fmt.Sprintf("%s:%d", tmp.Ip, tmp.Port)] = tmp
			}
		}
	}

	return data, nil
}

func (t *subdomainStore) GetDomainIpsByDomains(hosts []string) (map[string][]string, error) {
	ipsQuery := make([]interface{}, 0)
	for _, tmp := range hosts {
		ipsQuery = append(ipsQuery, tmp)
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("host.host_raw", ipsQuery...))

	domainIps := make(map[string][]string, 0)
	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("ip", "host")
	ss := t.client.Scroll().
		Index(t.index).
		Type(t.typ).
		FetchSourceContext(fetchSourceContext).
		Query(boolQuery).
		Size(5000)
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if elastic.IsNotFound(err) {
			break
		}
		if err != nil {
			break
		}

		for _, hit := range sr.Hits.Hits {
			tmp := new(models.SubdomainHostIpDocument)
			err := json.Unmarshal(*hit.Source, &tmp)
			if err != nil {
				continue
			}

			domainIps[tmp.Host] = append(domainIps[tmp.Host], tmp.Ip)
		}
	}

	return domainIps, nil
}
