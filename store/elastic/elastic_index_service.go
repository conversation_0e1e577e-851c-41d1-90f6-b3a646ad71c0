package elastic

import (
	"context"
	"encoding/json"
	"fmt"
	"git.gobies.org/foeye/foeye-engine-syncasset/models"
	"github.com/olivere/elastic"
	"sync"
)

const (
	IndexNameOfService = "fofaee_service"
	TypeNameOfService  = "service"
)

type serviceStore struct {
	client   *elastic.Client
	index    string
	typ      string
	fileLock sync.Mutex
}

func newServiceStore(datastore *datastore) *serviceStore {
	return &serviceStore{
		client: datastore.client,
		index:  IndexNameOfService,
		typ:    TypeNameOfService,
	}
}

func (t *serviceStore) GetHoneypotByIps(ips []string) (map[string]*models.ServiceHoneypotDocument, error) {
	data := make(map[string]*models.ServiceHoneypotDocument)

	ipsQuery := make([]interface{}, 0)
	for _, tmp := range ips {
		if _, ok := data[tmp]; !ok {
			data[tmp] = new(models.ServiceHoneypotDocument)
			ipsQuery = append(ipsQuery, tmp)
		}
	}
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ipsQuery...))
	boolQuery.Must(elastic.NewTermQuery("is_honeypot", true))

	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("ip", "is_honeypot", "honeypot_name")
	r, err := t.client.Search().
		Index(t.index).
		Type(t.typ).
		FetchSourceContext(fetchSourceContext).
		Query(boolQuery).
		Size(10000).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for _, hit := range r.Hits.Hits {
		tmp := new(models.ServiceHoneypotDocument)
		err := json.Unmarshal(*hit.Source, &tmp)
		if err != nil {
			continue
		}
		if v, ok := data[tmp.Ip]; ok {
			v.IsHoneypot = tmp.IsHoneypot
			v.HoneypotName += fmt.Sprintf(",%s", tmp.HoneypotName)
			v.Ip = tmp.Ip
		}
	}

	return data, nil
}
