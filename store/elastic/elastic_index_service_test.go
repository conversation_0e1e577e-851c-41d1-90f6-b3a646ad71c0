package elastic

import (
	"git.gobies.org/foeye-dependencies/connecter/elasticsearch"
	"git.gobies.org/foeye-dependencies/fsfire"
	"github.com/stretchr/testify/assert"
	"path/filepath"
	"time"
)

func (suite *ElasticSuite) Test_Service_BulkInsert() {
	filename := filepath.Join(suite.testDataDir, "index_service.json")
	slice, err := fsfire.GetFileContentStringSliceWithFilename(filename)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	resp, err := elasticsearch.BulkInsert(suite.ctx, suite.store.Client(), slice)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
}

func (suite *ElasticSuite) Test_GetHoneypotByIps() {
	suite.Test_Service_BulkInsert()

	time.Sleep(1 * time.Second)
	res, err := suite.store.Service().GetHoneypotByIps([]string{"**************"})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}
