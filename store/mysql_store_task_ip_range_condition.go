package store

import (
	"context"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

type TaskIPRangeConditionStore interface {
	List(ctx context.Context, condition map[string]interface{}) (models.IPRangeConditionsList, error)
	ListWithTaskId(ctx context.Context, taskId int) (models.IPRangeConditionsList, error)
	GetTagListWithTaskId(ctx context.Context, taskId int) (models.IPRangeConditionsList, error)
	Create(ctx context.Context, data *models.IPRangeConditions) error
	CreateBatch(ctx context.Context, data models.IPRangeConditionsList) error
	Detail(ctx context.Context, id int) (*models.IPRangeConditions, error)
}
