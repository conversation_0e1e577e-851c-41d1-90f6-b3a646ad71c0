package store

type Option interface {
	Apply(*Options)
}

type OptionFunc func(*Options)

func (o OptionFunc) Apply(ops *Options) {
	o(ops)
}

type Options struct {
	Pretty   bool
	Size     int
	Number   int
	Keyword  string
	Status   string
	Ids      []string
	IPRange  interface{}
	Metadata interface{}
}

func WithPretty(pretty bool) Option {
	return OptionFunc(func(ops *Options) {
		ops.Pretty = pretty
	})
}

func WithSize(size int) Option {
	return OptionFunc(func(ops *Options) {
		ops.Size = size
	})
}

func WithIDs(ids []string) Option {
	return OptionFunc(func(ops *Options) {
		ops.Ids = ids
	})
}

func WithNumber(number int) Option {
	return OptionFunc(func(ops *Options) {
		ops.Number = number
	})
}

func WithKeyword(keyword string) Option {
	return OptionFunc(func(ops *Options) {
		ops.Keyword = keyword
	})
}

func WithStatus(status string) Option {
	return OptionFunc(func(ops *Options) {
		ops.Status = status
	})
}

func WithMetadata(metadata interface{}) Option {
	return OptionFunc(func(ops *Options) {
		ops.Metadata = metadata
	})
}

func WithIPRange(iprange interface{}) Option {
	return OptionFunc(func(ops *Options) {
		ops.IPRange = iprange
	})
}
