package store

import "github.com/olivere/elastic"

var elasticClient ElasticFactory

type ElasticFactory interface {
	Assets() AssetsStore
	TaskAssets() TaskAssetsStore
	DomainAssets() DomainAssetsStore
	TaskDomainAssets() TaskDomainAssetsStore
	Service() ServiceStore
	Subdomain() SubdomainStore
	Client() *elastic.Client
}

// ElasticClient return the store mysqlClient instance.
func ElasticClient() ElasticFactory {
	return elasticClient
}

// SetElasticClient return the store mysqlClient instance.
func SetElasticClient(factory ElasticFactory) {
	elasticClient = factory
}
