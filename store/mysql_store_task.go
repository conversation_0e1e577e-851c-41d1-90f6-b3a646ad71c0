package store

import (
	"context"
	"time"

	"git.gobies.org/foeye/foeye-engine-syncasset/models"
)

type TaskStore interface {
	Create(ctx context.Context, data *models.Task) error
	Count(ctx context.Context) (int, error)
	Delete(ctx context.Context, id int) error
	GetTaskIPRangeTags(ctx context.Context, ids []int) ([]*models.IPRangeReferenceTags, error)
	GetIPRangeIdByTaskId(ctx context.Context, taskId int) ([]int, error)
	CreateBatch(ctx context.Context, data []*models.Task) error
	List(ctx context.Context, conditions ...interface{}) ([]*models.Task, error)
	Detail(ctx context.Context, id int) (*models.Task, error)
	UpdateThreatNumber(ctx context.Context, id int, threatNumber int) error
	UpdateIPCount(ctx context.Context, id int, count int) error
	Update(ctx context.Context, id int, values interface{}) error
	UpdateRealBeginTime(ctx context.Context, id int, time time.Time) error
	UpdateRealBeginEnd(ctx context.Context, id int, time time.Time) error
	UpdateUseSeconds(ctx context.Context, id int, duration time.Duration) error
	UpdateScanStep(ctx context.Context, id int, step models.ScanStep) error
	UpdateState(ctx context.Context, id int, state models.TaskState) error
	CurrentTask(ctx context.Context) (*models.Task, error)
}
