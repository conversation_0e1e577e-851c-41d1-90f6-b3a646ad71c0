package redis

import (
	"sync"
	"time"

	"github.com/go-redis/redis"
)

const (
	StringKeyOfTaskIncrease       = "task_increase"
	StringKeyOfCurrentScanInfo    = "foeye_current_scan_info"
	StringKeyOfQuickTransferState = "task_quick_transfer_state"
)

type task struct {
	client *redis.Client
	mutex  sync.RWMutex
}

func newTask(store *datastore) *task {
	return &task{client: store.client}
}

func (store *task) KeyExists(key string) bool {
	r, err := store.client.Exists(key).Result()
	if err != nil {
		return false
	}

	if int(r) == 1 {
		return true
	}

	return false
}

// SetStringValue  set value into redis memory cache.
func (store *task) SetStringValue(key string, value interface{}, expiration time.Duration) (string, error) {
	return store.client.Set(key, value, expiration).Result()
}

// DeleteKey delete redis key.
func (store *task) DeleteKey(key string) (bool, error) {
	result, err := store.client.Del(key).Result()
	if err != nil {
		return false, err
	}
	return int(result) == 1, err
}

func (store *task) GetTaskQuickTransferStateValue() int {
	got, err := store.client.Get(StringKeyOfQuickTransferState).Int()
	if err != nil {
		return 0
	}

	return got
}

// SetStringTaskIncreaseValue  set value into redis memory cache.
func (store *task) SetStringTaskIncreaseValue() (string, error) {
	return store.SetStringValue(StringKeyOfTaskIncrease, 0, 0)
}

// SetStringCurrentScanInfoValue setting redis string key
func (store *task) SetStringCurrentScanInfoValue() (string, error) {
	return store.SetStringValue(StringKeyOfCurrentScanInfo, "", 0)
}

// DelStringCurrentScanInfoValue delete value into redis memory cache.
func (store *task) DelStringCurrentScanInfoValue() (bool, error) {
	return store.DeleteKey(StringKeyOfCurrentScanInfo)
}

func (store *task) SetHashValue(key string, fields map[string]interface{}) error {
	pipeline := store.client.TxPipeline()
	pipeline.HMSet(key, fields)
	_, err := pipeline.Exec()
	return err
}

// SetHashScanTaskValue SetHashValue set value into redis memory cache.
//func (store *task) SetHashScanTaskValue() error {
//	fields := map[string]interface{}{
//		HashKeyOfScanTaskFieldWithTaskPCT:               0,
//		HashKeyOfScanTaskFieldWithOldProgress:           0,
//		HashKeyOfScanTaskFieldWithTaggingAssetsProgress: 0,
//	}
//
//	err := store.SetHashValue(HashKeyOfScanTask, fields)
//	if err == nil {
//		logger.Infow(fmt.Sprintf("[REDIS]reset %s", HashKeyOfScanTask), "fields", fields)
//	}
//
//	return err
//}

func (store *task) GetHashFieldValue(key string, field string) (string, error) {
	return store.client.HGet(key, field).Result()
}

//func (store *task) GetHashScanTaskFieldValueOfOldProgress() float32 {
//	val, err := store.client.HGet(HashKeyOfScanTask, HashKeyOfScanTaskFieldWithOldProgress).Float32()
//	if err != nil {
//		return 0.00
//	}
//	return val
//}
