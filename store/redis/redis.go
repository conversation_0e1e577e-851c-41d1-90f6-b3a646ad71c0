package redis

import (
	"fmt"
	"sync"

	"git.gobies.org/foeye/foeye-engine-syncasset/store"

	redisconnecter "git.gobies.org/foeye-dependencies/connecter/redis"
	"github.com/go-redis/redis"
)

type (
	Config = *redisconnecter.Config
	Option = redisconnecter.Option
)

var (
	factory                store.RedisFactory
	once                   sync.Once
	NewDefaultSimpleConfig = redisconnecter.NewDefaultSimpleConfig
)

type datastore struct {
	client *redis.Client
}

func (d *datastore) Client() *redis.Client {
	return d.client
}

func (d *datastore) Task() store.TaskRedisStore {
	return newTask(d)
}

// NewRedisClientOr initialize redisclient.Client instance.
func NewRedisClientOr(config Config, ops ...Option) (store.RedisFactory, error) {
	var err error
	once.Do(func() {
		inst, err := redisconnecter.NewConnection(config, ops...)
		if err != nil {
			return
		}
		factory = &datastore{client: inst}
	})
	if err != nil {
		return nil, err
	}

	if factory == nil {
		return nil, fmt.Errorf("initialize redisclient instance to failed")
	}
	return factory, nil
}
