package redis

import (
	"git.gobies.org/foeye-dependencies/mosso"
	"github.com/stretchr/testify/assert"
)

func (suite *RedisSuite) Test_TaskScanInfo_TaskScanInfoKey() {
	key := suite.store.Task().TaskScanInfoKey(10000)
	assert.Equal(suite.T(), `task:scan:info:10000`, key)
}

func (suite *RedisSuite) Test_TaskScanInfo_InitTaskScanInfo() {
	taskId := 10000
	key := suite.store.Task().TaskScanInfoKey(taskId)
	suite.store.Client().Del(key)

	err := suite.store.Task().InitTaskScanInfo(taskId)
	assert.NoError(suite.T(), err)
}

func (suite *RedisSuite) Test_TaskScanInfo_SetTaskScanInfo() {
	taskId := 10000
	key := suite.store.Task().TaskScanInfoKey(taskId)
	suite.store.Client().<PERSON>(key)

	err := suite.store.Task().InitTaskScanInfo(taskId)
	assert.NoError(suite.T(), err)

	err = suite.store.Task().SetTaskScanInfo(taskId, "percent", 52.25)
	assert.NoError(suite.T(), err)

	err = suite.store.Task().SetTaskScanInfoOfPercent(taskId, 63.25)
	assert.NoError(suite.T(), err)
	percent := suite.store.Task().GetTaskScanInfoPercent(taskId)
	assert.Equal(suite.T(), 63.25, percent)

	err = suite.store.Task().SetTaskScanInfoOfProgress(taskId, 50)
	assert.NoError(suite.T(), err)
	progress := suite.store.Task().GetTaskScanInfoProgress(taskId)
	assert.Equal(suite.T(), float64(50), progress)

	err = suite.store.Task().SetTaskScanInfoOfTaggingAssetsProgress(taskId, 100)
	assert.NoError(suite.T(), err)
	tProgress := suite.store.Task().GetTaskScanInfoTaggingAssetsProgress(taskId)
	assert.Equal(suite.T(), float64(100), tProgress)

	info, err := suite.store.Task().TaskScanInfo(taskId)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), info)
	mosso.DebugShowContentWithJSON(info)
}
