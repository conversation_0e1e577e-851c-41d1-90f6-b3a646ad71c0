package redis

import (
	"time"

	"github.com/stretchr/testify/assert"
)

func (suite *RedisSuite) Test_ForbiddenDispatchTimeWindow_Set() {
	suite.store.Client().Del(KeyOfForbiddenDispatchTimeWindow)

	exists := suite.store.Task().ForbiddenDispatchTimeWindowExists()
	assert.False(suite.T(), exists)

	err := suite.store.Task().SetForbiddenDispatchTimeWindow(30 * time.Second)
	assert.NoError(suite.T(), err)

	exists = suite.store.Task().ForbiddenDispatchTimeWindowExists()
	assert.True(suite.T(), exists)
}
