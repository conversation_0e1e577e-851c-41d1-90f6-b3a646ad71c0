package redis

import (
	"encoding/json"
	"fmt"

	"git.gobies.org/foeye-dependencies/jsonfixed"
	"git.gobies.org/foeye/foeye-engine-syncasset/exchange"
)

const (
	HashKeyOfTaskScanInfo                  = "task:scan:info"
	TaskScanInfoKeyOfProgress              = "progress"
	TaskScanInfoKeyOfPercent               = "percent"
	TaskScanInfoKeyOfTaggingAssetsProgress = "tagging_assets_progress"
)

// TaskScanInfoKey 任务ID
// {
//	 "task_id": 1,
//	 "percent": "79.13",  => task_pct
//	 "progress": "66.53", => old_progress
//	 "tagging_assets_progress": "0"
// }
func (store *task) TaskScanInfoKey(taskId int) string {
	return fmt.Sprintf("%s:%d", HashKeyOfTaskScanInfo, taskId)
}

func (store *task) InitTaskScanInfo(taskId int) error {
	key := store.TaskScanInfoKey(taskId)
	return store.SetHashValue(key, exchange.NewTaskScanInfo(taskId).ToMap())
}

func (store *task) SetTaskScanInfo(taskId int, field string, value interface{}) error {
	key := store.TaskScanInfoKey(taskId)
	return store.client.HSet(key, field, value).Err()
}

func (store *task) SetTaskScanInfoOfPercent(taskId int, value interface{}) error {
	key := store.TaskScanInfoKey(taskId)
	return store.client.HSet(key, TaskScanInfoKeyOfPercent, value).Err()
}

func (store *task) SetTaskScanInfoOfProgress(taskId int, value interface{}) error {
	key := store.TaskScanInfoKey(taskId)
	return store.client.HSet(key, TaskScanInfoKeyOfProgress, value).Err()
}

func (store *task) SetTaskScanInfoOfTaggingAssetsProgress(taskId int, value interface{}) error {
	key := store.TaskScanInfoKey(taskId)
	return store.client.HSet(key, TaskScanInfoKeyOfTaggingAssetsProgress, value).Err()
}

func (store *task) TaskScanInfo(taskId int) (*exchange.TaskScanInfo, error) {
	key := store.TaskScanInfoKey(taskId)
	res, err := store.client.HGetAll(key).Result()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(&res)
	if err != nil {
		return nil, err
	}

	convert, err := store.marshal(err, data)
	if err != nil {
		return nil, err
	}

	var r *exchange.TaskScanInfo
	err = json.Unmarshal(convert, &r)
	if err != nil {
		return nil, err
	}

	return r, nil
}

func (store *task) GetTaskScanInfoProgress(taskId int) float64 {
	return store.GetTaskScanInfoOfField(taskId, TaskScanInfoKeyOfProgress)
}

func (store *task) GetTaskScanInfoPercent(taskId int) float64 {
	return store.GetTaskScanInfoOfField(taskId, TaskScanInfoKeyOfPercent)
}

func (store *task) GetTaskScanInfoTaggingAssetsProgress(taskId int) float64 {
	return store.GetTaskScanInfoOfField(taskId, TaskScanInfoKeyOfTaggingAssetsProgress)
}

func (store *task) GetTaskScanInfoOfField(taskId int, field string) float64 {
	key := store.TaskScanInfoKey(taskId)
	get := store.client.HGet(key, field)
	if err := get.Err(); err != nil {
		return 0
	}

	f, err := get.Float64()
	if err != nil {
		return 0
	}

	return f
}

func (store *task) marshal(err error, data []byte) ([]byte, error) {
	floater := jsonfixed.NewDestinationOfFloat(jsonfixed.FloatTypeOfFloat64)
	integer := jsonfixed.NewDestinationOfInteger(jsonfixed.IntegerTypeOfInt)
	tmp := jsonfixed.Template{
		"task_id":                 integer,
		"percent":                 floater,
		"progress":                floater,
		"tagging_assets_progress": floater,
	}

	return jsonfixed.Convert(data, tmp)
}
