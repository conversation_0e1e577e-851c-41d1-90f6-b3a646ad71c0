package redis

import "fmt"

const (
	KeyOfTaskVulnerability = "task_vulnerability"
)

var (
	ErrDeleteKeyFailed = fmt.<PERSON><PERSON><PERSON>("delete key failure")
)

func (store *task) GetTaskVulnerabilityKeyWithTaskId(taskId int) string {
	return fmt.Sprintf("%s_%d", KeyOfTaskVulnerability, taskId)
}

func (store *task) DelTaskVulnerabilityKeyWithTaskId(taskId int) bool {
	keys := store.GetTaskVulnerabilityKeyWithTaskId(taskId)

	result, err := store.client.Del(keys).Result()
	if err != nil {
		return false
	}

	if result == 1 {
		return true
	}

	return false
}

func (store *task) SetHashValueOfTaskVulnerability(taskId int, fields map[string]interface{}) error {
	key := store.GetTaskVulnerabilityKeyWithTaskId(taskId)
	return store.SetHashValue(key, fields)
}

func (store *task) SetForceUpdateProgress(taskId int, progress float64) (bool, error) {
	store.mutex.Lock()
	defer store.mutex.Unlock()

	key := store.GetTaskVulnerabilityKeyWithTaskId(taskId)
	if !store.KeyExists(key) {
		fields := map[string]interface{}{"status": "pending"}
		err := store.SetHashValueOfTaskVulnerability(taskId, fields)
		if err != nil {
			return false, err
		}
	}

	result, err := store.client.HSet(key, "force_progress", progress).Result()
	if err != nil {
		return false, err
	}

	return result, nil
}
