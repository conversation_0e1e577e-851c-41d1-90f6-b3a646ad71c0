package redis

import (
	"fmt"
	"time"
)

const (
	KeyOfForbiddenDispatchTimeWindow = "task:forbidden:dispatch:time:window"
)

func (store *task) SetForbiddenDispatchTimeWindow(duration time.Duration) error {
	ok, err := store.SetStringValue(KeyOfForbiddenDispatchTimeWindow, true, duration)
	if err != nil {
		return err
	}

	if ok == `OK` {
		return nil
	}

	return fmt.Errorf("set forbidden dispatch time window")
}

func (store *task) ForbiddenDispatchTimeWindowExists() bool {
	return store.KeyExists(KeyOfForbiddenDispatchTimeWindow)
}
