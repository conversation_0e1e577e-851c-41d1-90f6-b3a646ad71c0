package redis

import "github.com/stretchr/testify/assert"

func (suite *RedisSuite) Test_SetSingleIPAddressKey() {
	key := suite.store.Task().GetSingleIPAddressKey(1)
	suite.store.Task().Delete<PERSON>ey(key)

	err := suite.store.Task().SetSingleIPAddressKey(1, true)
	assert.NoError(suite.T(), err)

	addressKey, err := suite.store.Task().DeleteSingleIPAddressKey(1)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), addressKey)

	addressKey, err = suite.store.Task().DeleteSingleIPAddressKey(2)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), addressKey)
}

func (suite *RedisSuite) Test_SingleIPAddressKeyExists() {
	addressKey, err := suite.store.Task().DeleteSingleIPAddressKey(1000)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), addressKey)

	exists := suite.store.Task().SingleIPAddressKeyExists(1000)
	assert.False(suite.T(), exists)

	err = suite.store.Task().SetSingleIPAddressKey(2000, true)
	assert.NoError(suite.T(), err)
	exists = suite.store.Task().SingleIPAddressKeyExists(2000)
	assert.True(suite.T(), exists)
}
