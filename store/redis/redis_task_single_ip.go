package redis

import "fmt"

const (
	KeyOfSingleIPAddress = "task_single_ip_address"
)

func (store *task) GetSingleIPAddress<PERSON>ey(taskId int) string {
	return fmt.Sprintf("%s_%d", KeyOfSingleIPAddress, taskId)
}

func (store *task) DeleteSingleIPAddress<PERSON>ey(taskId int) (bool, error) {
	key := store.GetSingleIPAddressKey(taskId)

	return store.DeleteKey(key)
}

func (store *task) SetSingleIPAddressKey(taskId int, isSingle bool) error {
	key := store.GetSingleIPAddressKey(taskId)

	value, err := store.SetStringValue(key, isSingle, 0)
	if err != nil {
		return err
	}

	if value == `OK` {
		return nil
	}

	return fmt.Errorf("key set failed")
}

func (store *task) SingleIPAddressKeyExists(taskId int) bool {
	return store.KeyExists(store.GetSingleIP<PERSON><PERSON><PERSON><PERSON><PERSON>(taskId))
}
