package redis

import (
	"github.com/stretchr/testify/assert"
)

func (suite *RedisSuite) Test_SetStringValue() {
	actual, err := suite.store.Task().SetStringValue("hello", "world", 0)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), `OK`, actual)
}

func (suite *RedisSuite) Test_Task_SetStringTaskIncreaseValue() {
	actual, err := suite.store.Task().SetStringTaskIncreaseValue()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), `OK`, actual)
}

func (suite *RedisSuite) Test_Task_SetStringCurrentScanInfoValue() {
	actual, err := suite.store.Task().SetStringCurrentScanInfoValue()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), `OK`, actual)
}

//func (suite *RedisSuite) Test_Task_SetHashValue() {
//	err := suite.store.Task().SetHashScanTaskValue()
//	assert.NoError(suite.T(), err)
//}
