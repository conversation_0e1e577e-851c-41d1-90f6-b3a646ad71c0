package redis

import (
	"github.com/stretchr/testify/assert"
)

func (suite *RedisSuite) Test_GetTaskVulnerabilityKeyWithTaskId() {
	actual := suite.store.Task().GetTaskVulnerabilityKeyWithTaskId(1)
	assert.Equal(suite.T(), "task_vulnerability_1", actual)
}

func (suite *RedisSuite) Test_DelTaskVulnerabilityKeyWithTaskId() {
	key := suite.store.Task().GetTaskVulnerabilityKeyWithTaskId(1)
	suite.store.Client().Del(key)

	actual := suite.store.Task().DelTaskVulnerabilityKeyWithTaskId(1)
	assert.Equal(suite.T(), false, actual)

	err := suite.store.Task().SetHashValueOfTaskVulnerability(1,
		map[string]interface{}{"single_ip": true})
	assert.NoError(suite.T(), err)

	actual = suite.store.Task().DelTaskVulnerabilityKeyWithTaskId(1)
	assert.Equal(suite.T(), true, actual)
}
