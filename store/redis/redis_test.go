package redis

import (
	"testing"

	"git.gobies.org/foeye/foeye-engine-syncasset/config"
	"git.gobies.org/foeye/foeye-engine-syncasset/store"

	"git.gobies.org/foeye-dependencies/configure"
	redisconnecter "git.gobies.org/foeye-dependencies/connecter/redis"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestRedisSuite(t *testing.T) {
	suite.Run(t, &RedisSuite{})
}

type RedisSuite struct {
	suite.Suite
	config      *config.Configure
	redisconfig *redisconnecter.Config
	store       store.RedisFactory
}

func (suite *RedisSuite) BeforeTest(suiteName, testName string) {
	ops := []configure.Option{
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("conf.test"),
	}

	suite.config = config.GetConfigure(ops...)
	assert.NotNil(suite.T(), suite.config)

	suite.redisconfig = NewDefaultSimpleConfig(
		suite.config.Redis.Host,
		suite.config.Redis.Password,
		suite.config.Redis.Database,
	)

	var err error
	suite.store, err = NewRedisClientOr(suite.redisconfig)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), suite.store)
}

func (suite *RedisSuite) Test_NewRedisClientOr() {
	or, err := NewRedisClientOr(suite.redisconfig)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), or)
}
