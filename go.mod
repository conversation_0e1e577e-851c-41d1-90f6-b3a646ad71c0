module git.gobies.org/foeye/foeye-engine-syncasset

go 1.18

require (
	git.gobies.org/foeye-dependencies/address v0.2.6
	git.gobies.org/foeye-dependencies/cidr v1.4.2
	git.gobies.org/foeye-dependencies/configure v1.1.0
	git.gobies.org/foeye-dependencies/connecter v0.3.18
	git.gobies.org/foeye-dependencies/embedsfs v1.2.0
	git.gobies.org/foeye-dependencies/expression v0.3.0
	git.gobies.org/foeye-dependencies/fireness v1.5.2
	git.gobies.org/foeye-dependencies/fsfire v1.2.0
	git.gobies.org/foeye-dependencies/httpclient v0.4.0
	git.gobies.org/foeye-dependencies/interside v1.3.0
	git.gobies.org/foeye-dependencies/jsonfixed v1.2.0
	git.gobies.org/foeye-dependencies/logger v0.3.0
	git.gobies.org/foeye-dependencies/mosso v1.2.0
	git.gobies.org/foeye-dependencies/sidekip v0.2.0
	git.gobies.org/foeye-dependencies/version v1.2.0
	github.com/agiledragon/gomonkey/v2 v2.11.0
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/jrallison/go-workers v0.0.0-20180112190529-dbf81d0b75bb
	github.com/olivere/elastic v6.2.37+incompatible
	github.com/pkg/errors v0.9.1
	github.com/prataprc/goparsec v0.0.0-20210210111032-e54bde2749dd
	github.com/spf13/pflag v1.0.5
	github.com/stretchr/testify v1.8.1
	github.com/tdewolff/minify/v2 v2.9.22
	github.com/thoas/go-funk v0.9.3
	gorm.io/gorm v1.23.8
)

require (
	git.gobies.org/foeye-xc/go-aci v0.0.4 // indirect
	git.gobies.org/foeye/kingbase-driver v0.0.20 // indirect
	git.gobies.org/foeye/shentong v0.0.20 // indirect
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fsnotify/fsnotify v1.5.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.0 // indirect
	github.com/garyburd/redigo v1.6.3 // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/google/go-cmp v0.5.9 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.14.0 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.3.2 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgtype v1.14.0 // indirect
	github.com/jackc/pgx/v4 v4.18.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/magiconair/properties v1.8.5 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mitchellh/mapstructure v1.4.1 // indirect
	github.com/onsi/ginkgo v1.16.5 // indirect
	github.com/onsi/gomega v1.23.0 // indirect
	github.com/pelletier/go-toml v1.9.3 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/spf13/afero v1.6.0 // indirect
	github.com/spf13/cast v1.3.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/viper v1.8.1 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/tdewolff/parse/v2 v2.5.21 // indirect
	go.uber.org/atomic v1.10.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	go.uber.org/zap v1.19.1 // indirect
	golang.org/x/crypto v0.6.0 // indirect
	golang.org/x/net v0.6.0 // indirect
	golang.org/x/sys v0.5.0 // indirect
	golang.org/x/text v0.7.0 // indirect
	gopkg.in/ini.v1 v1.62.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.3.6 // indirect
)
